/*
 * Example demonstrating the Sweep feature implementation
 * This file shows how to use the new sweep functionality
 */

#include "MyCad/GeometryOperations.h"
#include <BRepBuilderAPI_MakeWire.hxx>
#include <BRepBuilderAPI_MakeEdge.hxx>
#include <BRepBuilderAPI_MakeFace.hxx>
#include <gp_Pnt.hxx>
#include <gp_Circ.hxx>
#include <gp_Ax2.hxx>
#include <gp_Dir.hxx>
#include <TColgp_Array1OfPnt.hxx>
#include <GeomAPI_PointsToBSpline.hxx>
#include <BRepBuilderAPI_MakeEdge.hxx>

// Example 1: Create a circular profile and sweep it along a straight path
TopoDS_Shape CreateCircularSweepExample() {
    try {
        // Create circular profile (2D closed wire)
        gp_Ax2 profileAxis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
        gp_Circ circle(profileAxis, 10.0); // radius = 10
        
        TopoDS_Edge circleEdge = BRepBuilderAPI_MakeEdge(circle);
        TopoDS_Wire profileWire = BRepBuilderAPI_MakeWire(circleEdge);
        
        // Create straight path (3D wire)
        gp_Pnt startPnt(0, 0, 0);
        gp_Pnt endPnt(0, 0, 100); // sweep along Z-axis
        TopoDS_Edge pathEdge = BRepBuilderAPI_MakeEdge(startPnt, endPnt);
        TopoDS_Wire pathWire = BRepBuilderAPI_MakeWire(pathEdge);
        
        // Perform sweep
        return MyCadGeom::SweepProfileAlongPath(profileWire, pathWire);
        
    } catch (const Standard_Failure& e) {
        // Handle error
        return TopoDS_Shape();
    }
}

// Example 2: Create a rectangular profile and sweep it along a curved path
TopoDS_Shape CreateRectangularSweepExample() {
    try {
        // Create rectangular profile
        gp_Pnt p1(-5, -3, 0);
        gp_Pnt p2(5, -3, 0);
        gp_Pnt p3(5, 3, 0);
        gp_Pnt p4(-5, 3, 0);
        
        TopoDS_Edge e1 = BRepBuilderAPI_MakeEdge(p1, p2);
        TopoDS_Edge e2 = BRepBuilderAPI_MakeEdge(p2, p3);
        TopoDS_Edge e3 = BRepBuilderAPI_MakeEdge(p3, p4);
        TopoDS_Edge e4 = BRepBuilderAPI_MakeEdge(p4, p1);
        
        BRepBuilderAPI_MakeWire wireBuilder;
        wireBuilder.Add(e1);
        wireBuilder.Add(e2);
        wireBuilder.Add(e3);
        wireBuilder.Add(e4);
        TopoDS_Wire profileWire = wireBuilder.Wire();
        
        // Create curved path using B-spline
        TColgp_Array1OfPnt pathPoints(1, 5);
        pathPoints(1) = gp_Pnt(0, 0, 0);
        pathPoints(2) = gp_Pnt(20, 0, 20);
        pathPoints(3) = gp_Pnt(40, 20, 40);
        pathPoints(4) = gp_Pnt(60, 40, 60);
        pathPoints(5) = gp_Pnt(80, 60, 80);
        
        Handle(Geom_BSplineCurve) bspline = GeomAPI_PointsToBSpline(pathPoints);
        TopoDS_Edge pathEdge = BRepBuilderAPI_MakeEdge(bspline);
        
        // Perform sweep
        return MyCadGeom::SweepProfileAlongPath(profileWire, pathEdge);
        
    } catch (const Standard_Failure& e) {
        // Handle error
        return TopoDS_Shape();
    }
}

// Example 3: Validate shapes before sweeping
bool ValidateShapesExample() {
    // Create a simple wire
    gp_Pnt p1(0, 0, 0);
    gp_Pnt p2(10, 0, 0);
    gp_Pnt p3(10, 10, 0);
    gp_Pnt p4(0, 10, 0);
    
    TopoDS_Edge e1 = BRepBuilderAPI_MakeEdge(p1, p2);
    TopoDS_Edge e2 = BRepBuilderAPI_MakeEdge(p2, p3);
    TopoDS_Edge e3 = BRepBuilderAPI_MakeEdge(p3, p4);
    TopoDS_Edge e4 = BRepBuilderAPI_MakeEdge(p4, p1);
    
    BRepBuilderAPI_MakeWire wireBuilder;
    wireBuilder.Add(e1);
    wireBuilder.Add(e2);
    wireBuilder.Add(e3);
    wireBuilder.Add(e4);
    TopoDS_Wire testWire = wireBuilder.Wire();
    
    // Validate profile
    bool isValidProfile = MyCadGeom::ValidateProfileForSweep(testWire);
    
    // Create path
    gp_Pnt pathStart(0, 0, 0);
    gp_Pnt pathEnd(0, 0, 50);
    TopoDS_Edge pathEdge = BRepBuilderAPI_MakeEdge(pathStart, pathEnd);
    
    // Validate path
    bool isValidPath = MyCadGeom::ValidatePathForSweep(pathEdge);
    
    return isValidProfile && isValidPath;
}

/*
 * Usage Instructions:
 * 
 * 1. Start the CAD application
 * 2. Create or import a 2D profile shape (closed wire or face)
 * 3. Create or import a 3D path (edge or wire)
 * 4. Click the "Sweep" button
 * 5. First, select the profile shape
 * 6. Then, select the path shape
 * 7. The sweep operation will be performed automatically
 * 
 * Requirements:
 * - Profile must be a closed wire or face
 * - Path must be a continuous edge or wire
 * - Profile and path should not intersect in problematic ways
 * 
 * Error Handling:
 * - The system validates inputs before performing the sweep
 * - Clear error messages are displayed for invalid inputs
 * - The operation uses IsDone() to check for successful completion
 * 
 * Features:
 * - Two-step selection process for better user experience
 * - Comprehensive input validation
 * - Integration with undo/redo system
 * - Professional error handling and user feedback
 * - Compatible with FreeCAD-style sweep operations
 */
