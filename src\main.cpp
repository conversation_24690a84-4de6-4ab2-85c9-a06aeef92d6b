#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include "MyCad/MainWindow.h"
#include "MyCad/AppCore.h"

namespace MyCad {
    AppCore g_appCore;
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Validate hInstance
    if (hInstance == NULL) {
        MessageBoxW(NULL, L"ERROR: hInstance is NULL in WinMain!", L"Invalid hInstance", MB_OK | MB_ICONERROR);
        return 1;
    }

    MyCad::MainWindow mainWindow(hInstance);

    if (!mainWindow.Create(L"My OCCT CAD Application", nCmdShow, 800, 600)) {
        return 1;
    }

    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}