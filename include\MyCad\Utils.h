#pragma once

#include <string>
#include <windows.h>
#include <algorithm> // For std::transform
#include <TopAbs_ShapeEnum.hxx>

namespace MyCadUtils {

std::string WideCharToStdString(LPCWSTR wstr);
std::wstring StringToWString(const std::string& str);
std::string StringToLower(const std::string& s);
std::string GetFileExtension(const std::string& filePath);
std::string GetFilePathFromDialog(HWND hwndOwner, const wchar_t* filter, const wchar_t* defaultExt = L"");
std::string GetSaveFilePathFromDialog(HWND hwndOwner, const wchar_t* filter, const wchar_t* defaultExt = L"stp");
std::wstring ShapeTypeToString(TopAbs_ShapeEnum shapeType);

} // namespace MyCadUtils