#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <windowsx.h>
#include <commdlg.h>
#include <string>
#include <stdexcept>
#include <algorithm>
#include <vector>
#include <cmath>
#include <set>
#include <memory>
#include <functional>
#include <limits>

#include "MyCad/AppCore.h"
#include "MyCad/GeometryOperations.h"
#include "MyCad/Utils.h"
#include "MyCad/Constants.h"
#include "MyCad/UndoRedoSystem.h"
#include "MyCad/ModelTreeManager.h"
#include "resource.h"

#include <Prs3d_ShadingAspect.hxx>
#include <Prs3d_Drawer.hxx>
#include <Prs3d_LineAspect.hxx>
#include <Aspect_TypeOfLine.hxx>
#include <Quantity_NameOfColor.hxx>
#include <V3d_TypeOfOrientation.hxx>
#include <BRepBndLib.hxx>
#include <Bnd_Box.hxx>
#include <TopExp_Explorer.hxx>
#include <TopoDS.hxx>
#include <SelectMgr_EntityOwner.hxx>
#include <StdSelect_BRepOwner.hxx>
#include <SelectMgr_SelectableObject.hxx>
#include <AIS_ListOfInteractive.hxx>
#include <AIS_ListIteratorOfListOfInteractive.hxx>
#include <gp_Pnt.hxx>
#include <gp_Ax2.hxx>
#include <gp_Dir.hxx>
#include <gp_Vec.hxx>
#include <Standard_Failure.hxx>
#include <TopTools_ListOfShape.hxx>
#include <TopTools_ListIteratorOfListOfShape.hxx>
#include <AIS_Shape.hxx>
#include <AIS_InteractiveContext.hxx>
#include <V3d_Viewer.hxx>
#include <V3d_View.hxx>
#include <WNT_Window.hxx>
#include <Aspect_DisplayConnection.hxx>
#include <OpenGl_GraphicDriver.hxx>
#include <BRepPrimAPI_MakePrism.hxx>
#include <BRepAlgoAPI_Fuse.hxx>
#include <BRepBuilderAPI_MakeWire.hxx>
#include <BRepBuilderAPI_MakeEdge.hxx>
#include <BRepBuilderAPI_MakeVertex.hxx>
#include <BRepExtrema_DistShapeShape.hxx>
#include <Precision.hxx>
#include <TopAbs.hxx>

// Comparator classes from your reference code for removing duplicates
struct ComparePoints
{
    bool operator()(const gp_Pnt &p1, const gp_Pnt &p2) const
    {
        if (p1.X() < p2.X())
            return true;
        if (p1.X() > p2.X())
            return false;
        if (p1.Y() < p2.Y())
            return true;
        if (p1.Y() > p2.Y())
            return false;
        return p1.Z() < p2.Z();
    }
};

struct CompareEdge
{
    bool operator()(const std::pair<gp_Pnt, gp_Pnt> &edge1, const std::pair<gp_Pnt, gp_Pnt> &edge2) const
    {
        // Compare first by start point
        if (edge1.first.X() < edge2.first.X())
            return true;
        if (edge1.first.X() > edge2.first.X())
            return false;
        if (edge1.first.Y() < edge2.first.Y())
            return true;
        if (edge1.first.Y() > edge2.first.Y())
            return false;
        if (edge1.first.Z() < edge2.first.Z())
            return true;
        if (edge1.first.Z() > edge2.first.Z())
            return false;

        // If start points are the same, compare by end point
        if (edge1.second.X() < edge2.second.X())
            return true;
        if (edge1.second.X() > edge2.second.X())
            return false;
        if (edge1.second.Y() < edge2.second.Y())
            return true;
        if (edge1.second.Y() > edge2.second.Y())
            return false;
        return edge1.second.Z() < edge2.second.Z();
    }
};

namespace MyCad
{

    int SceneObject::nextId = 0;
    const double AppCore::PLACEMENT_SPACING = 150.0;

    // Forward declaration of dialog procedures
    INT_PTR CALLBACK DlgProcRevolveParams(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);

    INT_PTR CALLBACK DlgProcThicknessLocal(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
    {
        static double *pThickness = nullptr;
        switch (message)
        {
        case WM_INITDIALOG:
            pThickness = (double *)lParam;
            if (pThickness)
            {
                std::wstring initialThicknessStr = std::to_wstring(*pThickness);
                SetDlgItemTextW(hDlg, IDC_EDIT_THICKNESS, initialThicknessStr.c_str());
            }
            SetFocus(GetDlgItem(hDlg, IDC_EDIT_THICKNESS));
            SendDlgItemMessage(hDlg, IDC_EDIT_THICKNESS, EM_SETSEL, 0, -1);
            return (INT_PTR)FALSE;
        case WM_COMMAND:
            switch (LOWORD(wParam))
            {
            case IDOK:
                if (pThickness)
                {
                    wchar_t buffer[100];
                    GetDlgItemTextW(hDlg, IDC_EDIT_THICKNESS, buffer, 100);
                    try
                    {
                        std::wstring wstr(buffer);
                        if (wstr.empty())
                        {
                            MessageBoxW(hDlg, L"Thickness value cannot be empty.", L"Input Error", MB_OK | MB_ICONERROR);
                            SetFocus(GetDlgItem(hDlg, IDC_EDIT_THICKNESS));
                            SendDlgItemMessage(hDlg, IDC_EDIT_THICKNESS, EM_SETSEL, 0, -1);
                            return (INT_PTR)TRUE;
                        }
                        *pThickness = std::stod(wstr);
                        EndDialog(hDlg, IDOK);
                    }
                    catch (const std::invalid_argument &)
                    {
                        MessageBoxW(hDlg, L"Invalid number format.", L"Input Error", MB_OK | MB_ICONERROR);
                        SetFocus(GetDlgItem(hDlg, IDC_EDIT_THICKNESS));
                        SendDlgItemMessage(hDlg, IDC_EDIT_THICKNESS, EM_SETSEL, 0, -1);
                        return (INT_PTR)TRUE;
                    }
                    catch (const std::out_of_range &)
                    {
                        MessageBoxW(hDlg, L"Number is out of range.", L"Input Error", MB_OK | MB_ICONERROR);
                        SetFocus(GetDlgItem(hDlg, IDC_EDIT_THICKNESS));
                        SendDlgItemMessage(hDlg, IDC_EDIT_THICKNESS, EM_SETSEL, 0, -1);
                        return (INT_PTR)TRUE;
                    }
                }
                else
                {
                    EndDialog(hDlg, IDOK);
                }
                return (INT_PTR)TRUE;
            case IDCANCEL:
                EndDialog(hDlg, IDCANCEL);
                return (INT_PTR)TRUE;
            }
            break;
        }
        return (INT_PTR)FALSE;
    }

    AppCore::AppCore()
        : m_graphicDriver(nullptr),
          m_displayConnection(nullptr),
          m_viewer(nullptr),
          m_view(nullptr),
          m_wntWindow(nullptr),
          m_context(nullptr),
          m_selection1_IO(nullptr),
          m_selection2_IO(nullptr),
          m_parentShapeOfSelectedFaces(nullptr),
          m_isPanning(Standard_False), m_lastMouseX_occ(0), m_lastMouseY_occ(0),
          m_hMainWnd(nullptr), m_hOccChildWnd(nullptr), m_buttonAreaHeight(0),
          m_currentPlacementXOffset(0.0),
          m_isInSplitMode(false), m_isDrawingSplitLine(false), m_isDraggingSplitLine(false),
          m_hasSplitStartPoint(false), m_hasSplitEndPoint(false),
          m_dragStartX(0), m_dragStartY(0), m_dragCurrentX(0), m_dragCurrentY(0),
          m_useRubberBandLine(true), // Enable by default
          m_currentTrimMode(TRIM_MODE_NORMAL), m_isInTrimMode(false),
          m_showTrimPreview(false),
          m_isInSweepSelectionMode(false), m_hasSweepProfile(false), m_hasSweepPath(false),
          m_hMirrorDialog(nullptr), m_isSelectingMirrorFace(false),
          m_isInFillSelectionMode(false), m_fillDegree(3), m_fillMaxSegments(9),
          m_fillUseG1Continuity(true), m_fillUseG2Continuity(false)
    {
        SceneObject::nextId = 0;
        m_selectedFacesList.Clear();
        m_undoRedoManager = std::make_unique<UndoRedoManager>(50); // 50 command history
        m_modelTreeManager = std::make_unique<ModelTreeManager>();
    }

    AppCore::~AppCore()
    {
    }

    void AppCore::SetWindowHandles(HWND hMain, HWND hOccChild, int buttonAreaTotalHeight)
    {
        m_hMainWnd = hMain;
        m_hOccChildWnd = hOccChild;
        m_buttonAreaHeight = buttonAreaTotalHeight;
    }

    bool AppCore::InitializeGraphics(HWND hOccWnd)
    {
        m_hOccChildWnd = hOccWnd;
        try
        {
            m_displayConnection = new Aspect_DisplayConnection();
            m_graphicDriver = new OpenGl_GraphicDriver(m_displayConnection);
        }
        catch (const Standard_Failure &e)
        {
            MessageBoxA(NULL, e.GetMessageString(), "Graphics Driver Init Failed", MB_OK | MB_ICONERROR);
            return false;
        }

        m_viewer = new V3d_Viewer(m_graphicDriver);
        m_viewer->SetDefaultLights();
        m_viewer->SetLightOn();

        m_view = m_viewer->CreateView();
        m_view->SetBackgroundColor(Quantity_NameOfColor::Quantity_NOC_GRAY30);
        m_view->TriedronDisplay(Aspect_TOTP_LEFT_LOWER, Quantity_NameOfColor::Quantity_NOC_WHITE, 0.08, V3d_ZBUFFER);
        m_view->SetImmediateUpdate(Standard_False);

        m_wntWindow = new WNT_Window(hOccWnd);
        m_view->SetWindow(m_wntWindow);
        if (!m_wntWindow->IsMapped())
        {
            m_wntWindow->Map();
        }
        FitAll();
        return true;
    }

    void AppCore::InitializeContextStyles()
    {
        if (m_viewer.IsNull())
            return;

        m_context = new AIS_InteractiveContext(m_viewer);
        m_context->SetDisplayMode(AIS_Shaded, Standard_True);

        // Setup highlight style
        m_context->HighlightStyle()->SetColor(Quantity_NameOfColor::Quantity_NOC_CYAN1);
        m_context->HighlightStyle()->SetDisplayMode(AIS_Shaded);
        m_context->HighlightStyle()->SetTransparency(0.3);

        // Setup selection style
        m_context->SelectionStyle()->SetColor(Quantity_NameOfColor::Quantity_NOC_GREENYELLOW);
        m_context->SelectionStyle()->SetDisplayMode(AIS_Shaded);
        m_context->SelectionStyle()->SetTransparency(0.1);

        // Enable face boundaries for better edge visibility
        m_context->DefaultDrawer()->SetFaceBoundaryDraw(Standard_True);

        // Configure selection sensitivity
        m_context->SetPixelTolerance(5); // Increased from 3 for better edge picking
        m_context->SetAutoActivateSelection(Standard_True);

        // For edge highlighting, we need to ensure edges are detectable
        // This will be handled when objects are added to the scene
    }
    void AppCore::AddObjectToScene(const TopoDS_Shape &shape, const Quantity_Color &color, const std::string &name, bool fitView)
    {
        // Use command system for undo/redo support
        auto command = std::make_unique<AddObjectCommand>(this, shape, color, name, fitView);
        ExecuteCommand(std::move(command));
    }

    void AppCore::RemoveObjectFromScene(int objectId)
    {
        auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                               [objectId](const SceneObject &obj)
                               { return obj.id == objectId; });
        if (it != m_sceneObjects.end())
        {
            Handle(AIS_InteractiveObject) aisObjectHandle = it->aisObject;

            if (!aisObjectHandle.IsNull() && !m_context.IsNull())
            {
                m_context->Erase(aisObjectHandle, Standard_False);
            }

            if (m_selection1_IO == aisObjectHandle)
                m_selection1_IO.Nullify();
            if (m_selection2_IO == aisObjectHandle)
                m_selection2_IO.Nullify();
            if (m_parentShapeOfSelectedFaces == aisObjectHandle)
            {
                m_selectedFacesList.Clear();
                m_parentShapeOfSelectedFaces.Nullify();
                if (!m_context.IsNull())
                    m_context->ClearSelected(Standard_False);
            }

            bool currentShapeWasRemoved = false;
            if (!m_currentShapeToExport.IsNull() && aisObjectHandle->IsKind(STANDARD_TYPE(AIS_Shape)))
            {
                Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(aisObjectHandle);
                if (!aisShape.IsNull() && !aisShape->Shape().IsNull())
                {
                    if (m_currentShapeToExport.IsSame(aisShape->Shape()))
                    {
                        currentShapeWasRemoved = true;
                    }
                }
            }

            // Remove from model tree before erasing from internal list
            if (m_modelTreeManager)
            {
                m_modelTreeManager->RemoveObject(it->id);
            }

            m_sceneObjects.erase(it);

            if (currentShapeWasRemoved)
            {
                if (!m_sceneObjects.empty())
                {
                    UpdateCurrentShapeForExport(m_sceneObjects.back().shape);
                }
                else
                {
                    m_currentShapeToExport.Nullify();
                }
            }
            RedrawView();
            FitAll();
        }
    }

    void AppCore::UpdateCurrentShapeForExport(const TopoDS_Shape &shape)
    {
        m_currentShapeToExport = shape;
    }

    void AppCore::SetupAisObjectDisplay(const Handle(AIS_Shape) & aisShape,
                                        const Quantity_Color &mainColor,
                                        const Quantity_Color &edgeColor,
                                        Standard_Real edgeWidth)
    {
        if (aisShape.IsNull())
            return;
        aisShape->SetColor(mainColor);
        Handle(Prs3d_Drawer) drawer = aisShape->Attributes();
        if (drawer.IsNull())
        {
            drawer = new Prs3d_Drawer();
            aisShape->SetAttributes(drawer);
        }
        if (drawer->ShadingAspect().IsNull())
        {
            drawer->SetShadingAspect(new Prs3d_ShadingAspect());
        }
        drawer->ShadingAspect()->SetColor(mainColor);
        drawer->SetFaceBoundaryDraw(Standard_True); // Ensure face boundaries (edges) are drawn

        Handle(Prs3d_LineAspect) lineAspect = drawer->LineAspect();
        if (lineAspect.IsNull())
        {
            lineAspect = new Prs3d_LineAspect(edgeColor, Aspect_TOL_SOLID, edgeWidth);
            drawer->SetLineAspect(lineAspect);
        }
        else
        {
            lineAspect->SetColor(edgeColor);
            lineAspect->SetTypeOfLine(Aspect_TOL_SOLID);
            lineAspect->SetWidth(edgeWidth);
        }
        aisShape->SetDisplayMode(AIS_Shaded); // Set to shaded mode (0)
        // Note: Individual selection modes (for faces, edges, etc.) are handled during m_context->Activate()
    }

    void AppCore::HandleImportShape()
    {
        if (!m_hMainWnd)
            return;
        std::string filePath = MyCadUtils::GetFilePathFromDialog(m_hMainWnd, L"STEP/IGES Files (*.stp;*.step;*.igs;*.iges)\0*.stp;*.step;*.igs;*.iges\0All Files (*.*)\0*.*\0");
        if (!filePath.empty())
        {
            TopoDS_Shape importedShape = MyCadGeom::ImportStepIgesFile(m_hMainWnd, filePath);
            if (!importedShape.IsNull())
            {
                AddObjectToScene(importedShape, Quantity_NameOfColor::Quantity_NOC_LIGHTSEAGREEN, MyCadUtils::GetFileExtension(filePath), true);
            }
        }
    }

    void AppCore::HandleExportCurrentShape()
    {
        if (m_currentShapeToExport.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"No current shape available to export.", L"Export Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        std::string filePath = MyCadUtils::GetSaveFilePathFromDialog(m_hMainWnd, L"STEP Files (*.stp;*.step)\0*.stp;*.step\0IGES Files (*.igs;*.iges)\0*.igs;*.iges\0All Files (*.*)\0*.*\0", L"stp");
        if (!filePath.empty())
        {
            MyCadGeom::ExportStepIgesFile(m_hMainWnd, m_currentShapeToExport, filePath);
        }
    }

    void AppCore::HandleCreateCube()
    {
        TopoDS_Shape cubeShape = MyCadGeom::CreateCube();
        if (!cubeShape.IsNull())
        {
            AddObjectToScene(cubeShape, Quantity_NameOfColor::Quantity_NOC_BLUE1, "Cube", true);
        }
    }

    void AppCore::HandleCreateCylinder()
    {
        gp_Ax2 axis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
        TopoDS_Shape cylShape = MyCadGeom::CreateCylinder(axis);
        if (!cylShape.IsNull())
        {
            AddObjectToScene(cylShape, Quantity_NameOfColor::Quantity_NOC_RED1, "Cylinder", true);
        }
    }

    void AppCore::HandleCutOperation()
    {
        size_t selectedCount = GetSelectedObjectCount();

        if (selectedCount < 2)
        {
            MessageBoxW(m_hMainWnd, L"Please select at least two shapes for Cut operation (Ctrl+Click).", L"Cut Error", MB_OK | MB_ICONERROR);
            return;
        }

        const auto &selectedObjects = GetSelectedObjects();

        // Validate that all selected objects are shapes
        std::vector<Handle(AIS_Shape)> aisShapes;
        for (const auto &obj : selectedObjects)
        {
            Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(obj);
            if (aisShape.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"One or more selected items are not valid shapes for Cut.", L"Cut Error", MB_OK | MB_ICONERROR);
                return;
            }
            aisShapes.push_back(aisShape);
        }

        // For multiple objects: use first as base, cut with all others
        TopoDS_Shape resultShape = aisShapes[0]->Shape();

        for (size_t i = 1; i < aisShapes.size(); ++i)
        {
            TopoDS_Shape cuttingTool = aisShapes[i]->Shape();
            resultShape = MyCadGeom::CutShapes(resultShape, cuttingTool);

            if (resultShape.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"Cut operation failed during processing.", L"Cut Error", MB_OK | MB_ICONERROR);
                return;
            }
        }

        if (!resultShape.IsNull())
        {
            // Collect IDs of objects to remove
            std::vector<int> idsToRemove;
            for (const auto &aisShape : aisShapes)
            {
                for (const auto &obj : m_sceneObjects)
                {
                    if (obj.aisObject == aisShape)
                    {
                        idsToRemove.push_back(obj.id);
                        break;
                    }
                }
            }

            ClearAllContextSelections();

            // Remove all selected objects
            for (int id : idsToRemove)
            {
                RemoveObjectFromScene(id);
            }

            AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_GREEN1, "CutResult", true);

            wchar_t title[100];
            swprintf_s(title, _countof(title), L"OCCT Viewer - Cut Complete (%zu objects). Click to select.", selectedCount);
            UpdateWindowTitle(title);
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Cut operation resulted in no change or an error.", L"Cut Info", MB_OK | MB_ICONINFORMATION);
            ClearAllContextSelections();
        }
    }

    void AppCore::HandleThickenOperation()
    {
        if (m_selectedFacesList.IsEmpty() || m_parentShapeOfSelectedFaces.IsNull())
        {
            if (!m_selection1_IO.IsNull() && m_selection1_IO->IsKind(STANDARD_TYPE(AIS_Shape)))
            {
                Handle(AIS_Shape) aisParentShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
                TopoDS_Shape targetShape = aisParentShape->Shape();
                double thicknessValue = -2.0; // Default for hollowing a solid

                HINSTANCE hInstance = GetModuleHandle(NULL);
                INT_PTR dlgResult = DialogBoxParamW(hInstance, MAKEINTRESOURCEW(IDD_THICKNESS_DIALOG), m_hMainWnd, DlgProcThicknessLocal, (LPARAM)&thicknessValue);

                if (dlgResult == IDOK)
                {
                    TopTools_ListOfShape emptyFacesToRemove; // For BRepOffsetAPI_MakeThickSolid this would be faces to remove to create openings.
                                                             // For general thickening, it's often empty.
                    TopoDS_Shape resultShape = MyCadGeom::ThickenShape(targetShape, thicknessValue, emptyFacesToRemove);

                    if (!resultShape.IsNull() && !resultShape.IsSame(targetShape))
                    {
                        int idToRemove = -1;
                        for (const auto &obj : m_sceneObjects)
                        {
                            if (obj.aisObject == aisParentShape)
                                idToRemove = obj.id;
                        }
                        if (m_selection1_IO == aisParentShape)
                            m_selection1_IO.Nullify();
                        if (m_selection2_IO == aisParentShape)
                            m_selection2_IO.Nullify();

                        ClearAllContextSelections();

                        if (idToRemove != -1)
                            RemoveObjectFromScene(idToRemove);

                        AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_ORANGERED1, "ThickenedShape", true);
                        UpdateWindowTitle(L"OCCT Viewer - Shape Thicken Complete.");
                    }
                    else
                    {
                        MessageBoxW(m_hMainWnd, L"Shape Thicken resulted in no change or an error.", L"Operation Info", MB_OK | MB_ICONINFORMATION);
                    }
                }
                return;
            }
            MessageBoxW(m_hMainWnd, L"Select faces (SHIFT+Click) or a solid (CTRL+Click) for Thicken/Offset.", L"Thicken/Offset Error", MB_OK | MB_ICONERROR);
            return;
        }

        Handle(AIS_Shape) aisParentShape = Handle(AIS_Shape)::DownCast(m_parentShapeOfSelectedFaces);
        if (aisParentShape.IsNull() || aisParentShape->Shape().ShapeType() != TopAbs_SOLID)
        {
            MessageBoxW(m_hMainWnd, L"Parent of selected faces is not a solid or not found.", L"Thicken/Offset Error", MB_OK | MB_ICONERROR);
            return;
        }
        TopoDS_Solid parentSolid = TopoDS::Solid(aisParentShape->Shape());
        double thicknessValue = 5.0;

        HINSTANCE hInstance = GetModuleHandle(NULL);
        INT_PTR dlgResult = DialogBoxParamW(hInstance, MAKEINTRESOURCEW(IDD_THICKNESS_DIALOG), m_hMainWnd, DlgProcThicknessLocal, (LPARAM)&thicknessValue);

        if (dlgResult == -1)
        {
            DWORD error = GetLastError();
            wchar_t errorMsg[256];
            swprintf_s(errorMsg, _countof(errorMsg), L"DialogBoxParam failed! Error: %lu", error);
            MessageBoxW(m_hMainWnd, errorMsg, L"Dialog Error", MB_OK | MB_ICONERROR);
            return;
        }

        if (dlgResult == IDOK)
        {
            UpdateWindowTitle(L"OCCT Viewer - Applying multi-face offset...");
            bool operationSuccess = false;
            TopTools_ListOfShape originalFacesToOffset = m_selectedFacesList;
            TopTools_ListOfShape prismsToProcess;

            for (TopTools_ListIteratorOfListOfShape it(originalFacesToOffset); it.More(); it.Next())
            {
                TopoDS_Face faceToOffset = TopoDS::Face(it.Value());
                if (faceToOffset.IsNull())
                    continue;

                gp_Dir extrusionDir;
                try
                {
                    extrusionDir = MyCadGeom::GetCorrectedFaceNormal(parentSolid, faceToOffset);
                }
                catch (const Standard_Failure &e)
                {
                    wchar_t errMsg[256];
                    std::string errStr = e.GetMessageString();
                    size_t convertedChars = 0;
                    mbstowcs_s(&convertedChars, errMsg, sizeof(errMsg) / sizeof(wchar_t), errStr.c_str(), _TRUNCATE);
                    MessageBoxW(m_hMainWnd, errMsg, L"Normal Calculation Error", MB_OK | MB_ICONERROR);
                    continue;
                }

                BRepPrimAPI_MakePrism prismMaker(faceToOffset, gp_Vec(extrusionDir.XYZ()) * thicknessValue, Standard_False, Standard_True);
                if (prismMaker.IsDone() && !prismMaker.Shape().IsNull())
                {
                    prismsToProcess.Append(prismMaker.Shape());
                }
                else
                {
                    MessageBoxW(m_hMainWnd, L"Failed to create extrusion prism for one of the faces.", L"Prism Error", MB_OK);
                }
            }

            if (prismsToProcess.IsEmpty())
            {
                MessageBoxW(m_hMainWnd, L"No valid prisms created for offset.", L"Operation Info", MB_OK | MB_ICONINFORMATION);
                UpdateWindowTitle(L"OCCT Viewer - Multi-Face Offset failed.");
                if (!m_parentShapeOfSelectedFaces.IsNull() && m_context) // Check m_context
                {
                    for (TopTools_ListIteratorOfListOfShape itFace(originalFacesToOffset); itFace.More(); itFace.Next())
                    {
                        if (!itFace.Value().IsNull())
                        {
                            // Ensure BRepOwner is created correctly
                            m_context->AddOrRemoveSelected(new StdSelect_BRepOwner(itFace.Value(), m_parentShapeOfSelectedFaces, TopAbs_FACE), Standard_True);
                        }
                    }
                }
                return;
            }

            TopoDS_Shape toolShape;
            if (prismsToProcess.Extent() == 1)
            {
                toolShape = prismsToProcess.First();
            }
            else
            {
                TopoDS_Shape currentFusedPrisms = prismsToProcess.First();
                TopTools_ListIteratorOfListOfShape prismIt(prismsToProcess);
                prismIt.Next();
                for (; prismIt.More(); prismIt.Next())
                {
                    TopoDS_Shape nextPrism = prismIt.Value();
                    if (!nextPrism.IsNull())
                    {
                        BRepAlgoAPI_Fuse fuseOp(currentFusedPrisms, nextPrism);
                        fuseOp.SetRunParallel(Standard_True);
                        fuseOp.Build();
                        if (fuseOp.IsDone())
                        {
                            currentFusedPrisms = fuseOp.Shape();
                        }
                        else
                        {
                            MessageBoxW(m_hMainWnd, L"Failed to fuse prisms together.", L"Boolean Error", MB_OK | MB_ICONERROR);
                        }
                    }
                }
                toolShape = currentFusedPrisms;
            }

            if (toolShape.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"Failed to create a combined tool shape from prisms.", L"Operation Error", MB_OK | MB_ICONERROR);
                UpdateWindowTitle(L"OCCT Viewer - Multi-Face Offset failed.");
                return;
            }

            TopoDS_Shape finalResultShape;
            if (std::abs(thicknessValue) < Precision::Confusion())
            {
                finalResultShape = parentSolid;
            }
            else if (thicknessValue > 0)
            {
                finalResultShape = MyCadGeom::FuseShapes(parentSolid, toolShape);
            }
            else
            {
                finalResultShape = MyCadGeom::CutShapes(parentSolid, toolShape);
            }

            if (!finalResultShape.IsNull() && !finalResultShape.IsSame(parentSolid))
            {
                operationSuccess = true;
            }

            if (operationSuccess)
            {
                int idToRemove = -1;
                for (const auto &obj : m_sceneObjects)
                {
                    if (obj.aisObject == aisParentShape)
                        idToRemove = obj.id;
                }
                if (m_selection1_IO == aisParentShape)
                    m_selection1_IO.Nullify();
                if (m_selection2_IO == aisParentShape)
                    m_selection2_IO.Nullify();

                m_selectedFacesList.Clear();
                m_parentShapeOfSelectedFaces.Nullify();
                ClearAllContextSelections();

                if (idToRemove != -1)
                    RemoveObjectFromScene(idToRemove);

                AddObjectToScene(finalResultShape, Quantity_NameOfColor::Quantity_NOC_ORANGERED1, "MultiOffsetResult", true);
                UpdateWindowTitle(L"OCCT Viewer - Multi-Face Offset Complete.");
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Multi-Face Offset Boolean operation resulted in no change or an error.", L"Operation Info", MB_OK | MB_ICONINFORMATION);
                UpdateWindowTitle(L"OCCT Viewer - Multi-Face Offset failed.");
                if (!m_parentShapeOfSelectedFaces.IsNull() && m_context) // Check m_context
                {
                    for (TopTools_ListIteratorOfListOfShape itFace(originalFacesToOffset); itFace.More(); itFace.Next())
                    {
                        if (!itFace.Value().IsNull())
                        {
                            m_context->AddOrRemoveSelected(new StdSelect_BRepOwner(itFace.Value(), m_parentShapeOfSelectedFaces, TopAbs_FACE), Standard_True);
                        }
                    }
                }
            }
        }
    }

    TopTools_ListOfShape AppCore::GetSelectedEdges(const Handle(AIS_InteractiveObject) & parentAisIO_filter)
    {
        TopTools_ListOfShape selectedEdgesList;
        if (m_context.IsNull())
            return selectedEdgesList;

        for (m_context->InitSelected(); m_context->MoreSelected(); m_context->NextSelected())
        {
            Handle(SelectMgr_EntityOwner) owner = m_context->SelectedOwner();
            if (!owner.IsNull() && owner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
            {
                Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(owner);
                if (brepOwner->HasShape() && brepOwner->Shape().ShapeType() == TopAbs_EDGE)
                {
                    if (!parentAisIO_filter.IsNull())
                    {
                        // Ensure the selectable object of the owner is the parent AIS object.
                        // AIS_InteractiveObject itself is a SelectMgr_SelectableObject.
                        if (owner->Selectable() == parentAisIO_filter)
                        {
                            selectedEdgesList.Append(brepOwner->Shape());
                        }
                    }
                    else
                    {
                        selectedEdgesList.Append(brepOwner->Shape());
                    }
                }
            }
        }
        return selectedEdgesList;
    }

    void AppCore::HandleExtrudeOperation()
    {
        if (m_selectedFacesList.IsEmpty() || m_parentShapeOfSelectedFaces.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Please select one or more faces (SHIFT+Click) on a single solid to extrude.", L"Extrude Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisParentShape = Handle(AIS_Shape)::DownCast(m_parentShapeOfSelectedFaces);
        if (aisParentShape.IsNull() || aisParentShape->Shape().ShapeType() != TopAbs_SOLID)
        {
            MessageBoxW(m_hMainWnd, L"Parent shape for extrusion is not a solid.", L"Extrude Error", MB_OK | MB_ICONERROR);
            return;
        }
        TopoDS_Solid parentSolid = TopoDS::Solid(aisParentShape->Shape());

        double extrusionDistance = DEFAULT_EXTRUSION_DISTANCE;
        HINSTANCE hInstance = GetModuleHandle(NULL);
        INT_PTR dlgResult = DialogBoxParamW(hInstance, MAKEINTRESOURCEW(IDD_THICKNESS_DIALOG), m_hMainWnd, DlgProcThicknessLocal, (LPARAM)&extrusionDistance);

        if (dlgResult == IDOK)
        {
            if (std::abs(extrusionDistance) < Precision::Confusion())
            {
                MessageBoxW(m_hMainWnd, L"Extrusion distance is too small.", L"Extrude Info", MB_OK | MB_ICONINFORMATION);
                return;
            }

            UpdateWindowTitle(L"OCCT Viewer - Applying Extrusion...");

            TopoDS_Shape currentSolidShape = parentSolid;
            bool overallSuccess = false;

            if (m_selectedFacesList.Extent() == 1 && !m_selectedFacesList.First().IsNull())
            {
                currentSolidShape = MyCadGeom::ExtrudeFace(parentSolid, TopoDS::Face(m_selectedFacesList.First()), extrusionDistance);
                if (!currentSolidShape.IsNull() && !currentSolidShape.IsSame(parentSolid))
                {
                    overallSuccess = true;
                }
            }
            else
            {
                TopTools_ListOfShape prismsToProcess;
                for (TopTools_ListIteratorOfListOfShape it(m_selectedFacesList); it.More(); it.Next())
                {
                    TopoDS_Face face = TopoDS::Face(it.Value());
                    if (face.IsNull())
                        continue;

                    gp_Dir extrusionDir;
                    try
                    {
                        extrusionDir = MyCadGeom::GetCorrectedFaceNormal(parentSolid, face);
                    }
                    catch (const Standard_Failure &)
                    {
                        // Optional: Log or notify about problematic face
                        continue;
                    }

                    BRepPrimAPI_MakePrism prismMaker(face, gp_Vec(extrusionDir.XYZ()) * extrusionDistance, Standard_False, Standard_True);
                    if (prismMaker.IsDone() && !prismMaker.Shape().IsNull())
                    {
                        prismsToProcess.Append(prismMaker.Shape());
                    }
                }
                if (!prismsToProcess.IsEmpty())
                {
                    TopoDS_Shape toolShape;
                    if (prismsToProcess.Extent() == 1)
                    {
                        toolShape = prismsToProcess.First();
                    }
                    else
                    {
                        // Fuse all prisms into a single tool shape
                        BRepAlgoAPI_Fuse fuseOp;
                        fuseOp.SetArguments(prismsToProcess); // Use SetArguments for a list
                        fuseOp.SetRunParallel(Standard_True);
                        fuseOp.Build();
                        if (fuseOp.IsDone())
                        {
                            toolShape = fuseOp.Shape();
                        }
                        else
                        {
                            // Optional: Log or notify about fusion failure
                        }
                    }

                    if (!toolShape.IsNull())
                    {
                        if (extrusionDistance >= 0)
                        { // Positive distance: Fuse
                            currentSolidShape = MyCadGeom::FuseShapes(parentSolid, toolShape);
                        }
                        else
                        { // Negative distance: Cut
                            currentSolidShape = MyCadGeom::CutShapes(parentSolid, toolShape);
                        }
                        if (!currentSolidShape.IsNull() && !currentSolidShape.IsSame(parentSolid))
                        {
                            overallSuccess = true;
                        }
                    }
                }
            }

            if (overallSuccess)
            {
                int idToRemove = -1;
                for (const auto &obj : m_sceneObjects)
                {
                    if (obj.aisObject == aisParentShape)
                        idToRemove = obj.id;
                }

                if (m_selection1_IO == aisParentShape)
                    m_selection1_IO.Nullify();
                if (m_selection2_IO == aisParentShape)
                    m_selection2_IO.Nullify();

                m_selectedFacesList.Clear();
                m_parentShapeOfSelectedFaces.Nullify();
                ClearAllContextSelections();

                if (idToRemove != -1)
                    RemoveObjectFromScene(idToRemove);

                AddObjectToScene(currentSolidShape, Quantity_NameOfColor::Quantity_NOC_SKYBLUE, "ExtrudedResult", true);
                UpdateWindowTitle(L"OCCT Viewer - Extrusion Complete.");
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Extrusion resulted in no change or an error.", L"Extrusion Info", MB_OK | MB_ICONINFORMATION);
                UpdateWindowTitle(L"OCCT Viewer - Extrusion failed.");
                if (!m_parentShapeOfSelectedFaces.IsNull() && m_context)
                { // Re-select originally selected faces on failure
                    for (TopTools_ListIteratorOfListOfShape itFace(m_selectedFacesList); itFace.More(); itFace.Next())
                    {
                        if (!itFace.Value().IsNull())
                        {
                            m_context->AddOrRemoveSelected(new StdSelect_BRepOwner(itFace.Value(), m_parentShapeOfSelectedFaces, TopAbs_FACE), Standard_True);
                        }
                    }
                }
            }
        }
    }
    void AppCore::HandleFilletOperation()
    {
        Handle(AIS_InteractiveObject) parentAisIO = m_selection1_IO;
        TopoDS_Shape parentSolidShape;

        if (parentAisIO.IsNull() || !parentAisIO->IsKind(STANDARD_TYPE(AIS_Shape)))
        {
            // Try to get parent from currently selected sub-shapes (edges)
            if (!m_context.IsNull() && m_context->NbSelected() > 0)
            {
                m_context->InitSelected();
                if (m_context->MoreSelected())
                {
                    Handle(SelectMgr_EntityOwner) owner = m_context->SelectedOwner();
                    if (owner && owner->HasSelectable() && owner->Selectable()->IsKind(STANDARD_TYPE(AIS_Shape)))
                    {
                        parentAisIO = Handle(AIS_InteractiveObject)::DownCast(owner->Selectable());
                    }
                }
            }

            if (parentAisIO.IsNull() || !parentAisIO->IsKind(STANDARD_TYPE(AIS_Shape)))
            {
                MessageBoxW(m_hMainWnd, L"Please select a solid shape (Ctrl+Click it) first, then select edges for filleting.", L"Fillet Info", MB_OK | MB_ICONINFORMATION);
                return;
            }
        }
        parentSolidShape = Handle(AIS_Shape)::DownCast(parentAisIO)->Shape();
        if (parentSolidShape.ShapeType() != TopAbs_SOLID)
        {
            MessageBoxW(m_hMainWnd, L"Fillet operation requires a solid shape.", L"Fillet Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopTools_ListOfShape edgesToFillet = GetSelectedEdges(parentAisIO);
        if (edgesToFillet.IsEmpty())
        {
            MessageBoxW(m_hMainWnd, L"Please select one or more edges on the chosen solid to fillet. (Shift+Click edges after selecting the solid)", L"Fillet Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        double radius = DEFAULT_FILLET_RADIUS;
        HINSTANCE hInstance = GetModuleHandle(NULL);
        INT_PTR dlgResult = DialogBoxParamW(hInstance, MAKEINTRESOURCEW(IDD_THICKNESS_DIALOG), m_hMainWnd, DlgProcThicknessLocal, (LPARAM)&radius);

        if (dlgResult == IDOK)
        {
            if (radius <= Precision::Confusion())
            {
                MessageBoxW(m_hMainWnd, L"Fillet radius is too small.", L"Fillet Info", MB_OK | MB_ICONINFORMATION);
                return;
            }
            UpdateWindowTitle(L"OCCT Viewer - Applying Fillet...");
            TopoDS_Shape resultShape = MyCadGeom::FilletEdges(parentSolidShape, edgesToFillet, radius);

            if (!resultShape.IsNull() && !resultShape.IsSame(parentSolidShape))
            {
                int idToRemove = -1;
                for (const auto &obj : m_sceneObjects)
                {
                    if (obj.aisObject == parentAisIO)
                        idToRemove = obj.id;
                }
                if (m_selection1_IO == parentAisIO)
                    m_selection1_IO.Nullify();
                if (m_selection2_IO == parentAisIO)
                    m_selection2_IO.Nullify();

                m_selectedFacesList.Clear(); // Clear any face selections
                m_parentShapeOfSelectedFaces.Nullify();
                ClearAllContextSelections(); // Clears edge selections too

                if (idToRemove != -1)
                    RemoveObjectFromScene(idToRemove);

                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_VIOLET, "FilletedResult", true);
                UpdateWindowTitle(L"OCCT Viewer - Fillet Complete.");
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Fillet operation resulted in no change or an error.", L"Fillet Info", MB_OK | MB_ICONINFORMATION);
                UpdateWindowTitle(L"OCCT Viewer - Fillet failed.");
            }
        }
    }

    void AppCore::HandleChamferOperation()
    {
        Handle(AIS_InteractiveObject) parentAisIO = m_selection1_IO;
        TopoDS_Shape parentSolidShape;

        if (parentAisIO.IsNull() || !parentAisIO->IsKind(STANDARD_TYPE(AIS_Shape)))
        {
            // Try to get parent from currently selected sub-shapes (edges)
            if (!m_context.IsNull() && m_context->NbSelected() > 0)
            {
                m_context->InitSelected();
                if (m_context->MoreSelected())
                {
                    Handle(SelectMgr_EntityOwner) owner = m_context->SelectedOwner();
                    if (owner && owner->HasSelectable() && owner->Selectable()->IsKind(STANDARD_TYPE(AIS_Shape)))
                    {
                        parentAisIO = Handle(AIS_InteractiveObject)::DownCast(owner->Selectable());
                    }
                }
            }
            if (parentAisIO.IsNull() || !parentAisIO->IsKind(STANDARD_TYPE(AIS_Shape)))
            {
                MessageBoxW(m_hMainWnd, L"Please select a solid shape (Ctrl+Click it) before selecting edges for chamfering.", L"Chamfer Info", MB_OK | MB_ICONINFORMATION);
                return;
            }
        }
        parentSolidShape = Handle(AIS_Shape)::DownCast(parentAisIO)->Shape();
        if (parentSolidShape.ShapeType() != TopAbs_SOLID)
        {
            MessageBoxW(m_hMainWnd, L"Chamfer operation requires a solid shape.", L"Chamfer Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopTools_ListOfShape edgesToChamfer = GetSelectedEdges(parentAisIO);
        if (edgesToChamfer.IsEmpty())
        {
            MessageBoxW(m_hMainWnd, L"Please select one or more edges on the chosen solid to chamfer. (Shift+Click edges after selecting the solid)", L"Chamfer Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        double distance = DEFAULT_CHAMFER_DISTANCE;
        HINSTANCE hInstance = GetModuleHandle(NULL);
        INT_PTR dlgResult = DialogBoxParamW(hInstance, MAKEINTRESOURCEW(IDD_THICKNESS_DIALOG), m_hMainWnd, DlgProcThicknessLocal, (LPARAM)&distance);

        if (dlgResult == IDOK)
        {
            if (distance <= Precision::Confusion())
            {
                MessageBoxW(m_hMainWnd, L"Chamfer distance is too small.", L"Chamfer Info", MB_OK | MB_ICONINFORMATION);
                return;
            }
            UpdateWindowTitle(L"OCCT Viewer - Applying Chamfer...");
            TopoDS_Shape resultShape = MyCadGeom::ChamferEdges(parentSolidShape, edgesToChamfer, distance);

            if (!resultShape.IsNull() && !resultShape.IsSame(parentSolidShape))
            {
                int idToRemove = -1;
                for (const auto &obj : m_sceneObjects)
                {
                    if (obj.aisObject == parentAisIO)
                        idToRemove = obj.id;
                }
                if (m_selection1_IO == parentAisIO)
                    m_selection1_IO.Nullify();
                if (m_selection2_IO == parentAisIO)
                    m_selection2_IO.Nullify();

                m_selectedFacesList.Clear();
                m_parentShapeOfSelectedFaces.Nullify();
                ClearAllContextSelections();

                if (idToRemove != -1)
                    RemoveObjectFromScene(idToRemove);

                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_MEDIUMPURPLE, "ChamferedResult", true);
                UpdateWindowTitle(L"OCCT Viewer - Chamfer Complete.");
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Chamfer operation resulted in no change or an error.", L"Chamfer Info", MB_OK | MB_ICONINFORMATION);
                UpdateWindowTitle(L"OCCT Viewer - Chamfer failed.");
            }
        }
    }

    void AppCore::HandleOffsetFaceOperation()
    {
        HandleThickenOperation(); // Reuses thicken logic which can also offset faces if selected.
    }

    void AppCore::HandleClearScene()
    {
        // Use command system for undo/redo support
        auto command = std::make_unique<ClearSceneCommand>(this);
        ExecuteCommand(std::move(command));
        UpdateWindowTitle(L"OCCT Viewer - Scene Cleared. Click to select.");
    }

    bool AppCore::MapToOccViewCoords(int mainX, int mainY, int &occX, int &occY)
    {
        if (!m_hOccChildWnd)
            return false;
        int adjustedY_main = mainY - m_buttonAreaHeight;
        RECT mainClientRect;
        GetClientRect(m_hMainWnd, &mainClientRect);

        RECT occChildRect;
        GetClientRect(m_hOccChildWnd, &occChildRect);

        // The mainX, mainY are relative to the main window.
        // We need to check if they are within the bounds of the OCC child window area.
        // The OCC child window's top-left is (0, m_buttonAreaHeight) in main window client coordinates.
        // Its bottom-right is (mainClientRect.right, mainClientRect.bottom).

        if (mainX < 0 || mainX >= occChildRect.right ||
            adjustedY_main < 0 || adjustedY_main >= occChildRect.bottom)
        {
            return false;
        }
        occX = mainX;
        occY = adjustedY_main; // occY is relative to the OCC child window's top edge
        return true;
    }

    void AppCore::ProcessLButtonDown(int x_main, int y_main, UINT flags)
    {
        if (m_context.IsNull() || m_view.IsNull())
            return;
        int occX, occY;
        if (!MapToOccViewCoords(x_main, y_main, occX, occY))
            return;

        // Handle split mode mouse down
        if (m_isInSplitMode)
        {
            HandleSplitMouseDown(occX, occY); // This now handles drag start
            return;
        }

        // Handle sweep selection mode
        if (m_isInSweepSelectionMode)
        {
            HandleSweepModeClick(occX, occY);
            return;
        }

        // Handle fill edge selection mode
        if (m_isInFillSelectionMode)
        {
            HandleFillEdgeSelection(occX, occY);
            return;
        }

        m_context->MoveTo(occX, occY, m_view, Standard_False); // Standard_False: don't update view yet

        bool ctrlPressed = (flags & MK_CONTROL);
        bool shiftPressed = (flags & MK_SHIFT);

        if (m_context->HasDetected())
        {
            Handle(AIS_InteractiveObject) detectedIO = m_context->DetectedInteractive();
            Handle(SelectMgr_EntityOwner) detectedOwner = m_context->DetectedOwner();

            if (ctrlPressed) // Object selection for boolean ops etc.
            {
                // If selecting objects, clear any previous sub-shape (face/edge) selections
                m_selectedFacesList.Clear();
                m_parentShapeOfSelectedFaces.Nullify();

                // Enhanced multiple selection logic
                if (IsSelected(detectedIO))
                {
                    // Object is already selected, remove it from selection
                    RemoveFromSelection(detectedIO);

                    size_t count = GetSelectedObjectCount();
                    if (count == 0)
                    {
                        UpdateWindowTitle(L"OCCT Viewer - No objects selected. Ctrl+Click to select.");
                    }
                    else if (count == 1)
                    {
                        UpdateWindowTitle(L"OCCT Viewer - 1 object selected. Ctrl+Click for more.");
                    }
                    else
                    {
                        wchar_t title[100];
                        swprintf_s(title, _countof(title), L"OCCT Viewer - %zu objects selected. Ready for operations.", count);
                        UpdateWindowTitle(title);
                    }
                }
                else
                {
                    // Object is not selected, add it to selection
                    AddToSelection(detectedIO);

                    size_t count = GetSelectedObjectCount();
                    if (count == 1)
                    {
                        UpdateWindowTitle(L"OCCT Viewer - 1 object selected. Ctrl+Click for more.");
                    }
                    else if (count == 2)
                    {
                        UpdateWindowTitle(L"OCCT Viewer - 2 objects selected. Ready for Cut/Fuse/etc.");
                    }
                    else
                    {
                        wchar_t title[100];
                        swprintf_s(title, _countof(title), L"OCCT Viewer - %zu objects selected. Ready for operations.", count);
                        UpdateWindowTitle(title);
                    }
                }
            }
            else if (shiftPressed && detectedOwner && detectedOwner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner))) // Sub-shape selection (faces/edges)
            {
                Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(detectedOwner);
                TopoDS_Shape detectedSubShape = brepOwner->Shape();
                Handle(AIS_InteractiveObject) currentParentIO = Handle(AIS_InteractiveObject)::DownCast(brepOwner->Selectable());

                if (detectedSubShape.ShapeType() == TopAbs_FACE)
                {
                    TopoDS_Face detectedFace = TopoDS::Face(detectedSubShape);
                    if (m_parentShapeOfSelectedFaces.IsNull()) // First face selection
                    {
                        m_parentShapeOfSelectedFaces = currentParentIO;
                        m_selectedFacesList.Append(detectedFace);
                        m_context->AddOrRemoveSelected(brepOwner, Standard_True);
                        UpdateWindowTitle(L"OCCT Viewer - Face selected. SHIFT+Click to add more.");
                    }
                    else if (m_parentShapeOfSelectedFaces == currentParentIO) // Adding/removing faces from the same parent
                    {
                        bool found = false;
                        TopTools_ListIteratorOfListOfShape it(m_selectedFacesList);
                        for (; it.More(); it.Next())
                        {
                            if (it.Value().IsSame(detectedFace))
                            {
                                m_selectedFacesList.Remove(it);
                                m_context->AddOrRemoveSelected(brepOwner, Standard_False); // Deselect
                                found = true;
                                break;
                            }
                        }
                        if (!found)
                        {
                            m_selectedFacesList.Append(detectedFace);
                            m_context->AddOrRemoveSelected(brepOwner, Standard_True); // Select
                        }
                        // Update window title based on selection count
                        if (m_selectedFacesList.IsEmpty())
                        {
                            m_parentShapeOfSelectedFaces.Nullify(); // Clear parent if no faces selected
                            UpdateWindowTitle(L"OCCT Viewer - Selection cleared.");
                        }
                        else
                        {
                            wchar_t title[100];
                            swprintf_s(title, _countof(title), L"OCCT Viewer - %d Faces selected. SHIFT+Click to (de)select.", m_selectedFacesList.Extent());
                            UpdateWindowTitle(title);
                        }
                    }
                    else // Switched to selecting faces on a *different* parent object
                    {
                        m_context->ClearSelected(Standard_False); // Clear all previous selections
                        m_selectedFacesList.Clear();
                        m_parentShapeOfSelectedFaces = currentParentIO;
                        m_selectedFacesList.Append(detectedFace);
                        m_context->AddOrRemoveSelected(brepOwner, Standard_True);
                        UpdateWindowTitle(L"OCCT Viewer - Face selected (new parent). SHIFT+Click to add more.");
                    }
                    // Clear object-level selections if now selecting faces
                    m_selection1_IO.Nullify();
                    m_selection2_IO.Nullify();
                }
                else if (detectedSubShape.ShapeType() == TopAbs_EDGE)
                {
                    // Handle edge selection for operations like Fillet/Chamfer
                    // This assumes the parent object (m_selection1_IO) was selected first via Ctrl+Click
                    if (m_selection1_IO.IsNull())
                    {                                                            // Parent object not yet selected
                        m_selection1_IO = currentParentIO;                       // Set the parent
                        m_context->SetSelected(m_selection1_IO, Standard_False); // Highlight parent
                    }

                    if (currentParentIO == m_selection1_IO)
                    {                                                             // Edge belongs to the selected main object
                        m_context->AddOrRemoveSelected(brepOwner, Standard_True); // Add/Remove edge to selection
                        // Count selected edges for window title
                        int edgeCount = 0;
                        for (m_context->InitSelected(); m_context->MoreSelected(); m_context->NextSelected())
                        {
                            Handle(SelectMgr_EntityOwner) owner = m_context->SelectedOwner();
                            if (owner && owner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
                            {
                                Handle(StdSelect_BRepOwner) bowner = Handle(StdSelect_BRepOwner)::DownCast(owner);
                                if (bowner->HasShape() && bowner->Shape().ShapeType() == TopAbs_EDGE)
                                {
                                    edgeCount++;
                                }
                            }
                        }
                        wchar_t title[100];
                        swprintf_s(title, _countof(title), L"OCCT Viewer - %d Edges selected. Ready for Fillet/Chamfer.", edgeCount);
                        UpdateWindowTitle(title);
                    }
                    else
                    {
                        // Edge is on a different object than m_selection1_IO, clear and start new edge selection
                        m_context->ClearSelected(Standard_False);
                        m_selection1_IO = currentParentIO;                        // New parent
                        m_context->SetSelected(m_selection1_IO, Standard_False);  // Highlight new parent
                        m_context->AddOrRemoveSelected(brepOwner, Standard_True); // Select this edge
                        UpdateWindowTitle(L"OCCT Viewer - 1 Edge selected (new parent). Ready for Fillet/Chamfer.");
                    }
                    // Clear face-level selections if now selecting edges
                    m_selectedFacesList.Clear();
                    m_parentShapeOfSelectedFaces.Nullify();
                }
            }
            else // Single click without modifiers (or on empty space)
            {
                // Clear all selections and edge highlights
                ClearAllContextSelections();
                ClearEdgeHighlights();

                // Normal selection logic - don't automatically select edges on every click
                m_context->MoveTo(occX, occY, m_view, Standard_True);
                m_context->Select(Standard_True);

                // Handle normal selection logic
                if (detectedOwner && detectedOwner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
                {
                    Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(detectedOwner);
                    if (brepOwner->HasShape())
                    {
                        m_context->AddOrRemoveSelected(brepOwner, Standard_True);
                        TopoDS_Shape subShape = brepOwner->Shape();

                        if (subShape.ShapeType() == TopAbs_EDGE)
                        {
                            // Only highlight edges when specifically requested, not on every click
                            Handle(AIS_InteractiveObject) parentObject = Handle(AIS_InteractiveObject)::DownCast(brepOwner->Selectable());
                            AddToSelection(parentObject); // Add parent to multiple selection
                            UpdateWindowTitle(L"OCCT Viewer - Edge detected. Use specific edge selection mode for operations.");
                        }
                        else if (subShape.ShapeType() == TopAbs_FACE)
                        {
                            // For single click on a face, make it the start of a multi-face selection
                            m_selectedFacesList.Append(TopoDS::Face(subShape));
                            m_parentShapeOfSelectedFaces = Handle(AIS_InteractiveObject)::DownCast(brepOwner->Selectable());
                            UpdateWindowTitle(L"OCCT Viewer - Face Selected. SHIFT+Click to add more.");
                        }
                        else
                        {
                            UpdateWindowTitle(L"OCCT Viewer - Sub-element Selected.");
                        }
                    }
                }
                else if (detectedIO) // Clicked on an object, not a sub-shape
                {
                    AddToSelection(detectedIO); // Use new multiple selection system
                    UpdateWindowTitle(L"OCCT Viewer - Object Selected. Ctrl+Click for more. SHIFT+Click sub-shape.");
                }
                else
                {
                    UpdateWindowTitle(L"OCCT Viewer - Selection Cleared.");
                }
            }
        }
        else // Clicked on empty space
        {
            ClearAllContextSelections(); // Use the enhanced clear method
            UpdateWindowTitle(L"OCCT Viewer - Click for selection.");
        }
        // m_view->Update(); // OCCT 7.3+
        RedrawView(); // For older OCCT or general redraw
    }

    void AppCore::ProcessLButtonUp(int x_main, int y_main, UINT flags)
    {
        if (m_context.IsNull() || m_view.IsNull())
            return;
        int occX, occY;
        if (!MapToOccViewCoords(x_main, y_main, occX, occY))
            return;

        // Handle split mode mouse up
        if (m_isInSplitMode && m_isDraggingSplitLine)
        { // Ensure we were dragging
            HandleSplitMouseUp(occX, occY);
            return;
        }

        // Normal mouse up handling (if needed)
        // Currently no special handling needed for normal mode LButtonUp
    }

    void AppCore::ClearAllContextSelections()
    {
        if (!m_context.IsNull())
        {
            m_context->ClearSelected(Standard_False); // Standard_False: don't update viewer yet
        }
        // Also clear internal tracking variables
        m_selection1_IO.Nullify();
        m_selection2_IO.Nullify();
        m_selectedObjects.clear(); // Clear multiple selection
        m_selectedFacesList.Clear();
        m_parentShapeOfSelectedFaces.Nullify();
        // m_selectedEdgesForFill.Clear(); // If relevant, or handle in specific mode exits

        // Update window title to reflect cleared selection
        UpdateWindowTitle(L"OCCT Viewer - No Selection");

        RedrawView(); // Redraw after clearing
    }

    void AppCore::AddToSelection(const Handle(AIS_InteractiveObject) & object)
    {
        if (object.IsNull())
            return;

        // Check if already selected
        if (!IsSelected(object))
        {
            m_selectedObjects.push_back(object);

            // Update legacy selection variables for backward compatibility
            if (m_selectedObjects.size() == 1)
            {
                m_selection1_IO = object;
            }
            else if (m_selectedObjects.size() == 2)
            {
                m_selection2_IO = object;
            }

            // Add to OpenCASCADE context selection
            if (!m_context.IsNull())
            {
                m_context->AddOrRemoveSelected(object, Standard_True);
            }
        }
    }

    void AppCore::RemoveFromSelection(const Handle(AIS_InteractiveObject) & object)
    {
        if (object.IsNull())
            return;

        auto it = std::find(m_selectedObjects.begin(), m_selectedObjects.end(), object);
        if (it != m_selectedObjects.end())
        {
            m_selectedObjects.erase(it);

            // Update legacy selection variables
            if (m_selection1_IO == object)
            {
                m_selection1_IO = m_selectedObjects.empty() ? Handle(AIS_InteractiveObject)() : m_selectedObjects[0];
            }
            if (m_selection2_IO == object)
            {
                m_selection2_IO = (m_selectedObjects.size() < 2) ? Handle(AIS_InteractiveObject)() : m_selectedObjects[1];
            }

            // Remove from OpenCASCADE context selection
            if (!m_context.IsNull())
            {
                m_context->AddOrRemoveSelected(object, Standard_False);
            }
        }
    }

    bool AppCore::IsSelected(const Handle(AIS_InteractiveObject) & object) const
    {
        if (object.IsNull())
            return false;
        return std::find(m_selectedObjects.begin(), m_selectedObjects.end(), object) != m_selectedObjects.end();
    }

    void AppCore::ClearObjectSelection()
    {
        for (const auto &obj : m_selectedObjects)
        {
            if (!m_context.IsNull() && !obj.IsNull())
            {
                m_context->AddOrRemoveSelected(obj, Standard_False);
            }
        }
        m_selectedObjects.clear();
        m_selection1_IO.Nullify();
        m_selection2_IO.Nullify();
    }

    Handle(AIS_InteractiveObject) AppCore::GetAisObjectById(int id)
    {
        for (const auto &obj : m_sceneObjects)
        {
            if (obj.id == id)
                return obj.aisObject;
        }
        return Handle(AIS_InteractiveObject)();
    }

    void AppCore::RedrawView()
    {
        if (!m_view.IsNull())
        {
            m_view->Redraw();
        }
    }

    void AppCore::FitAll()
    {
        if (!m_view.IsNull())
        {
            m_view->FitAll();
            m_view->ZFitAll(); // Ensure depth is also fit
            m_view->Redraw();
        }
    }

    void AppCore::SetViewProjection(V3d_TypeOfOrientation aOrientation)
    {
        if (!m_view.IsNull())
        {
            m_view->SetProj(aOrientation);
            FitAll(); // Refit after changing projection
        }
    }
    void AppCore::ResetView()
    {
        if (!m_view.IsNull())
        {
            m_view->Reset();
            FitAll();
        }
    }

    void AppCore::MustBeResized()
    {
        if (!m_view.IsNull())
        {
            m_view->MustBeResized();
        }
    }

    void AppCore::SetViewBackgroundColor(const Quantity_Color &color)
    {
        if (!m_view.IsNull())
        {
            m_view->SetBackgroundColor(color);
            RedrawView();
        }
    }

    void AppCore::UpdateWindowTitle(const std::wstring &newTitle)
    {
        if (m_hMainWnd)
        {
            SetWindowTextW(m_hMainWnd, newTitle.c_str());
        }
    }

    Bnd_Box AppCore::GetSceneBoundingBox() const
    {
        Bnd_Box globalBBox;
        if (m_context.IsNull())
            return globalBBox;

        AIS_ListOfInteractive displayedObjects;
        m_context->DisplayedObjects(displayedObjects); // Get only currently displayed objects

        for (AIS_ListIteratorOfListOfInteractive it(displayedObjects); it.More(); it.Next())
        {
            Handle(AIS_InteractiveObject) currentIO = it.Value();
            if (currentIO->IsKind(STANDARD_TYPE(AIS_Shape)))
            {
                Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(currentIO);
                if (!aisShape.IsNull() && !aisShape->Shape().IsNull())
                {
                    Bnd_Box shapeBBox;
                    // Use BRepBndLib::Add for more robust bounding box calculation, especially for complex shapes.
                    // The false flag means to not include triangulation tolerance.
                    BRepBndLib::Add(aisShape->Shape(), shapeBBox, Standard_False);
                    if (!shapeBBox.IsVoid())
                    {
                        globalBBox.Add(shapeBBox);
                    }
                }
            }
        }
        return globalBBox;
    }

    void AppCore::MouseMoveHandler(int x_main, int y_main, UINT flags)
    {
        if (m_view.IsNull())
            return;

        int occX, occY;
        if (!MapToOccViewCoords(x_main, y_main, occX, occY))
        {
            if (m_isDraggingSplitLine && m_hMainWnd)
                ReleaseCapture();
            return;
        }

        // Handle view manipulation if buttons are pressed
        if (flags & MK_MBUTTON || (flags & MK_LBUTTON && (GetAsyncKeyState(VK_CONTROL) & 0x8000)))
        {
            m_view->Rotation(occX, occY);
        }
        else if (m_isPanning && (flags & MK_RBUTTON))
        {
            m_view->Pan(occX - m_lastMouseX_occ, m_lastMouseY_occ - occY);
        }
        else
        {
            // Normal hover behavior - only basic detection without automatic edge highlighting
            if (!m_context.IsNull() && !m_isInSplitMode && !m_isInFillSelectionMode && !m_isInSweepSelectionMode)
            {
                // Only do basic MoveTo for object detection, but don't force edge highlighting
                m_context->MoveTo(occX, occY, m_view, Standard_True);
            }
        }

        m_lastMouseX_occ = occX;
        m_lastMouseY_occ = occY;

        if (flags & (MK_LBUTTON | MK_MBUTTON | MK_RBUTTON))
        {
            RedrawView();
        }
    }

    void AppCore::MouseRButtonDownHandler(int x_main, int y_main, UINT flags)
    {
        if (m_view.IsNull())
            return;
        int occX, occY;
        if (!MapToOccViewCoords(x_main, y_main, occX, occY))
            return;

        // Handle Fill edge selection mode - right-click to finish selection
        if (m_isInFillSelectionMode)
        {
            HandleFillSelectionComplete();
            return; // Exclusive mode
        }

        // Handle Sweep mode - right-click to cancel
        if (m_isInSweepSelectionMode)
        {
            EndSweepSelectionMode();
            MessageBoxW(m_hMainWnd, L"Sweep selection cancelled.", L"Sweep Mode", MB_OK | MB_ICONINFORMATION);
            return;
        }

        // Handle Split mode - right-click to cancel
        if (m_isInSplitMode)
        {
            EndSplitMode();
            MessageBoxW(m_hMainWnd, L"Split mode cancelled.", L"Split Mode", MB_OK | MB_ICONINFORMATION);
            return;
        }

        // Default Right Mouse Button behavior: Start Panning
        m_lastMouseX_occ = occX;
        m_lastMouseY_occ = occY;
        m_isPanning = Standard_True;
        // Potentially show a context menu here in the future if not panning
    }

    void AppCore::MouseMButtonDownHandler(int x_main, int y_main, UINT flags)
    {
        if (m_view.IsNull())
            return;
        int occX, occY;
        if (!MapToOccViewCoords(x_main, y_main, occX, occY))
            return;
        m_view->StartRotation(occX, occY);
        m_lastMouseX_occ = occX; // Store last position for MButton operations if any
        m_lastMouseY_occ = occY;
    }

    void AppCore::MouseRButtonUpHandler(int x_main, int y_main, UINT flags)
    {
        if (m_isPanning)
        { // Only stop panning if we were panning
            m_isPanning = Standard_False;
        }
        // If a context menu was shown, it would be handled here or by Windows.
    }
    void AppCore::MouseMButtonUpHandler(int x_main, int y_main, UINT flags)
    {
        // Middle mouse button up usually doesn't require specific action after rotation.
        // The view->Rotation call in MouseMoveHandler handles the rotation itself.
    }

    void AppCore::MouseWheelHandler(short zDelta, int x_main, int y_main)
    {
        if (m_view.IsNull())
            return;

        // x_main, y_main are screen coordinates. Convert to client coordinates of m_hMainWnd
        POINT ptScreen = {x_main, y_main};
        ScreenToClient(m_hMainWnd, &ptScreen); // ptScreen now holds client coordinates for m_hMainWnd

        int occX_view, occY_view;
        // Map these client coordinates to OCC view coordinates
        if (!MapToOccViewCoords(ptScreen.x, ptScreen.y, occX_view, occY_view))
        {
            // If mapping fails (e.g., cursor outside OCC view), zoom to center
            RECT viewRect;
            GetClientRect(m_hOccChildWnd, &viewRect);
            occX_view = (viewRect.left + viewRect.right) / 2;
            occY_view = (viewRect.top + viewRect.bottom) / 2;
        }

        Standard_Real currentZoom = m_view->Scale();
        Standard_Real factor = (zDelta > 0) ? ZOOM_FACTOR : (1.0 / ZOOM_FACTOR);

        // ZoomAtPoint requires two pairs of coordinates:
        // (Xpix, Ypix) - center of zoom in pixels
        // (X, Y) - center of zoom in V3d_View view coordinates (usually same as Xpix, Ypix for pixel-based zoom)
        m_view->ZoomAtPoint(occX_view, occY_view, occX_view, occY_view);
        m_view->SetScale(currentZoom * factor);
        RedrawView();
    }

    void AppCore::HandleRevolveOperation()
    {
        if (m_selectedFacesList.IsEmpty() || m_parentShapeOfSelectedFaces.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"No face selected. Creating a demonstration revolve shape.\nTo revolve a face: SHIFT+Click a face first, then click Revolve.",
                        L"Revolve Demo", MB_OK | MB_ICONINFORMATION);

            double angle = 360.0; // Default angle
            // You would typically use a dialog to get the angle from the user
            // For now, using default Z-axis and 360 degrees.

            // Example: Create a simple rectangle face to revolve into a cylinder for demo
            gp_Pnt p1(10, 0, 0);
            gp_Pnt p2(10, 0, 50);
            gp_Pnt p3(30, 0, 50); // Rectangle in XZ plane, to be revolved around Z-axis
            gp_Pnt p4(30, 0, 0);
            TopoDS_Edge e1 = BRepBuilderAPI_MakeEdge(p1, p2);
            TopoDS_Edge e2 = BRepBuilderAPI_MakeEdge(p2, p3);
            TopoDS_Edge e3 = BRepBuilderAPI_MakeEdge(p3, p4);
            TopoDS_Edge e4 = BRepBuilderAPI_MakeEdge(p4, p1);
            TopoDS_Wire demoWire = BRepBuilderAPI_MakeWire(e1, e2, e3, e4);
            if (demoWire.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"Failed to create demo wire.", L"Revolve Error", MB_OK | MB_ICONERROR);
                return;
            }
            // Create a simple face from the wire (placeholder - will be implemented later)
            TopoDS_Face demoFace;

            gp_Ax1 revolutionAxis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1)); // Z-axis
            TopoDS_Shape revolvedShape = MyCadGeom::RevolveShape(demoFace, revolutionAxis, angle);

            if (!revolvedShape.IsNull())
            {
                AddObjectToScene(revolvedShape, Quantity_NameOfColor::Quantity_NOC_ORANGE, "DemoRevolvedShape", true);
                UpdateWindowTitle(L"OCCT Viewer - Demo revolution completed.");
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Failed to create demo revolution.", L"Revolve Error", MB_OK | MB_ICONERROR);
            }
            return;
        }

        if (m_selectedFacesList.Extent() > 1)
        {
            MessageBoxW(m_hMainWnd, L"Please select only one face for revolution.", L"Revolve Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisParentShape = Handle(AIS_Shape)::DownCast(m_parentShapeOfSelectedFaces);
        if (aisParentShape.IsNull())
        { // Should not happen if m_parentShapeOfSelectedFaces is set
            MessageBoxW(m_hMainWnd, L"Parent shape for revolution is not valid.", L"Revolve Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Face faceToRevolve = TopoDS::Face(m_selectedFacesList.First());
        if (faceToRevolve.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected face is not valid for revolution.", L"Revolve Error", MB_OK | MB_ICONERROR);
            return;
        }

        double angle = 360.0; // Default, get from dialog
        // TODO: Implement axis selection or dialog for axis input
        // For now, using default Z-axis passing through the origin
        gp_Ax1 revolutionAxis = GetRevolveAxisFromUser(); // Placeholder for user input

        UpdateWindowTitle(L"OCCT Viewer - Creating revolution...");
        TopoDS_Shape revolvedShape = MyCadGeom::RevolveShape(faceToRevolve, revolutionAxis, angle);

        if (!revolvedShape.IsNull())
        {
            Quantity_Color shapeColor = Quantity_NameOfColor::Quantity_NOC_YELLOW; // Default color
            if (!aisParentShape.IsNull())
            {
                Quantity_Color color;
                if (aisParentShape->HasColor())
                {                                 // Check if the AIS_Shape has a color set
                    aisParentShape->Color(color); // Get the color
                    shapeColor = color;
                }
            }

            // Potentially remove the original face/object or fuse, depending on design
            // For now, just add the new revolved shape.
            AddObjectToScene(revolvedShape, shapeColor, "RevolvedShape", true);

            // Clear selection after operation
            ClearAllContextSelections();
            m_selectedFacesList.Clear();
            m_parentShapeOfSelectedFaces.Nullify();

            UpdateWindowTitle(L"OCCT Viewer - Revolution completed.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Failed to create revolution. The face geometry may not be suitable or axis invalid.", L"Revolve Error", MB_OK | MB_ICONERROR);
            UpdateWindowTitle(L"OCCT Viewer - Revolution failed.");
        }
    }

    gp_Ax1 AppCore::GetRevolveAxisFromUser()
    {
        // For now, return a default Z-axis.
        // Future: Implement UI for edge selection or coordinate input.
        // For testing, you could also try an X-axis or Y-axis.
        MessageBoxW(m_hMainWnd, L"Using default Z-axis (0,0,1) at origin (0,0,0) for revolution. Edge selection for axis will be implemented later.",
                    L"Revolution Axis", MB_OK | MB_ICONINFORMATION);
        return gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
    }

    // Undo/Redo Implementation
    void AppCore::HandleUndo()
    {
        if (m_undoRedoManager && m_undoRedoManager->CanUndo())
        {
            m_undoRedoManager->Undo();
            UpdateWindowTitle(L"OCCT Viewer - " + MyCadUtils::StringToWString(m_undoRedoManager->GetUndoDescription()));
            RedrawView();
            UpdateModelTree(); // Update tree after undo
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Nothing to undo.", L"Undo", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleRedo()
    {
        if (m_undoRedoManager && m_undoRedoManager->CanRedo())
        {
            m_undoRedoManager->Redo();
            UpdateWindowTitle(L"OCCT Viewer - " + MyCadUtils::StringToWString(m_undoRedoManager->GetRedoDescription()));
            RedrawView();
            UpdateModelTree(); // Update tree after redo
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Nothing to redo.", L"Redo", MB_OK | MB_ICONINFORMATION);
        }
    }

    bool AppCore::CanUndo() const
    {
        return m_undoRedoManager && m_undoRedoManager->CanUndo();
    }

    bool AppCore::CanRedo() const
    {
        return m_undoRedoManager && m_undoRedoManager->CanRedo();
    }

    std::string AppCore::GetUndoDescription() const
    {
        if (m_undoRedoManager)
        {
            return m_undoRedoManager->GetUndoDescription();
        }
        return "Undo";
    }

    std::string AppCore::GetRedoDescription() const
    {
        if (m_undoRedoManager)
        {
            return m_undoRedoManager->GetRedoDescription();
        }
        return "Redo";
    }
    void AppCore::EnhanceEdgeVisibility(Handle(AIS_Shape) aisShape)
    {
        if (aisShape.IsNull())
            return;

        // Get or create the drawer
        Handle(Prs3d_Drawer) drawer = aisShape->Attributes();
        if (drawer.IsNull())
        {
            drawer = new Prs3d_Drawer();
            aisShape->SetAttributes(drawer);
        }

        // Set up edge display
        drawer->SetFaceBoundaryDraw(Standard_True);

        // Customize edge appearance
        Handle(Prs3d_LineAspect) edgeAspect = drawer->FaceBoundaryAspect();
        if (edgeAspect.IsNull())
        {
            edgeAspect = new Prs3d_LineAspect(Quantity_NOC_BLACK, Aspect_TOL_SOLID, 2.0);
            drawer->SetFaceBoundaryAspect(edgeAspect);
        }
        else
        {
            edgeAspect->SetColor(Quantity_NOC_BLACK);
            edgeAspect->SetWidth(2.0);
            edgeAspect->SetTypeOfLine(Aspect_TOL_SOLID);
        }

        // Update the display
        if (!m_context.IsNull())
        {
            m_context->Redisplay(aisShape, Standard_False);
        }
    }
    // Helper methods for undo/redo system
    int AppCore::AddObjectToSceneWithId(const TopoDS_Shape &shape, const Quantity_Color &color,
                                        const std::string &name, bool fitView, int forceId)
    {
        if (shape.IsNull() || m_context.IsNull())
            return -1;

        Handle(AIS_Shape) aisShape = new AIS_Shape(shape);
        SetupAisObjectDisplay(aisShape, color);

        // Display the shape
        m_context->Display(aisShape, Standard_False);

        // Activate selection modes - critical for edge selection
        m_context->Activate(aisShape, AIS_Shape::SelectionMode(TopAbs_EDGE), Standard_True); // Edge selection
        m_context->Activate(aisShape, AIS_Shape::SelectionMode(TopAbs_FACE), Standard_True); // Face selection
        m_context->Activate(aisShape, 0, Standard_True);                                     // Whole object selection

        // Set up edge visualization
        Handle(Prs3d_Drawer) drawer = aisShape->Attributes();
        if (drawer.IsNull())
        {
            drawer = new Prs3d_Drawer();
            aisShape->SetAttributes(drawer);
        }

        // Make edges more visible
        drawer->SetFaceBoundaryDraw(Standard_True);
        drawer->FaceBoundaryAspect()->SetWidth(2.0);
        drawer->FaceBoundaryAspect()->SetColor(Quantity_NOC_BLACK);

        // Enhance edge visibility for better selection
        EnhanceEdgeVisibility(aisShape);

        int objectId = (forceId != -1) ? forceId : SceneObject::nextId++;
        if (forceId != -1 && forceId >= SceneObject::nextId)
        {
            SceneObject::nextId = forceId + 1;
        }

        m_sceneObjects.emplace_back(shape, aisShape, name, color);
        m_sceneObjects.back().id = objectId;
        UpdateCurrentShapeForExport(shape);

        // Update model tree
        if (m_modelTreeManager)
        {
            m_modelTreeManager->AddObject(objectId, name, true); // Assume visible
        }

        if (fitView)
        {
            FitAll();
        }
        RedrawView(); // Redraw once after all changes

        return objectId;
    }

    std::optional<AppCore::ObjectData> AppCore::GetObjectData(int objectId) const
    {
        auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                               [objectId](const SceneObject &obj)
                               { return obj.id == objectId; });
        if (it != m_sceneObjects.end())
        {
            return ObjectData{it->shape, it->name, it->color};
        }
        return std::nullopt;
    }

    void AppCore::CaptureSceneState(SceneState &state) const
    {
        state.objects.clear();
        for (const auto &obj : m_sceneObjects)
        {
            // Check if object is currently displayed in the context
            bool isVisible = false;
            if (!m_context.IsNull() && !obj.aisObject.IsNull())
            {
                isVisible = m_context->IsDisplayed(obj.aisObject);
            }
            state.objects.emplace_back(obj.shape, obj.name, obj.color, obj.id, isVisible);
        }
        state.currentShapeToExport = m_currentShapeToExport;
        state.currentPlacementXOffset = m_currentPlacementXOffset;
        state.nextObjectId = SceneObject::nextId;
    }

    void AppCore::RestoreSceneState(const SceneState &state)
    {
        // Clear current scene robustly
        ClearSceneInternal(); // Use internal clear that also resets model tree

        // Restore objects
        for (const auto &objState : state.objects)
        {
            AddObjectToSceneWithId(objState.shape, objState.color, objState.name, false, objState.id);
            // Restore visibility state
            if (!objState.isVisible && !m_context.IsNull())
            {
                auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                                       [id = objState.id](const SceneObject &obj)
                                       { return obj.id == id; });
                if (it != m_sceneObjects.end() && !it->aisObject.IsNull())
                {
                    m_context->Erase(it->aisObject, Standard_False);
                }
            }
        }

        // Restore other state
        m_currentShapeToExport = state.currentShapeToExport;
        m_currentPlacementXOffset = state.currentPlacementXOffset;
        SceneObject::nextId = state.nextObjectId;

        UpdateModelTree(); // Rebuild model tree from restored scene
        RedrawView();
        FitAll();
    }

    void AppCore::ExecuteCommand(std::unique_ptr<ICommand> command)
    {
        if (m_undoRedoManager)
        {
            m_undoRedoManager->ExecuteCommand(std::move(command));
        }
    }

    void AppCore::ClearSceneInternal()
    {
        if (m_context.IsNull())
            return;

        // Erase all AIS objects from the context
        m_context->EraseAll(Standard_False); // Erase all objects

        m_sceneObjects.clear();
        m_selection1_IO.Nullify();
        m_selection2_IO.Nullify();
        m_selectedFacesList.Clear();
        m_parentShapeOfSelectedFaces.Nullify();
        m_currentShapeToExport.Nullify();
        m_currentPlacementXOffset = 0.0;
        SceneObject::nextId = 0;

        // Clear temporary interaction states
        m_selectedEdgesForFill.Clear();
        ClearEdgeHighlights();
        EndSplitMode();
        EndSweepSelectionMode();
        if (m_isSelectingMirrorFace && m_hMirrorDialog)
        {
            EndDialog(m_hMirrorDialog, IDCANCEL); // Close mirror dialog if open
        }
        m_isSelectingMirrorFace = false;
        m_hMirrorDialog = nullptr;

        // Clear model tree
        if (m_modelTreeManager)
        {
            m_modelTreeManager->ClearAll();
        }

        RedrawView();
        FitAll();
    }

    // ============================================================================
    // TRIM AND UNTRIM OPERATION HANDLERS
    // ============================================================================

    void AppCore::HandleTrimOperation()
    {
        if (m_selection1_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Simple Trim Operation:\n\n1. Select a shape to trim\n2. Click 'Trim' button\n3. The shape will be trimmed automatically\n\nFor shapes like cubes: trims to half size\nFor cylinders: trims height by half\nFor complex shapes: applies smart trimming", L"Trim Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        if (aisShape.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected item is not a valid shape for Trim.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Shape selectedShape = aisShape->Shape();
        TopoDS_Shape resultShape;

        // Apply smart trimming based on shape type and geometry
        if (selectedShape.ShapeType() == TopAbs_SOLID)
        {
            // For solids, try to trim with a plane through center
            gp_Pnt center = MyCadGeom::GetShapeCenter(selectedShape);
            gp_Pln cuttingPlane(center, gp_Dir(0, 0, 1)); // Horizontal cut through center
            resultShape = MyCadGeom::TrimShapeWithPlane(selectedShape, cuttingPlane, true);
        }
        else if (selectedShape.ShapeType() == TopAbs_FACE)
        {
            // For faces, try to trim to a smaller area
            resultShape = MyCadGeom::TrimFaceToHalf(TopoDS::Face(selectedShape));
        }
        else if (selectedShape.ShapeType() == TopAbs_EDGE)
        {
            // For edges, trim to half length
            resultShape = MyCadGeom::TrimCurveToHalf(TopoDS::Edge(selectedShape));
        }
        else
        {
            // For other shapes, try general trimming
            gp_Pnt center = MyCadGeom::GetShapeCenter(selectedShape);
            gp_Pln cuttingPlane(center, gp_Dir(1, 0, 0)); // Vertical cut through center
            resultShape = MyCadGeom::TrimShapeWithPlane(selectedShape, cuttingPlane, true);
        }

        if (!resultShape.IsNull() && !resultShape.IsSame(selectedShape))
        {
            // Create command for undo/redo support
            auto command = std::make_unique<SceneCommand>(this, "Smart Trim Operation");
            command->CaptureBeforeState();

            int originalObjectId = -1;
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                {
                    originalObjectId = obj.id;
                    break;
                }
            }

            command->SetOperation([this, resultShape, originalObjectId]()
                                  {
                if (originalObjectId != -1) {
                    RemoveObjectFromScene(originalObjectId); // Remove by ID
                }
                m_selection1_IO.Nullify(); // Nullify after potentially using it to find ID
                ClearAllContextSelections();
                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_ORANGE, "TrimmedShape", true); });

            command->CaptureAfterState();
            ExecuteCommand(std::move(command));

            UpdateWindowTitle(L"OCCT Viewer - Smart Trim Complete. Click to select.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Smart trim operation resulted in no change or an error.\nTry selecting a different shape or use advanced trim options.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleUntrimOperation()
    {
        if (m_selection1_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Please select a shape to untrim.", L"Untrim Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        if (aisShape.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected item is not a valid shape for Untrim.", L"Untrim Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Shape selectedShape = aisShape->Shape();
        TopoDS_Shape resultShape;

        // Try to untrim based on shape type
        if (selectedShape.ShapeType() == TopAbs_FACE)
        {
            resultShape = MyCadGeom::UntrimSurface(TopoDS::Face(selectedShape));
        }
        else if (selectedShape.ShapeType() == TopAbs_EDGE)
        {
            resultShape = MyCadGeom::UntrimCurve(TopoDS::Edge(selectedShape));
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Selected shape type is not supported for untrim operation.\nPlease select a face or edge.", L"Untrim Error", MB_OK | MB_ICONERROR);
            return;
        }

        if (!resultShape.IsNull() && !resultShape.IsSame(selectedShape))
        {
            auto command = std::make_unique<SceneCommand>(this, "Untrim Operation");
            command->CaptureBeforeState();

            int originalObjectId = -1;
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                {
                    originalObjectId = obj.id;
                    break;
                }
            }

            command->SetOperation([this, resultShape, originalObjectId]()
                                  {
                if (originalObjectId != -1) {
                    RemoveObjectFromScene(originalObjectId);
                }
                m_selection1_IO.Nullify();
                ClearAllContextSelections();
                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_CYAN1, "UntrimResult", true); });

            command->CaptureAfterState();
            ExecuteCommand(std::move(command));

            UpdateWindowTitle(L"OCCT Viewer - Untrim Complete. Click to select.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Untrim operation resulted in no change or an error.", L"Untrim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleSplitShapeOperation()
    {
        HandleInteractiveSplit();
    }

    void AppCore::HandleInteractiveSplit()
    {
        if (m_selection1_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Interactive Split Operation:\n\n1. Select a solid to split\n2. Click 'Split' to enter split mode\n3. Draw split line: Press LButton, drag, release\n4. The solid will be split\n\nPlease select a solid first.", L"Interactive Split Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        if (aisShape.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected item is not a valid shape for Split.", L"Split Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Shape selectedShape = aisShape->Shape();
        if (selectedShape.ShapeType() != TopAbs_SOLID)
        {
            MessageBoxW(m_hMainWnd, L"Please select a solid shape for splitting.\nCurrently only solids can be split interactively.", L"Split Error", MB_OK | MB_ICONERROR);
            return;
        }

        m_shapeToSplit = selectedShape;
        StartSplitMode();
    }

    void AppCore::StartSplitMode()
    {
        m_isInSplitMode = true;
        m_isDrawingSplitLine = false;  // Will be true during drag
        m_isDraggingSplitLine = false; // Will be true during drag
        m_hasSplitStartPoint = false;
        m_hasSplitEndPoint = false;
        ClearSplitPreview();   // Clear old line if any
        ClearRubberBandLine(); // Clear rubber band if any

        UpdateWindowTitle(L"OCCT Viewer - Split Mode: Drag mouse to draw split line. ESC to cancel.");
        MessageBoxW(m_hMainWnd, L"Split Mode Active!\n\nDraw the split line:\n1. Press and hold left mouse button\n2. Drag to draw the split line\n3. Release mouse button\n4. The solid will be split automatically\n\nPress ESC or Right-Click to cancel split mode.", L"Split Mode", MB_OK | MB_ICONINFORMATION);
    }

    void AppCore::EndSplitMode()
    {
        if (m_hMainWnd && GetCapture() == m_hMainWnd)
        { // Release capture if we had it
            ReleaseCapture();
        }
        m_isInSplitMode = false;
        m_isDrawingSplitLine = false;
        m_isDraggingSplitLine = false;
        m_hasSplitStartPoint = false;
        m_hasSplitEndPoint = false;
        m_shapeToSplit.Nullify();
        ClearSplitPreview();
        ClearRubberBandLine();

        UpdateWindowTitle(L"OCCT Viewer - Click to select.");
    }

    void AppCore::HandleSplitModeClick(int x, int y)
    {
        // This function is now effectively replaced by HandleSplitMouseDown, HandleSplitMouseMove, HandleSplitMouseUp
        // If called, it means a simple click happened without drag, which we don't use for split line.
        if (m_isInSplitMode)
        {
            MessageBoxW(m_hMainWnd, L"Please DRAG to draw a split line, not just click.", L"Split Mode Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::ShowSplitLinePreview()
    {
        if (!m_hasSplitStartPoint || !m_hasSplitEndPoint)
            return;
        ClearSplitPreview(); // Clear previous

        try
        {
            TopoDS_Edge splitEdge = BRepBuilderAPI_MakeEdge(m_splitStartPoint, m_splitEndPoint);
            if (!splitEdge.IsNull())
            {
                Handle(AIS_Shape) lineShape = new AIS_Shape(splitEdge);
                lineShape->SetColor(Quantity_NameOfColor::Quantity_NOC_RED);
                lineShape->SetWidth(3.0);
                lineShape->SetDisplayMode(0); // Wireframe

                if (!m_context.IsNull())
                {
                    m_context->Display(lineShape, Standard_False);
                    m_splitLinePreview = lineShape;
                    RedrawView();
                }
            }
        }
        catch (const Standard_Failure &e)
        {
            // Silently fail for preview
        }
    }

    void AppCore::ClearSplitPreview()
    {
        if (!m_splitLinePreview.IsNull() && !m_context.IsNull())
        {
            m_context->Erase(m_splitLinePreview, Standard_True); // Erase and update
            m_splitLinePreview.Nullify();
        }
    }

    void AppCore::ExecuteSplit()
    {
        if (!m_hasSplitStartPoint || !m_hasSplitEndPoint || m_shapeToSplit.IsNull())
        {
            EndSplitMode();
            return;
        }

        // Ensure the line is not too short
        if (m_splitStartPoint.Distance(m_splitEndPoint) < Precision::Confusion())
        {
            MessageBoxW(m_hMainWnd, L"Split line is too short. Please draw a longer line.", L"Split Info", MB_OK | MB_ICONINFORMATION);
            EndSplitMode();
            return;
        }

        TopTools_ListOfShape resultShapes = MyCadGeom::SplitSolidWithLine(m_shapeToSplit, m_splitStartPoint, m_splitEndPoint);

        if (!resultShapes.IsEmpty() && resultShapes.Size() > 0) // Even one part means original was modified or split failed but returned original
        {
            if (resultShapes.Size() == 1 && resultShapes.First().IsSame(m_shapeToSplit))
            {
                MessageBoxW(m_hMainWnd, L"Split operation resulted in no change. The split line might not intersect the solid.", L"Split Info", MB_OK | MB_ICONINFORMATION);
                EndSplitMode();
                return;
            }

            auto command = std::make_unique<SceneCommand>(this, "Interactive Split Operation");
            command->CaptureBeforeState();

            int originalObjectId = -1;
            for (const auto &obj : m_sceneObjects)
            {
                // Compare with the AIS_Shape holding m_shapeToSplit, which is m_selection1_IO at the start of split
                if (obj.aisObject == m_selection1_IO)
                {
                    originalObjectId = obj.id;
                    break;
                }
            }

            command->SetOperation([this, resultShapes, originalObjectId]()
                                  {
                if (originalObjectId != -1) {
                    RemoveObjectFromScene(originalObjectId);
                }
                // Important: Nullify m_selection1_IO as it pointed to the removed object
                Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
                if (!aisShape.IsNull() && aisShape->Shape().IsSame(m_shapeToSplit)) {
                    m_selection1_IO.Nullify();
                }
                ClearAllContextSelections();


                int partNumber = 1;
                for (TopTools_ListIteratorOfListOfShape it(resultShapes); it.More(); it.Next())
                {
                    std::string partName = "SplitPart" + std::to_string(partNumber++);
                    Quantity_NameOfColor color = (partNumber % 2 == 0) ? // Alternate colors
                        Quantity_NameOfColor::Quantity_NOC_YELLOW :
                        Quantity_NameOfColor::Quantity_NOC_LIGHTSKYBLUE; // Changed for better visibility
                    AddObjectToScene(it.Value(), color, partName, (partNumber == 2)); // Fit view after adding first part
                } });

            command->CaptureAfterState();
            ExecuteCommand(std::move(command));

            UpdateWindowTitle(L"OCCT Viewer - Interactive Split Complete. Click to select.");
            MessageBoxW(m_hMainWnd, L"Split operation completed successfully!", L"Split Complete", MB_OK | MB_ICONINFORMATION);
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Split operation failed or resulted in no parts.\nTry adjusting the split line position.", L"Split Info", MB_OK | MB_ICONINFORMATION);
        }

        EndSplitMode();
    }

    void AppCore::HandleOffsetOperation()
    {
        if (m_selection1_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Offset Operation:\n\n• Select a shape (face, edge, wire, or solid)\n• Click 'Offset' to open offset dialog\n• Choose offset value and direction\n• Positive values expand, negative values shrink\n• Uncheck 'Keep Original' to modify shape in-place\n\nPlease select a shape first.", L"Offset Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        HandleOffsetDialog();
    }

    void AppCore::HandleOffsetDialog()
    {
        Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        if (aisShape.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected item is not a valid shape for Offset.", L"Offset Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Shape selectedShape = aisShape->Shape();

        double offsetValue = 5.0;
        int offsetDirection = 0; // 0=outward, 1=inward, 2=both
        int offsetMode = 0;      // 0=skin, 1=pipe, 2=rectoverso
        int joinType = 2;        // 0=arc, 1=tangent, 2=intersection (default to sharp edges)
        bool keepOriginal = false;

        if (ShowOffsetDialog(offsetValue, offsetDirection, offsetMode, joinType, keepOriginal))
        {
            // Apply direction to offset value for single direction modes
            double actualOffsetValue = offsetValue; // Use positive for outward, negative for inward
            if (offsetDirection == 1)
            { // Inward
                actualOffsetValue = -std::abs(offsetValue);
            }
            else if (offsetDirection == 0)
            { // Outward
                actualOffsetValue = std::abs(offsetValue);
            }
            // For "Both" (offsetDirection == 2), we'll handle two separate calls.

            MyCadGeom::OffsetMode geomMode = static_cast<MyCadGeom::OffsetMode>(offsetMode);
            MyCadGeom::JoinType geomJoinType = static_cast<MyCadGeom::JoinType>(joinType);

            if (offsetDirection == 2)
            { // Both directions
                TopoDS_Shape outwardShape = MyCadGeom::PerformFreeCADOffset(selectedShape, std::abs(offsetValue), geomMode, geomJoinType);
                TopoDS_Shape inwardShape = MyCadGeom::PerformFreeCADOffset(selectedShape, -std::abs(offsetValue), geomMode, geomJoinType);

                bool outwardOk = !outwardShape.IsNull() && !outwardShape.IsSame(selectedShape);
                bool inwardOk = !inwardShape.IsNull() && !inwardShape.IsSame(selectedShape);

                if (outwardOk || inwardOk)
                {
                    auto command = std::make_unique<SceneCommand>(this, "Offset (Both Directions)");
                    command->CaptureBeforeState();

                    int originalObjectId = -1;
                    if (!keepOriginal)
                    {
                        for (const auto &obj : m_sceneObjects)
                        {
                            if (obj.aisObject == m_selection1_IO)
                            {
                                originalObjectId = obj.id;
                                break;
                            }
                        }
                    }

                    command->SetOperation([this, outwardShape, inwardShape, outwardOk, inwardOk, originalObjectId, keepOriginal]()
                                          {
                        if (keepOriginal) {
                            // Keep original and add both offset shapes as new objects
                            if (outwardOk) AddObjectToScene(outwardShape, Quantity_NameOfColor::Quantity_NOC_GREEN, "OffsetOutward", !inwardOk);
                            if (inwardOk) AddObjectToScene(inwardShape, Quantity_NameOfColor::Quantity_NOC_ORANGE, "OffsetInward", true);
                        } else {
                            // For "both directions" without keeping original, we need to choose one direction to replace the original
                            // and add the other as a new shape. Let's replace with outward and add inward as new.
                            if (originalObjectId != -1 && outwardOk) {
                                // Replace original with outward offset
                                auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                                    [originalObjectId](const SceneObject& obj) { return obj.id == originalObjectId; });

                                if (it != m_sceneObjects.end() && !it->aisObject.IsNull()) {
                                    Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(it->aisObject);
                                    if (!aisShape.IsNull()) {
                                        aisShape->Set(outwardShape);
                                        it->name = it->name + "_OffsetOut";

                                        // Update model tree if available
                                        if (m_modelTreeManager) {
                                            m_modelTreeManager->UpdateObjectName(originalObjectId, it->name);
                                        }

                                        if (!m_context.IsNull()) {
                                            m_context->Redisplay(aisShape, Standard_True);
                                        }
                                    }
                                }
                            } else if (originalObjectId != -1) {
                                // If outward failed but inward worked, remove original
                                RemoveObjectFromScene(originalObjectId);
                            }

                            // Add inward as new shape if it worked
                            if (inwardOk) AddObjectToScene(inwardShape, Quantity_NameOfColor::Quantity_NOC_ORANGE, "OffsetInward", true);
                        }

                        m_selection1_IO.Nullify();
                        ClearAllContextSelections(); });
                    command->CaptureAfterState();
                    ExecuteCommand(std::move(command));
                    UpdateWindowTitle(L"OCCT Viewer - Offset Complete (Both Directions)");
                }
                else
                {
                    MessageBoxW(m_hMainWnd, L"Offset operation (both directions) resulted in no change or an error.", L"Offset Info", MB_OK | MB_ICONINFORMATION);
                }
            }
            else
            { // Single direction
                TopoDS_Shape offsetShape = MyCadGeom::PerformFreeCADOffset(selectedShape, actualOffsetValue, geomMode, geomJoinType);
                bool offsetWorked = !offsetShape.IsNull() && !offsetShape.IsSame(selectedShape);

                if (offsetWorked)
                {
                    auto command = std::make_unique<SceneCommand>(this, "Offset Operation");
                    command->CaptureBeforeState();

                    int originalObjectId = -1;
                    if (!keepOriginal)
                    {
                        for (const auto &obj : m_sceneObjects)
                        {
                            if (obj.aisObject == m_selection1_IO)
                            {
                                originalObjectId = obj.id;
                                break;
                            }
                        }
                    }

                    command->SetOperation([this, offsetShape, actualOffsetValue, originalObjectId, keepOriginal]()
                                          {
                        if (keepOriginal) {
                            // Keep original and add offset as new shape
                            std::string offsetName = "OffsetShape_" + std::to_string(std::abs((int)actualOffsetValue));
                            Quantity_NameOfColor offsetColor = (actualOffsetValue > 0) ?
                                Quantity_NameOfColor::Quantity_NOC_GREEN : Quantity_NameOfColor::Quantity_NOC_ORANGE;
                            AddObjectToScene(offsetShape, offsetColor, offsetName, true);
                        } else {
                            // Replace the original shape with the offset shape (shrink/expand in place)
                            if (originalObjectId != -1) {
                                // Find the original object and replace its shape
                                auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                                    [originalObjectId](const SceneObject& obj) { return obj.id == originalObjectId; });

                                if (it != m_sceneObjects.end() && !it->aisObject.IsNull()) {
                                    // Update the AIS shape with the offset result
                                    Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(it->aisObject);
                                    if (!aisShape.IsNull()) {
                                        aisShape->Set(offsetShape);

                                        // Update the object name to reflect the offset
                                        std::string newName = it->name + "_Offset" + std::to_string(std::abs((int)actualOffsetValue));
                                        it->name = newName;

                                        // Update model tree if available
                                        if (m_modelTreeManager) {
                                            m_modelTreeManager->UpdateObjectName(originalObjectId, newName);
                                        }

                                        // Redisplay the updated shape
                                        if (!m_context.IsNull()) {
                                            m_context->Redisplay(aisShape, Standard_True);
                                        }
                                    }
                                }
                            }
                        }

                        m_selection1_IO.Nullify();
                        ClearAllContextSelections(); });
                    command->CaptureAfterState();
                    ExecuteCommand(std::move(command));
                    UpdateWindowTitle(L"OCCT Viewer - Offset Complete");
                }
                else
                {
                    MessageBoxW(m_hMainWnd, L"Offset operation resulted in no change or an error.", L"Offset Info", MB_OK | MB_ICONINFORMATION);
                }
            }
        }
    }

    void AppCore::HandleThickSolidOperation()
    {
        if (m_selection1_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Thick Solid Operation:\n\n• Select a solid shape\n• Creates hollow thick-walled version\n• Like making a shell or container\n\nPlease select a solid first.", L"Thick Solid Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        if (aisShape.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected item is not a valid shape for Thick Solid.", L"Thick Solid Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Shape selectedShape = aisShape->Shape();

        if (selectedShape.ShapeType() != TopAbs_SOLID)
        {
            MessageBoxW(m_hMainWnd, L"Thick Solid operation requires a solid shape.\nPlease select a solid (cube, cylinder, etc.)", L"Thick Solid Error", MB_OK | MB_ICONERROR);
            return;
        }

        double thickness = -2.0; // Negative for inward thickness (hollow)
        // Potentially use DlgProcThicknessLocal for user input
        // HINSTANCE hInstance = GetModuleHandle(NULL);
        // INT_PTR dlgResult = DialogBoxParamW(hInstance, MAKEINTRESOURCEW(IDD_THICKNESS_DIALOG), m_hMainWnd, DlgProcThicknessLocal, (LPARAM)&thickness);
        // if (dlgResult != IDOK) return;

        TopoDS_Shape thickShape = MyCadGeom::CreateThickSolid(selectedShape, TopTools_ListOfShape(), thickness); // Empty list means all faces contribute to thickness

        if (!thickShape.IsNull() && !thickShape.IsSame(selectedShape))
        {
            auto command = std::make_unique<SceneCommand>(this, "Thick Solid Operation");
            command->CaptureBeforeState();

            int originalObjectId = -1;
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                {
                    originalObjectId = obj.id;
                    break;
                }
            }

            command->SetOperation([this, thickShape, originalObjectId]()
                                  {
                 if (originalObjectId != -1) { // Optionally remove original
                    // RemoveObjectFromScene(originalObjectId);
                 }
                m_selection1_IO.Nullify();
                ClearAllContextSelections();
                AddObjectToScene(thickShape, Quantity_NameOfColor::Quantity_NOC_PURPLE, "ThickSolid", true); });

            command->CaptureAfterState();
            ExecuteCommand(std::move(command));

            UpdateWindowTitle(L"OCCT Viewer - Thick Solid Complete");
            MessageBoxW(m_hMainWnd, L"Thick solid created successfully!\n\nThe solid now has hollow walls like a container.", L"Thick Solid Complete", MB_OK | MB_ICONINFORMATION);
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Thick solid operation failed.\n\nThis can happen if:\n• Shape is too complex\n• Thickness value is too large\n• Shape has invalid geometry", L"Thick Solid Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    // ============================================================================
    // MOUSE DRAG HANDLING FOR SPLIT LINE DRAWING
    // ============================================================================

    void AppCore::HandleSplitMouseDown(int x, int y)
    {
        if (!m_isInSplitMode)
            return;

        m_isDraggingSplitLine = true; // Mark that dragging has started
        m_isDrawingSplitLine = true;  // We are now in the process of drawing
        m_dragStartX = x;
        m_dragStartY = y;
        m_dragCurrentX = x; // Initialize current with start
        m_dragCurrentY = y;

        m_splitStartPoint = ScreenToWorldPrecise(x, y); // Use precise conversion
        m_hasSplitStartPoint = true;

        if (m_hMainWnd)
        {
            SetCapture(m_hMainWnd); // Capture mouse for smooth dragging
        }

        if (m_useRubberBandLine)
        {
            CreateRubberBandLine(m_dragStartX, m_dragStartY, m_dragCurrentX, m_dragCurrentY);
        }
        else
        {
            UpdateSplitLinePreview(m_dragStartX, m_dragStartY, m_dragCurrentX, m_dragCurrentY);
        }
        UpdateWindowTitle(L"OCCT Viewer - Drawing split line... Release mouse to split");
    }

    void AppCore::HandleSplitMouseMove(int x, int y)
    {
        if (!m_isInSplitMode || !m_isDraggingSplitLine)
            return;

        m_dragCurrentX = x;
        m_dragCurrentY = y;

        if (m_useRubberBandLine)
        {
            UpdateRubberBandLine(x, y);
        }
        else
        {
            UpdateSplitLinePreview(m_dragStartX, m_dragStartY, x, y);
        }
        // No need to update window title here continuously, it flickers.
        // RedrawView(); // RubberBandLine updates viewer itself, or UpdateSplitLinePreview does.
    }

    void AppCore::HandleSplitMouseUp(int x, int y)
    {
        if (!m_isInSplitMode || !m_isDraggingSplitLine)
            return;

        if (m_hMainWnd)
        {
            ReleaseCapture(); // Release mouse capture
        }

        m_isDraggingSplitLine = false;
        m_isDrawingSplitLine = false;

        m_splitEndPoint = ScreenToWorldPrecise(x, y); // Use precise conversion
        m_hasSplitEndPoint = true;

        if (m_useRubberBandLine)
        {
            ClearRubberBandLine();
        }
        else
        {
            ClearSplitPreview(); // Clear the temporary preview line
        }

        // Check if the line is valid (not just a click)
        int deltaX = std::abs(x - m_dragStartX);
        int deltaY = std::abs(y - m_dragStartY);
        double minDragDistance = 5.0; // Minimum pixel distance for a drag to be considered valid

        if (m_splitStartPoint.Distance(m_splitEndPoint) < Precision::Confusion() || (deltaX < minDragDistance && deltaY < minDragDistance))
        {
            MessageBoxW(m_hMainWnd, L"Split line too short or just a click. Please drag to create a valid split line.", L"Split Info", MB_OK | MB_ICONINFORMATION);
            m_hasSplitStartPoint = false; // Reset for next attempt
            m_hasSplitEndPoint = false;
            EndSplitMode(); // Exit split mode as it was not a valid drag
            return;
        }

        ShowSplitLinePreview(); // Show the final chosen line briefly (optional)
        ExecuteSplit();         // This will also call EndSplitMode()
    }

    void AppCore::UpdateSplitLinePreview(int startX, int startY, int endX, int endY)
    {
        ClearSplitPreview(); // Clear existing preview first

        try
        {
            gp_Pnt start3D = ScreenToWorldPrecise(startX, startY);
            gp_Pnt end3D = ScreenToWorldPrecise(endX, endY);

            if (start3D.Distance(end3D) < Precision::Confusion() * 10)
            { // Avoid creating zero-length edges
                return;
            }

            BRepBuilderAPI_MakeEdge edgeMaker(start3D, end3D);
            if (edgeMaker.IsDone() && !edgeMaker.Edge().IsNull())
            {
                TopoDS_Edge edge = edgeMaker.Edge();
                Handle(AIS_Shape) lineShape = new AIS_Shape(edge);

                lineShape->SetColor(Quantity_NameOfColor::Quantity_NOC_YELLOW); // Bright color for preview
                lineShape->SetWidth(2.0);
                lineShape->SetDisplayMode(0); // Wireframe for lines

                if (!m_context.IsNull())
                {
                    m_context->Display(lineShape, Standard_True); // Display and update immediately
                    m_splitLinePreview = lineShape;
                }
            }
        }
        catch (const Standard_Failure &e)
        {
            // Silently fail for preview update during drag
        }
    }

    gp_Pnt AppCore::ScreenToWorld(int screenX, int screenY) // Kept for compatibility, but precise is preferred
    {
        if (m_view.IsNull())
        {
            // Basic fallback if view is not available
            return gp_Pnt(static_cast<Standard_Real>(screenX) / 10.0,
                          static_cast<Standard_Real>(-screenY) / 10.0, 0.0);
        }

        Standard_Real worldX, worldY, worldZ;
        m_view->Convert(screenX, screenY, worldX, worldY, worldZ);

        // For a simple projection onto Z=0 plane in world coordinates:
        // This requires understanding how the view's projection is set up.
        // A more robust method is ScreenToWorldPrecise which uses ray casting.

        // Simplistic approach: project onto a plane parallel to screen at some depth.
        // Let's assume Z=0 for this generic version.
        // The Convert method gives a point on the view plane. To get a point in world,
        // we need to unproject. A common method is to cast a ray.

        // For now, let's project onto the Z=0 plane of the view's coordinate system.
        // This is not a true world Z=0 unless the view is aligned.
        // A better ScreenToWorld would involve constructing a ray.
        gp_Pnt viewPlanePoint(worldX, worldY, worldZ);
        // This point is on the "front" plane of the view.
        // To get a point on a specific world plane (e.g. Z=0), more complex calculations are needed.
        // For split line, ScreenToWorldPrecise is used.
        return viewPlanePoint;
    }

    // ============================================================================
    // PROFESSIONAL CAD-STYLE RUBBER BAND LINE (like AutoCAD/SolidWorks)
    // ============================================================================

    void AppCore::CreateRubberBandLine(int startX, int startY, int endX, int endY)
    {
        ClearRubberBandLine(); // Clear any existing one
        try
        {
            gp_Pnt start3D = ScreenToWorldPrecise(startX, startY);
            gp_Pnt end3D = ScreenToWorldPrecise(endX, endY);

            if (start3D.Distance(end3D) < Precision::Confusion() * 10)
                return;

            BRepBuilderAPI_MakeEdge edgeMaker(start3D, end3D);
            if (edgeMaker.IsDone() && !edgeMaker.Edge().IsNull())
            {
                TopoDS_Edge edge = edgeMaker.Edge();
                Handle(AIS_Shape) lineShape = new AIS_Shape(edge);

                lineShape->SetColor(Quantity_NameOfColor::Quantity_NOC_YELLOW);
                lineShape->SetWidth(1.5);     // Thin, crisp line
                lineShape->SetDisplayMode(0); // Wireframe for lines

                Handle(Prs3d_Drawer) drawer = lineShape->Attributes();
                if (drawer.IsNull())
                {
                    drawer = new Prs3d_Drawer();
                    lineShape->SetAttributes(drawer);
                }
                Handle(Prs3d_LineAspect) lineAspect = drawer->LineAspect();
                if (lineAspect.IsNull())
                {
                    lineAspect = new Prs3d_LineAspect(Quantity_NameOfColor::Quantity_NOC_YELLOW, Aspect_TOL_DASH, 1.5);
                }
                else
                {
                    lineAspect->SetColor(Quantity_NameOfColor::Quantity_NOC_YELLOW);
                    lineAspect->SetTypeOfLine(Aspect_TOL_DASH); // Dashed line
                    lineAspect->SetWidth(1.5);
                }
                lineShape->SetAttributes(drawer); // Re-set drawer if modified

                if (!m_context.IsNull())
                {
                    // Display with highest priority (always on top) is harder with AIS_Shape directly.
                    // For true on-top drawing, one might need to use V3d_View::Draw cośtam or WNT_GraphicDevice::SetLineAttributes.
                    // For now, standard display.
                    m_context->Display(lineShape, Standard_False); // Display, update separately if needed
                    m_rubberBandLine = lineShape;
                    m_context->UpdateCurrentViewer(); // Force update for rubber band
                }
            }
        }
        catch (const Standard_Failure &e)
        {
            // Silently fail
        }
    }

    void AppCore::UpdateRubberBandLine(int endX, int endY)
    {
        // A common fast way is to remove the old and add new, though not super efficient.
        // For true rubber banding, you'd draw directly to overlay.
        // Given AIS, re-creating is simpler.
        CreateRubberBandLine(m_dragStartX, m_dragStartY, endX, endY); // Re-creates with new end point
    }

    void AppCore::ClearRubberBandLine()
    {
        if (!m_rubberBandLine.IsNull() && !m_context.IsNull())
        {
            m_context->Erase(m_rubberBandLine, Standard_False); // Erase, update separately
            m_rubberBandLine.Nullify();
            m_context->UpdateCurrentViewer(); // Force update
        }
    }

    gp_Pnt AppCore::ScreenToWorldPrecise(int screenX, int screenY)
    {
        if (m_view.IsNull())
        {
            return gp_Pnt(static_cast<Standard_Real>(screenX) / 10.0, static_cast<Standard_Real>(-screenY) / 10.0, 0.0);
        }

        Standard_Real worldX, worldY, worldZ;
        // This converts screen pixel (screenX, screenY) to a point (worldX, worldY, worldZ) on the view's front plane.
        m_view->Convert(screenX, screenY, worldX, worldY, worldZ);
        gp_Pnt pointOnViewPlane(worldX, worldY, worldZ);

        // Create a ray from camera eye through this pointOnViewPlane
        gp_Pnt eye;
        gp_Vec dir, up, side;
        eye = m_view->Camera()->Eye();
        dir = m_view->Camera()->Direction();
        up = m_view->Camera()->Up();

        // The direction of the ray is from the eye to the pointOnViewPlane
        gp_Dir ray_dir(pointOnViewPlane.X() - eye.X(), pointOnViewPlane.Y() - eye.Y(), pointOnViewPlane.Z() - eye.Z());
        gp_Lin ray(eye, ray_dir);

        // Try to intersect with the m_shapeToSplit if it exists
        if (!m_shapeToSplit.IsNull())
        {
            gp_Pnt intersectionPoint;
            if (FindRayShapeIntersectionPrecise(ray, m_shapeToSplit, intersectionPoint))
            {
                return intersectionPoint;
            }
        }

        // Fallback: If no shape to split or no intersection, project onto a plane.
        // Project onto a plane that is Z=0 in world coordinates.
        // The plane is defined by P.N=0, where N=(0,0,1) so Pz=0.
        // Ray: R(t) = Eye + t * RayDir
        // (Eye.z + t * RayDir.z) = 0  => t = -Eye.z / RayDir.z
        if (std::abs(ray_dir.Z()) > Precision::Angular())
        { // Avoid division by zero if ray is parallel to XY plane
            Standard_Real t = -eye.Z() / ray_dir.Z();
            if (t > 0)
            { // Intersection in front of the camera
                return eye.Translated(ray_dir.XYZ() * t);
            }
        }

        // Further fallback: if ray parallel to XY or intersection is behind, return point on view plane.
        // Or, if we have a scene bounding box, project onto its center Z.
        Bnd_Box sceneBox = GetSceneBoundingBox();
        if (!sceneBox.IsVoid())
        {
            Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
            sceneBox.Get(xmin, ymin, zmin, xmax, ymax, zmax);
            Standard_Real centerZ = (zmin + zmax) / 2.0;
            if (std::abs(ray_dir.Z()) > Precision::Angular())
            {
                Standard_Real t = (centerZ - eye.Z()) / ray_dir.Z();
                if (t > 0)
                {
                    return eye.Translated(ray_dir.XYZ() * t);
                }
            }
        }

        return pointOnViewPlane; // Last resort
    }

    bool AppCore::FindRayShapeIntersection(const gp_Lin &ray, const TopoDS_Shape &shape, gp_Pnt &intersectionPoint)
    {
        // This is a simplified version. FindRayShapeIntersectionPrecise is more robust.
        // For this simplified version, we'll just try BRepExtrema_DistShapeShape with a segment.
        try
        {
            gp_Pnt rayStart = ray.Location();
            gp_Dir rayDir = ray.Direction();
            // Create a long segment along the ray
            gp_Pnt rayEnd = rayStart.Translated(gp_Vec(rayDir.XYZ()) * 10000.0); // A large distance
            BRepBuilderAPI_MakeEdge rayEdgeMaker(rayStart, rayEnd);
            if (!rayEdgeMaker.IsDone())
                return false;
            TopoDS_Edge rayEdge = rayEdgeMaker.Edge();

            BRepExtrema_DistShapeShape distCalc(rayEdge, shape);
            if (distCalc.IsDone() && distCalc.NbSolution() > 0)
            {
                Standard_Real minDist = Precision::Infinite();
                Standard_Integer bestSol = -1;
                for (Standard_Integer i = 1; i <= distCalc.NbSolution(); ++i)
                {
                    if (distCalc.Value() < minDist && distCalc.Value() < Precision::Confusion())
                    { // Check if it's an actual intersection
                        minDist = distCalc.Value();
                        bestSol = i;
                    }
                }
                if (bestSol != -1)
                {
                    intersectionPoint = distCalc.PointOnShape2(bestSol); // Point on the target shape
                    return true;
                }
            }
        }
        catch (const Standard_Failure &)
        {
            return false;
        }
        return false;
    }

    bool AppCore::Get3DPointFromPicking(int screenX, int screenY, gp_Pnt &point3D)
    {
        // This function is largely superseded by ScreenToWorldPrecise for split line drawing.
        // However, it can be useful for general picking on a shape's surface.
        if (m_view.IsNull() || m_context.IsNull())
            return false;

        m_context->MoveTo(screenX, screenY, m_view, Standard_False);
        if (m_context->HasDetected())
        {
            Handle(SelectMgr_EntityOwner) owner = m_context->DetectedOwner();
            if (!owner.IsNull())
            { // Use fallback method for 3D point detection
                // Fallback to ScreenToWorldPrecise logic for ray casting
                point3D = ScreenToWorldPrecise(screenX, screenY);
                return true;
            }
            // If no specific point, try to project onto the detected shape if it's the one we care about
            if (!m_shapeToSplit.IsNull() && owner->Selectable()->IsKind(STANDARD_TYPE(AIS_Shape)))
            {
                Handle(AIS_Shape) detectedAisShape = Handle(AIS_Shape)::DownCast(owner->Selectable());
                if (detectedAisShape->Shape().IsSame(m_shapeToSplit))
                {
                    // Fallback to ScreenToWorldPrecise logic for ray casting
                    point3D = ScreenToWorldPrecise(screenX, screenY);
                    return true; // Assume ScreenToWorldPrecise found a point on the shape or a plane
                }
            }
        }
        // Fallback if nothing specific detected
        point3D = ScreenToWorldPrecise(screenX, screenY);
        return true;
    }

    bool AppCore::ProjectCursorOntoShape(int screenX, int screenY, const TopoDS_Shape &shape, gp_Pnt &projectedPoint)
    {
        // This is a more specific version of Get3DPointFromPicking.
        if (m_view.IsNull())
            return false;

        Standard_Real worldX, worldY, worldZ;
        m_view->Convert(screenX, screenY, worldX, worldY, worldZ);
        gp_Pnt pointOnViewPlane(worldX, worldY, worldZ);

        gp_Pnt eye;
        gp_Vec dir; // At - Eye
        gp_Vec up;
        eye = m_view->Camera()->Eye();
        dir = m_view->Camera()->Direction();
        up = m_view->Camera()->Up();

        gp_Dir ray_dir(pointOnViewPlane.X() - eye.X(), pointOnViewPlane.Y() - eye.Y(), pointOnViewPlane.Z() - eye.Z());
        gp_Lin ray(eye, ray_dir);

        return FindRayShapeIntersectionPrecise(ray, shape, projectedPoint);
    }

    bool AppCore::FindRayShapeIntersectionPrecise(const gp_Lin &ray, const TopoDS_Shape &shape, gp_Pnt &intersectionPoint)
    {
        // Using BRepIntCurveSurface_Inter from OCCT samples is more advanced.
        // For now, BRepExtrema_DistShapeShape with a long edge segment is a common approach.
        try
        {
            gp_Pnt rayStart = ray.Location();
            gp_Dir rayDir = ray.Direction();
            // Create a very long segment along the ray to simulate infinity
            // Max scene dimension could be used here for a better length.
            Standard_Real rayLength = 1.0e6; // A very large number
            gp_Pnt rayEnd = rayStart.Translated(gp_Vec(rayDir.XYZ()) * rayLength);

            BRepBuilderAPI_MakeEdge rayEdgeMaker(rayStart, rayEnd);
            if (!rayEdgeMaker.IsDone() || rayEdgeMaker.Edge().IsNull())
                return false;
            TopoDS_Edge rayEdge = rayEdgeMaker.Edge();

            // Use BRepExtrema_ShapeProximity for finding closest points which can indicate intersection
            BRepExtrema_DistShapeShape distCalc(rayEdge, shape);

            if (distCalc.IsDone() && distCalc.NbSolution() > 0)
            {
                Standard_Real currentMinDistToRayOrigin = Precision::Infinite();
                bool found = false;

                for (Standard_Integer i = 1; i <= distCalc.NbSolution(); ++i)
                {
                    // We need the point on 'shape' (Shape2) that is closest to 'rayEdge' (Shape1).
                    // And this point on 'rayEdge' should ideally be very close to 'shape'.
                    if (distCalc.Value() < Precision::Confusion())
                    { // Actual intersection or very close
                        gp_Pnt pOnShape = distCalc.PointOnShape2(i);
                        // Check if this point is "in front" of the ray origin
                        gp_Vec vecToPoint(rayStart, pOnShape);
                        if (vecToPoint.Dot(gp_Vec(rayDir)) >= 0)
                        { // Point is not behind ray origin
                            Standard_Real distToOrigin = rayStart.Distance(pOnShape);
                            if (distToOrigin < currentMinDistToRayOrigin)
                            {
                                currentMinDistToRayOrigin = distToOrigin;
                                intersectionPoint = pOnShape;
                                found = true;
                            }
                        }
                    }
                }
                return found;
            }
        }
        catch (const Standard_Failure &)
        {
            return false; // Exception during calculation
        }
        return false;
    }

    // ============================================================================
    // PROFESSIONAL OFFSET DIALOG IMPLEMENTATION
    // ============================================================================
    // Static members must be defined outside the class if they are to be used by a static dialog proc
    // However, it's better to pass `this` as lParam and store it in HWND's user data.
    // For simplicity here, if ShowOffsetDialog is always called on the same AppCore instance,
    // a static AppCore pointer in the DlgProc can work, but it's not ideal for multiple AppCore instances.
    // The provided DlgProc uses a static struct pointer initialized via lParam, which is better.

    bool AppCore::ShowOffsetDialog(double &offsetValue, int &offsetDirection, int &offsetMode, int &joinType, bool &keepOriginal)
    {
        struct OffsetDialogStaticData
        { // Data to pass to the dialog
            double val;
            int dir;
            int mode;
            int join;
            bool keep;
        } dialogStaticData;

        dialogStaticData.val = offsetValue;
        dialogStaticData.dir = offsetDirection;
        dialogStaticData.mode = offsetMode;
        dialogStaticData.join = joinType;
        dialogStaticData.keep = keepOriginal;

        INT_PTR result = DialogBoxParam(GetModuleHandle(NULL),
                                        MAKEINTRESOURCE(IDD_OFFSET_DIALOG),
                                        m_hMainWnd,
                                        OffsetDialogProc, // This DlgProc needs to be static or a global function
                                        reinterpret_cast<LPARAM>(&dialogStaticData));

        if (result == IDOK)
        {
            offsetValue = dialogStaticData.val;
            offsetDirection = dialogStaticData.dir;
            offsetMode = dialogStaticData.mode;
            joinType = dialogStaticData.join;
            keepOriginal = dialogStaticData.keep;
            return true;
        }
        return false;
    }

    INT_PTR CALLBACK AppCore::OffsetDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
    {
        // Define a structure to hold dialog data if needed, or use static variables carefully.
        // For this example, we'll use a struct passed via lParam at WM_INITDIALOG.
        struct OffsetDialogStaticData
        {
            double val;
            int dir;
            int mode;
            int join;
            bool keep;
        };
        static OffsetDialogStaticData *pData = nullptr;

        switch (message)
        {
        case WM_INITDIALOG:
        {
            pData = reinterpret_cast<OffsetDialogStaticData *>(lParam);
            if (!pData)
                return FALSE; // Should not happen

            SetDlgItemText(hDlg, IDC_EDIT_OFFSET_VALUE, std::to_wstring(pData->val).c_str());

            if (pData->dir == 0)
                CheckRadioButton(hDlg, IDC_RADIO_OFFSET_OUTWARD, IDC_RADIO_OFFSET_BOTH, IDC_RADIO_OFFSET_OUTWARD);
            else if (pData->dir == 1)
                CheckRadioButton(hDlg, IDC_RADIO_OFFSET_OUTWARD, IDC_RADIO_OFFSET_BOTH, IDC_RADIO_OFFSET_INWARD);
            else
                CheckRadioButton(hDlg, IDC_RADIO_OFFSET_OUTWARD, IDC_RADIO_OFFSET_BOTH, IDC_RADIO_OFFSET_BOTH);

            HWND hCombo = GetDlgItem(hDlg, IDC_COMBO_OFFSET_MODE);
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Skin - Surface offset");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Pipe - Pipe/tube creation");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"RectoVerso - Both sides");
            SendMessage(hCombo, CB_SETCURSEL, pData->mode, 0);

            // Populate the join type combo box
            HWND hJoinCombo = GetDlgItem(hDlg, IDC_COMBO_JOIN_TYPE);
            SendMessage(hJoinCombo, CB_ADDSTRING, 0, (LPARAM)L"Arc (Rounded Edges)");
            SendMessage(hJoinCombo, CB_ADDSTRING, 0, (LPARAM)L"Tangent (Smooth Transitions)");
            SendMessage(hJoinCombo, CB_ADDSTRING, 0, (LPARAM)L"Sharp (Keep Sharp Edges)");
            SendMessage(hJoinCombo, CB_SETCURSEL, pData->join, 0);

            CheckDlgButton(hDlg, IDC_CHECK_KEEP_ORIGINAL_OFFSET, pData->keep ? BST_CHECKED : BST_UNCHECKED);

            SetFocus(GetDlgItem(hDlg, IDC_EDIT_OFFSET_VALUE));
            SendDlgItemMessage(hDlg, IDC_EDIT_OFFSET_VALUE, EM_SETSEL, 0, -1);
            return FALSE;
        }

        case WM_COMMAND:
            if (!pData)
                return FALSE; // Should be set from WM_INITDIALOG

            switch (LOWORD(wParam))
            {
            case IDOK:
            {
                wchar_t buffer[100];
                GetDlgItemText(hDlg, IDC_EDIT_OFFSET_VALUE, buffer, 100);
                try
                {
                    pData->val = std::stod(buffer);
                    if (pData->val <= 0 && !(IsDlgButtonChecked(hDlg, IDC_RADIO_OFFSET_BOTH) == BST_CHECKED))
                    { // Allow 0 for "Both" if needed, but typically >0
                        // For "Both", the magnitude is used. For single direction, sign might matter.
                        // The PerformFreeCADOffset handles positive/negative for direction.
                        // Let's enforce positive here for clarity for the user.
                        MessageBoxW(hDlg, L"Offset value must be positive.", L"Input Error", MB_OK | MB_ICONERROR);
                        return TRUE;
                    }
                }
                catch (...)
                {
                    MessageBoxW(hDlg, L"Invalid offset value.", L"Input Error", MB_OK | MB_ICONERROR);
                    return TRUE;
                }

                if (IsDlgButtonChecked(hDlg, IDC_RADIO_OFFSET_OUTWARD))
                    pData->dir = 0;
                else if (IsDlgButtonChecked(hDlg, IDC_RADIO_OFFSET_INWARD))
                    pData->dir = 1;
                else if (IsDlgButtonChecked(hDlg, IDC_RADIO_OFFSET_BOTH))
                    pData->dir = 2;

                HWND hCombo = GetDlgItem(hDlg, IDC_COMBO_OFFSET_MODE);
                pData->mode = (int)SendMessage(hCombo, CB_GETCURSEL, 0, 0);
                if (pData->mode == CB_ERR)
                    pData->mode = 0; // Default to first if error

                HWND hJoinCombo = GetDlgItem(hDlg, IDC_COMBO_JOIN_TYPE);
                pData->join = (int)SendMessage(hJoinCombo, CB_GETCURSEL, 0, 0);
                if (pData->join == CB_ERR)
                    pData->join = 2; // Default to sharp edges if error

                pData->keep = (IsDlgButtonChecked(hDlg, IDC_CHECK_KEEP_ORIGINAL_OFFSET) == BST_CHECKED);

                EndDialog(hDlg, IDOK);
                return TRUE;
            }
            case IDCANCEL:
                EndDialog(hDlg, IDCANCEL);
                return TRUE;
                // Add EN_CHANGE for IDC_EDIT_OFFSET_VALUE for real-time validation if desired
            }
            break;
        }
        return FALSE;
    }

    // Enhanced specialized trim handlers
    void AppCore::HandleTrimSurfaceWithCurve()
    {
        if (m_selection1_IO.IsNull() || m_selection2_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Please select a surface (or shape containing a face) and then a curve (or shape containing an edge/wire) for Trim Surface with Curve operation (Ctrl+Click).\nFirst selection: Surface. Second selection: Curve.", L"Trim Surface with Curve Info", MB_OK | MB_ICONINFORMATION);
            return;
        }

        Handle(AIS_Shape) aisShape1 = Handle(AIS_Shape)::DownCast(m_selection1_IO); // Expected Surface
        Handle(AIS_Shape) aisShape2 = Handle(AIS_Shape)::DownCast(m_selection2_IO); // Expected Curve/Wire

        if (aisShape1.IsNull() || aisShape2.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected items are not valid shapes.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Face surfaceToTrim;
        TopExp_Explorer exp1(aisShape1->Shape(), TopAbs_FACE);
        if (exp1.More())
            surfaceToTrim = TopoDS::Face(exp1.Current());
        else
        {
            MessageBoxW(m_hMainWnd, L"First selection does not contain a face.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }

        TopoDS_Wire trimmingWire;
        TopExp_Explorer exp2_wire(aisShape2->Shape(), TopAbs_WIRE);
        if (exp2_wire.More())
        {
            trimmingWire = TopoDS::Wire(exp2_wire.Current());
        }
        else
        {
            TopExp_Explorer exp2_edge(aisShape2->Shape(), TopAbs_EDGE);
            if (exp2_edge.More())
            {
                BRepBuilderAPI_MakeWire wireMaker;
                for (; exp2_edge.More(); exp2_edge.Next())
                {
                    wireMaker.Add(TopoDS::Edge(exp2_edge.Current()));
                }
                if (wireMaker.IsDone())
                    trimmingWire = wireMaker.Wire();
            }
        }
        if (trimmingWire.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Second selection does not contain a usable wire or edges.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }

        // Ask user if they want to keep the part of the surface "inside" or "outside" the projected curve.
        // For simplicity, default to keeping "inside" (true).
        bool keepInside = true; // MyCadGeom::TrimSurfaceWithCurve should handle this parameter.
        TopoDS_Shape resultShape = MyCadGeom::TrimSurfaceWithCurve(surfaceToTrim, trimmingWire, keepInside);

        if (!resultShape.IsNull() && !resultShape.IsSame(surfaceToTrim)) // Check against original surface
        {
            auto command = std::make_unique<SceneCommand>(this, "Trim Surface with Curve");
            command->CaptureBeforeState();

            int id1 = -1, id2 = -1; // IDs of the original selected objects
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                    id1 = obj.id;
                if (obj.aisObject == m_selection2_IO)
                    id2 = obj.id;
            }

            command->SetOperation([this, resultShape, id1, id2]()
                                  {
                if (id1 != -1) RemoveObjectFromScene(id1); // Remove original surface object
                // Keep trimming curve object (id2) unless design dictates otherwise
                // if (id2 != -1) RemoveObjectFromScene(id2);

                m_selection1_IO.Nullify();
                m_selection2_IO.Nullify();
                ClearAllContextSelections();
                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_PAPAYAWHIP, "TrimmedSurface", true); });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Trim Surface with Curve Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Trim surface with curve operation resulted in no change or an error.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleTrimSurfaceWithSurface()
    {
        if (m_selection1_IO.IsNull() || m_selection2_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Please select two surfaces (or shapes containing faces) for Trim Surface with Surface operation (Ctrl+Click).\nFirst surface will be trimmed by the second.", L"Trim Surface Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        Handle(AIS_Shape) aisShape1 = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        Handle(AIS_Shape) aisShape2 = Handle(AIS_Shape)::DownCast(m_selection2_IO);

        if (aisShape1.IsNull() || aisShape2.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected items are not valid shapes.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }
        TopoDS_Face surfaceToTrim, trimmingSurface;
        TopExp_Explorer exp1(aisShape1->Shape(), TopAbs_FACE);
        if (exp1.More())
            surfaceToTrim = TopoDS::Face(exp1.Current());
        else
        { /* error */
            return;
        }
        TopExp_Explorer exp2(aisShape2->Shape(), TopAbs_FACE);
        if (exp2.More())
            trimmingSurface = TopoDS::Face(exp2.Current());
        else
        { /* error */
            return;
        }

        TopoDS_Shape resultShape = MyCadGeom::TrimSurfaceWithSurface(surfaceToTrim, trimmingSurface);

        if (!resultShape.IsNull() && !resultShape.IsSame(surfaceToTrim))
        {
            auto command = std::make_unique<SceneCommand>(this, "Trim Surface with Surface");
            command->CaptureBeforeState();
            int id1 = -1, id2 = -1;
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                    id1 = obj.id;
                if (obj.aisObject == m_selection2_IO)
                    id2 = obj.id;
            }
            command->SetOperation([this, resultShape, id1, id2]()
                                  {
                if (id1 != -1) RemoveObjectFromScene(id1);
                // if (id2 != -1) RemoveObjectFromScene(id2); // Optionally remove trimming tool
                m_selection1_IO.Nullify(); m_selection2_IO.Nullify(); ClearAllContextSelections();
                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_PEACHPUFF, "TrimmedSurfaceBySurface", true); });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Trim Surface with Surface Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Trim surface with surface operation resulted in no change or an error.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleTrimCurveWithPoints()
    {
        // This would require an interactive mode similar to Fill Edge Selection or Split Line.
        // 1. Select a curve.
        // 2. Enter "Trim Curve Points" mode.
        // 3. Click two points ON the curve.
        // 4. Perform trim.
        MessageBoxW(m_hMainWnd, L"Interactive Curve Trimming with Points:\n\n1. Select a curve (edge).\n2. (Future) Click this button again to enter point selection mode.\n3. (Future) Click two points ON the curve.\n4. The curve will be trimmed.\n\nThis feature is planned for a future update.", L"Trim Curve with Points Info", MB_OK | MB_ICONINFORMATION);
    }

    void AppCore::HandleTrimShapeWithPlane()
    {
        if (m_selection1_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Please select a shape to trim with a plane.", L"Trim Shape with Plane Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        Handle(AIS_Shape) aisShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        if (aisShape.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected item is not a valid shape.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }
        TopoDS_Shape selectedShape = aisShape->Shape();

        // For now, use a default plane. Future: Dialog to define plane.
        // Example: XY plane (Z=0), keep positive Z side.
        gp_Pln cuttingPlane(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1)); // XY plane
        bool keepPositiveSide = true;

        TopoDS_Shape resultShape = MyCadGeom::TrimShapeWithPlane(selectedShape, cuttingPlane, keepPositiveSide);

        if (!resultShape.IsNull() && !resultShape.IsSame(selectedShape))
        {
            auto command = std::make_unique<SceneCommand>(this, "Trim Shape with Plane");
            command->CaptureBeforeState();
            int idToRemove = -1;
            for (const auto &obj : m_sceneObjects)
                if (obj.aisObject == m_selection1_IO)
                    idToRemove = obj.id;
            command->SetOperation([this, resultShape, idToRemove]()
                                  {
                if (idToRemove != -1) RemoveObjectFromScene(idToRemove);
                m_selection1_IO.Nullify(); ClearAllContextSelections();
                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_TAN, "PlaneTrimmedShape", true); });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Trim with Plane Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Trim with plane operation resulted in no change or an error.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    // ============================================================================
    // ENHANCED INTERACTIVE TRIMMING OPERATIONS
    // ============================================================================

    void AppCore::HandleInteractiveCurveTrim()
    {
        MessageBoxW(m_hMainWnd, L"Interactive Curve Trim Mode (Advanced):\n\nThis feature will allow selecting multiple curves and trimming them against each other dynamically.\n\nComing in a future update!", L"Interactive Curve Trim", MB_OK | MB_ICONINFORMATION);
    }

    void AppCore::HandleMultiSegmentTrim()
    {
        if (m_selection1_IO.IsNull() || !m_selection1_IO->IsKind(STANDARD_TYPE(AIS_Shape)))
        {
            MessageBoxW(m_hMainWnd, L"Please select a curve (edge) for multi-segment trimming.", L"Multi-Segment Trim Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        Handle(AIS_Shape) aisCurveShape = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        TopoDS_Edge curveToTrim;
        TopExp_Explorer exp(aisCurveShape->Shape(), TopAbs_EDGE);
        if (exp.More())
            curveToTrim = TopoDS::Edge(exp.Current());
        else
        {
            MessageBoxW(m_hMainWnd, L"Selected shape does not contain an edge.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }

        // For demonstration, let's define 2 split points, resulting in 3 segments.
        // These parameters would typically come from user interaction (clicking on the curve).
        Standard_Real u1, u2;
        Handle(Geom_Curve) gc = BRep_Tool::Curve(curveToTrim, u1, u2);
        if (gc.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Cannot get curve geometry.", L"Trim Error", MB_OK | MB_ICONERROR);
            return;
        }
        std::vector<Standard_Real> parameters;
        parameters.push_back(u1 + (u2 - u1) * 0.33); // First split point at 1/3rd length
        parameters.push_back(u1 + (u2 - u1) * 0.66); // Second split point at 2/3rd length
        // Ensure parameters are within curve bounds and sorted, MyCadGeom::TrimCurveMultipleSegments should handle this.

        TopTools_ListOfShape resultSegments = MyCadGeom::TrimCurveMultipleSegments(curveToTrim, parameters);

        if (!resultSegments.IsEmpty())
        {
            auto command = std::make_unique<SceneCommand>(this, "Multi-Segment Trim");
            command->CaptureBeforeState();
            int idToRemove = -1;
            for (const auto &obj : m_sceneObjects)
                if (obj.aisObject == m_selection1_IO)
                    idToRemove = obj.id;

            command->SetOperation([this, resultSegments, idToRemove]()
                                  {
                if (idToRemove != -1) RemoveObjectFromScene(idToRemove);
                m_selection1_IO.Nullify(); ClearAllContextSelections();
                int segNum = 1;
                for (TopTools_ListIteratorOfListOfShape it(resultSegments); it.More(); it.Next(), ++segNum) {
                    AddObjectToScene(it.Value(), (segNum % 2 == 0 ? Quantity_NameOfColor::Quantity_NOC_GOLD : Quantity_NameOfColor::Quantity_NOC_KHAKI),
                                     "Segment" + std::to_string(segNum), (segNum == 1));
                } });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Multi-Segment Trim Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Multi-segment trim operation resulted in no segments or an error.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleTrimAtIntersection()
    {
        if (m_selection1_IO.IsNull() || m_selection2_IO.IsNull() ||
            !m_selection1_IO->IsKind(STANDARD_TYPE(AIS_Shape)) || !m_selection2_IO->IsKind(STANDARD_TYPE(AIS_Shape)))
        {
            MessageBoxW(m_hMainWnd, L"Please select two curves (edges) for Trim at Intersection (Ctrl+Click).", L"Trim Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        Handle(AIS_Shape) aisShape1 = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        Handle(AIS_Shape) aisShape2 = Handle(AIS_Shape)::DownCast(m_selection2_IO);
        TopoDS_Edge curve1, curve2;
        TopExp_Explorer exp1(aisShape1->Shape(), TopAbs_EDGE);
        if (exp1.More())
            curve1 = TopoDS::Edge(exp1.Current());
        else
            return;
        TopExp_Explorer exp2(aisShape2->Shape(), TopAbs_EDGE);
        if (exp2.More())
            curve2 = TopoDS::Edge(exp2.Current());
        else
            return;

        std::vector<gp_Pnt> intersections = MyCadGeom::FindCurveCurveIntersections(curve1, curve2);
        if (intersections.empty())
        {
            MessageBoxW(m_hMainWnd, L"No intersections found between selected curves.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        // For simplicity, use the first intersection point.
        // A more advanced version might let user pick which intersection or trim all.
        gp_Pnt intersectionPoint = intersections[0];
        bool keepFirstSegment = true; // Example: keep segment from start of curve to intersection.
                                      // MyCadGeom::TrimCurvesAtIntersection should take this as param.

        TopTools_ListOfShape resultSegments = MyCadGeom::TrimCurvesAtIntersection(curve1, curve2, intersectionPoint, keepFirstSegment);

        if (!resultSegments.IsEmpty())
        {
            auto command = std::make_unique<SceneCommand>(this, "Trim at Intersection");
            command->CaptureBeforeState();
            int id1 = -1, id2 = -1;
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                    id1 = obj.id;
                if (obj.aisObject == m_selection2_IO)
                    id2 = obj.id;
            }
            command->SetOperation([this, resultSegments, id1, id2]()
                                  {
                if (id1 != -1) RemoveObjectFromScene(id1);
                if (id2 != -1) RemoveObjectFromScene(id2);
                m_selection1_IO.Nullify(); m_selection2_IO.Nullify(); ClearAllContextSelections();
                int segNum = 1;
                for (TopTools_ListIteratorOfListOfShape it(resultSegments); it.More(); it.Next(), ++segNum) {
                     AddObjectToScene(it.Value(), (segNum % 2 == 0 ? Quantity_NameOfColor::Quantity_NOC_SALMON : Quantity_NameOfColor::Quantity_NOC_SIENNA),
                                     "IntersectTrimSeg" + std::to_string(segNum), (segNum == 1));
                } });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Trim at Intersection Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Trim at intersection operation resulted in no segments or an error.", L"Trim Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleAdvancedTrimDialog()
    {
        MessageBoxW(m_hMainWnd, L"Advanced Trim Dialog (Placeholder):\n\nThis will allow selection of trim methods, keep/discard options, and interactive previews.\n\nFeature under development.", L"Advanced Trim", MB_OK | MB_ICONINFORMATION);
    }

    void AppCore::SetTrimMode(TrimMode mode)
    {
        m_currentTrimMode = mode;
        m_isInTrimMode = (mode != TRIM_MODE_NORMAL);

        m_trimPoints.clear();
        m_selectedCurves.clear(); // Assuming this is a TopTools_ListOfShape or similar
        ClearTrimPreview();

        std::wstring modeText = L"Normal Mode";
        if (m_isInTrimMode)
        {
            switch (mode)
            {
            case TRIM_MODE_INTERACTIVE_CURVE:
                modeText = L"Interactive Curve Trim Mode";
                break;
            case TRIM_MODE_MULTI_SEGMENT:
                modeText = L"Multi-Segment Trim Mode: Select curve, then points";
                break;
            case TRIM_MODE_AT_INTERSECTION:
                modeText = L"Trim at Intersection Mode: Select two curves";
                break;
            default:
                break;
            }
            // Potentially configure selection modes here for the specific trim mode
        }
        else
        {
            // RestoreNormalSelection(); // If selection modes were changed
        }
        UpdateWindowTitle((L"OCCT Viewer - " + modeText).c_str());
    }

    void AppCore::HandleTrimModeClick(int x, int y) // Called from LButtonDown if m_isInTrimMode
    {
        if (!m_isInTrimMode || m_context.IsNull())
            return;

        // Example logic for TRIM_MODE_MULTI_SEGMENT (conceptual)
        if (m_currentTrimMode == TRIM_MODE_MULTI_SEGMENT)
        {
            m_context->MoveTo(x, y, m_view, Standard_False);
            if (m_context->HasDetected())
            {
                Handle(SelectMgr_EntityOwner) owner = m_context->DetectedOwner();
                if (owner && owner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
                {
                    Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(owner);
                    if (brepOwner->HasShape())
                    {
                        TopoDS_Shape detectedShape = brepOwner->Shape();
                        if (m_selectedCurves.empty() && detectedShape.ShapeType() == TopAbs_EDGE)
                        {
                            m_selectedCurves.push_back(TopoDS::Edge(detectedShape)); // Select the curve to trim
                            UpdateWindowTitle(L"Curve selected. Now click points ON the curve to segment.");
                        }
                        else if (!m_selectedCurves.empty())
                        {
                            // A curve is selected, now collecting points on it
                            // Use fallback method for point detection
                            // Project clicked position onto curve to get parameter
                            // Add parameter to a list.
                            // When enough points, or user confirms, call MyCadGeom::TrimCurveMultipleSegments
                            UpdateWindowTitle(L"Point selected on curve. Click more or confirm.");
                        }
                    }
                }
            }
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Click handling for this trim mode is not yet implemented.", L"Trim Mode", MB_OK | MB_ICONINFORMATION);
        }
        RedrawView();
    }

    void AppCore::ShowTrimPreview()
    {
        if (!m_showTrimPreview)
            return;         // If preview flag is off
        ClearTrimPreview(); // Clear old preview

        // Example: If m_selectedCurves and m_trimPoints have data, generate preview shapes
        // TopoDS_Shape previewShape = MyCadGeom::GenerateTrimPreview(m_selectedCurves, m_trimPoints);
        // if (!previewShape.IsNull()) {
        //     Handle(AIS_Shape) aisPreview = new AIS_Shape(previewShape);
        //     aisPreview->SetColor(Quantity_NameOfColor::Quantity_NOC_YELLOW); // Preview color
        //     aisPreview->SetDisplayMode(0); // Wireframe
        //     m_context->Display(aisPreview, Standard_False);
        //     m_trimPreviewObject = aisPreview;
        //     m_trimPreviewShapes.Append(previewShape); // Store if needed
        // }
        // RedrawView();
    }

    void AppCore::ClearTrimPreview()
    {
        if (!m_trimPreviewObject.IsNull() && !m_context.IsNull())
        {
            m_context->Erase(m_trimPreviewObject, Standard_False);
            m_trimPreviewObject.Nullify();
        }
        m_trimPreviewShapes.Clear(); // Assuming this is a TopTools_ListOfShape
        // RedrawView(); // Caller might redraw
    }

    void AppCore::HandleJoinOperation() // Fuse operation
    {
        if (m_selection1_IO.IsNull() || m_selection2_IO.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Please select two shapes for Join/Fuse operation (Ctrl+Click).", L"Join Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        Handle(AIS_Shape) aisShape1 = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        Handle(AIS_Shape) aisShape2 = Handle(AIS_Shape)::DownCast(m_selection2_IO);

        if (aisShape1.IsNull() || aisShape2.IsNull())
        {
            MessageBoxW(m_hMainWnd, L"Selected items are not valid shapes.", L"Join Error", MB_OK | MB_ICONERROR);
            return;
        }
        TopoDS_Shape shape1 = aisShape1->Shape();
        TopoDS_Shape shape2 = aisShape2->Shape();

        TopoDS_Shape resultShape = MyCadGeom::FuseShapes(shape1, shape2); // Using FuseShapes from GeometryOperations

        if (!resultShape.IsNull() && !resultShape.IsSame(shape1) && !resultShape.IsSame(shape2))
        {
            auto command = std::make_unique<SceneCommand>(this, "Join/Fuse Operation");
            command->CaptureBeforeState();
            int id1 = -1, id2 = -1;
            for (const auto &obj : m_sceneObjects)
            {
                if (obj.aisObject == m_selection1_IO)
                    id1 = obj.id;
                if (obj.aisObject == m_selection2_IO)
                    id2 = obj.id;
            }
            command->SetOperation([this, resultShape, id1, id2]()
                                  {
                if (id1 != -1) RemoveObjectFromScene(id1);
                if (id2 != -1) RemoveObjectFromScene(id2);
                m_selection1_IO.Nullify(); m_selection2_IO.Nullify(); ClearAllContextSelections();
                AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_SPRINGGREEN, "FusedShape", true); });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Join/Fuse Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Join/Fuse operation resulted in no change or an error.", L"Join Info", MB_OK | MB_ICONINFORMATION);
        }
    }

    void AppCore::HandleMirrorOperation()
    {
        if (m_selection1_IO.IsNull() || !m_selection1_IO->IsKind(STANDARD_TYPE(AIS_Shape)))
        {
            MessageBoxW(m_hMainWnd, L"Please select a shape to mirror (Ctrl+Click).", L"Mirror Info", MB_OK | MB_ICONINFORMATION);
            return;
        }
        Handle(AIS_Shape) aisToMirror = Handle(AIS_Shape)::DownCast(m_selection1_IO);
        TopoDS_Shape selectedShape = aisToMirror->Shape();

        int mirrorPlaneType = MIRROR_XY_PLANE; // Default
        bool keepOriginal = true;
        // m_selectedMirrorFace is a member variable to be set by the dialog interaction if custom face is chosen.
        m_selectedMirrorFace.Nullify(); // Clear from previous attempts

        if (!ShowMirrorDialog(mirrorPlaneType, keepOriginal, m_selectedMirrorFace))
        {           // Pass reference to member
            return; // User cancelled dialog
        }

        TopoDS_Shape mirroredShape;
        std::string opName = "Mirror";
        gp_Ax2 mirrorPlaneSystem; // Used by GeometryOperations::MirrorShape

        Standard_Real spacing = 5.0; // Default spacing between original and mirrored objects

        switch (mirrorPlaneType)
        {
        case MIRROR_XY_PLANE:
            mirroredShape = MyCadGeom::MirrorShapeAcrossXYPlaneWithSpacing(selectedShape, spacing);
            opName = "Mirror across XY plane (side-by-side)";
            break;
        case MIRROR_XZ_PLANE:
            mirroredShape = MyCadGeom::MirrorShapeAcrossXZPlaneWithSpacing(selectedShape, spacing);
            opName = "Mirror across XZ plane (side-by-side)";
            break;
        case MIRROR_YZ_PLANE:
            mirroredShape = MyCadGeom::MirrorShapeAcrossYZPlaneWithSpacing(selectedShape, spacing);
            opName = "Mirror across YZ plane (side-by-side)";
            break;
        case MIRROR_CUSTOM_FACE:
            if (m_selectedMirrorFace.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"No custom face selected for mirroring.", L"Mirror Error", MB_OK | MB_ICONERROR);
                return;
            }
            // Use enhanced custom face mirror with spacing for side-by-side placement
            mirroredShape = MyCadGeom::MirrorShapeAcrossFaceWithSpacing(selectedShape, m_selectedMirrorFace, spacing);
            opName = "Mirror across custom face (side-by-side)";
            goto process_mirror_result; // Skip to result processing
        default:
            MessageBoxW(m_hMainWnd, L"Unknown mirror plane type.", L"Mirror Error", MB_OK | MB_ICONERROR);
            return;
        }

    process_mirror_result:
        if (!mirroredShape.IsNull() && !mirroredShape.IsSame(selectedShape))
        {
            auto command = std::make_unique<SceneCommand>(this, opName);
            command->CaptureBeforeState();
            int idToRemove = -1;
            if (!keepOriginal)
            {
                for (const auto &obj : m_sceneObjects)
                    if (obj.aisObject == m_selection1_IO)
                        idToRemove = obj.id;
            }
            command->SetOperation([this, mirroredShape, keepOriginal, idToRemove]()
                                  {
                // Enhanced mirror: Always keep original for side-by-side comparison (like FreeCAD)
                // Only remove original if explicitly requested by user
                if (!keepOriginal && idToRemove != -1) {
                    RemoveObjectFromScene(idToRemove);
                }

                // Clear selection but keep original object visible
                ClearAllContextSelections();

                // Add mirrored shape with distinctive color for easy identification
                AddObjectToScene(mirroredShape, Quantity_NameOfColor::Quantity_NOC_SLATEBLUE, "MirroredShape", true);

                // Fit view to show both original and mirrored objects
                if (!m_view.IsNull()) {
                    m_view->FitAll();
                } });
            command->CaptureAfterState();
            ExecuteCommand(std::move(command));
            UpdateWindowTitle(L"OCCT Viewer - Mirror Complete.");
        }
        else
        {
            MessageBoxW(m_hMainWnd, L"Mirror operation resulted in no change or an error.", L"Mirror Info", MB_OK | MB_ICONINFORMATION);
        }
        m_selectedMirrorFace.Nullify(); // Clean up after use
    }

    bool AppCore::ShowMirrorDialog(int &mirrorPlaneTypeOut, bool &keepOriginalOut, TopoDS_Face &selectedFaceOut)
    {
        struct MirrorDialogData
        { // To pass AppCore instance and get results
            AppCore *pAppCore;
            int planeType;
            bool keepOriginal;
            TopoDS_Face selectedFace; // For custom plane
            bool customFaceSelectedSuccessfully;
        };

        MirrorDialogData data;
        data.pAppCore = this;
        data.planeType = MIRROR_XY_PLANE; // Default
        data.keepOriginal = true;         // Default
        data.customFaceSelectedSuccessfully = false;

        INT_PTR result = DialogBoxParam(GetModuleHandle(NULL),
                                        MAKEINTRESOURCE(IDD_MIRROR_DIALOG),
                                        m_hMainWnd,
                                        MirrorDialogProc, // Static or global
                                        reinterpret_cast<LPARAM>(&data));
        if (result == IDOK)
        {
            mirrorPlaneTypeOut = data.planeType;
            keepOriginalOut = data.keepOriginal;
            if (data.planeType == MIRROR_CUSTOM_FACE && data.customFaceSelectedSuccessfully)
            {
                selectedFaceOut = data.selectedFace;
            }
            else if (data.planeType == MIRROR_CUSTOM_FACE && !data.customFaceSelectedSuccessfully)
            {
                MessageBoxW(m_hMainWnd, L"Custom face was chosen but no face was selected successfully.", L"Mirror Warning", MB_OK | MB_ICONWARNING);
                return false; // Invalid state
            }
            return true;
        }
        return false;
    }

    INT_PTR CALLBACK AppCore::MirrorDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
    {
        struct MirrorDialogData
        { // Must match the one in ShowMirrorDialog
            AppCore *pAppCore;
            int planeType;
            bool keepOriginal;
            TopoDS_Face selectedFace;
            bool customFaceSelectedSuccessfully;
        };
        static MirrorDialogData *pData = nullptr; // Store pointer passed in WM_INITDIALOG

        switch (message)
        {
        case WM_INITDIALOG:
            pData = reinterpret_cast<MirrorDialogData *>(lParam);
            if (!pData || !pData->pAppCore)
                return FALSE;

            pData->pAppCore->m_hMirrorDialog = hDlg; // Store dialog handle in AppCore for updates

            CheckRadioButton(hDlg, IDC_RADIO_XY_PLANE, IDC_RADIO_CUSTOM_PLANE, IDC_RADIO_XY_PLANE);
            CheckDlgButton(hDlg, IDC_CHECK_KEEP_ORIGINAL, BST_CHECKED);
            EnableWindow(GetDlgItem(hDlg, IDC_BUTTON_SELECT_FACE), FALSE);
            SetDlgItemText(hDlg, IDC_STATIC_SELECTED_FACE, L"Selected Face: None");
            return TRUE;

        case WM_COMMAND:
            if (!pData)
                return TRUE;
            switch (LOWORD(wParam))
            {
            case IDC_RADIO_XY_PLANE:
                pData->planeType = MIRROR_XY_PLANE;
                EnableWindow(GetDlgItem(hDlg, IDC_BUTTON_SELECT_FACE), FALSE);
                SetDlgItemText(hDlg, IDC_STATIC_SELECTED_FACE, L"Selected Face: None");
                pData->customFaceSelectedSuccessfully = false;
                return TRUE;
            case IDC_RADIO_XZ_PLANE:
                pData->planeType = MIRROR_XZ_PLANE;
                EnableWindow(GetDlgItem(hDlg, IDC_BUTTON_SELECT_FACE), FALSE);
                SetDlgItemText(hDlg, IDC_STATIC_SELECTED_FACE, L"Selected Face: None");
                pData->customFaceSelectedSuccessfully = false;
                return TRUE;
            case IDC_RADIO_YZ_PLANE:
                pData->planeType = MIRROR_YZ_PLANE;
                EnableWindow(GetDlgItem(hDlg, IDC_BUTTON_SELECT_FACE), FALSE);
                SetDlgItemText(hDlg, IDC_STATIC_SELECTED_FACE, L"Selected Face: None");
                pData->customFaceSelectedSuccessfully = false;
                return TRUE;
            case IDC_RADIO_CUSTOM_PLANE:
                pData->planeType = MIRROR_CUSTOM_FACE;
                EnableWindow(GetDlgItem(hDlg, IDC_BUTTON_SELECT_FACE), TRUE);
                return TRUE;

            case IDC_BUTTON_SELECT_FACE:
                pData->pAppCore->m_isSelectingMirrorFace = true; // Signal AppCore to expect face selection
                // Hide dialog temporarily or instruct user
                ShowWindow(hDlg, SW_HIDE); // Hide dialog to allow 3D view interaction
                MessageBoxW(pData->pAppCore->m_hMainWnd,
                            L"Mirror dialog hidden.\nPlease SHIFT+Click a single FACE in the 3D view to use as the mirror plane, then click OK in the re-shown dialog.",
                            L"Select Mirror Face", MB_OK | MB_ICONINFORMATION);
                // AppCore's LButtonDown will need to check m_isSelectingMirrorFace
                return TRUE;

            case IDC_CHECK_KEEP_ORIGINAL:
                pData->keepOriginal = (IsDlgButtonChecked(hDlg, IDC_CHECK_KEEP_ORIGINAL) == BST_CHECKED);
                return TRUE;

            case IDOK:
                if (pData->planeType == MIRROR_CUSTOM_FACE && !pData->customFaceSelectedSuccessfully)
                {
                    MessageBoxW(hDlg, L"Custom plane selected, but no face was chosen. Please select a face or choose another plane type.", L"Mirror Error", MB_OK | MB_ICONWARNING);
                    return TRUE; // Prevent dialog closing
                }
                EndDialog(hDlg, IDOK);
                return TRUE;
            case IDCANCEL:
                EndDialog(hDlg, IDCANCEL);
                return TRUE;
            }
            break;
        case WM_USER_FACE_SELECTED_FOR_MIRROR: // Custom message from AppCore after face is picked
            if (pData && pData->pAppCore)
            {
                pData->selectedFace = pData->pAppCore->m_selectedMirrorFace; // Get face from AppCore
                pData->customFaceSelectedSuccessfully = !pData->selectedFace.IsNull();
                SetDlgItemText(hDlg, IDC_STATIC_SELECTED_FACE,
                               pData->customFaceSelectedSuccessfully ? L"Selected Face: Acquired" : L"Selected Face: Failed/None");
                ShowWindow(hDlg, SW_SHOW); // Re-show dialog
                SetForegroundWindow(hDlg);
            }
            return TRUE;

        case WM_CLOSE:
            EndDialog(hDlg, IDCANCEL);
            return TRUE;
        case WM_DESTROY:
            pData->pAppCore->m_hMirrorDialog = nullptr;
            pData->pAppCore->m_isSelectingMirrorFace = false;
            break; // Cleanup
        }
        return FALSE;
    }

    void AppCore::HandleSelectMirrorFace() // This is called from LButtonDown if m_isSelectingMirrorFace
    {
        if (!m_isSelectingMirrorFace)
            return;

        if (!m_selectedFacesList.IsEmpty() && m_selectedFacesList.Extent() == 1)
        {
            m_selectedMirrorFace = TopoDS::Face(m_selectedFacesList.First());
            MessageBoxW(m_hMainWnd, L"Face selected for mirror plane. The mirror dialog will now reappear.", L"Mirror Face Selected", MB_OK | MB_ICONINFORMATION);
        }
        else
        {
            m_selectedMirrorFace.Nullify();
            MessageBoxW(m_hMainWnd, L"No single face selected, or multiple faces selected. Please SHIFT+Click exactly one face.", L"Mirror Face Selection Error", MB_OK | MB_ICONWARNING);
        }
        m_isSelectingMirrorFace = false; // Mode finished
        ClearAllContextSelections();     // Clear the selection used for picking the face

        if (m_hMirrorDialog)
        { // If dialog is still valid
            PostMessage(m_hMirrorDialog, WM_USER_FACE_SELECTED_FOR_MIRROR, 0, 0);
        }
    }

    void AppCore::HandleSweepOperation()
    {
        if (!m_isInSweepSelectionMode)
        {
            StartSweepSelectionMode();
        }
        else
        {
            if (m_hasSweepProfile && m_hasSweepPath)
            {
                try
                {
                    TopoDS_Shape resultShape = MyCadGeom::SweepProfileAlongPath(m_sweepProfile, m_sweepPath);
                    if (!resultShape.IsNull())
                    {
                        auto command = std::make_unique<SceneCommand>(this, "Sweep Operation");
                        command->CaptureBeforeState();
                        // Get IDs of profile and path objects if they are separate scene objects
                        // For simplicity, assume they are not necessarily top-level objects to be removed unless specified
                        command->SetOperation([this, resultShape]()
                                              {
                            // Decide if profile/path should be removed. For now, let's keep them.
                            // Or, remove the objects m_sweepProfile and m_sweepPath were part of.
                            // This part needs careful design depending on whether profile/path are sub-shapes or full objects.
                            // For now, just add result.
                            AddObjectToScene(resultShape, Quantity_NameOfColor::Quantity_NOC_THISTLE, "SweepResult", true); });
                        command->CaptureAfterState();
                        ExecuteCommand(std::move(command));
                        MessageBoxW(m_hMainWnd, L"Sweep operation completed successfully!", L"Sweep Complete", MB_OK | MB_ICONINFORMATION);
                    }
                    else
                    {
                        MessageBoxW(m_hMainWnd, L"Sweep operation failed. Result was null.", L"Sweep Error", MB_OK | MB_ICONERROR);
                    }
                }
                catch (const Standard_Failure &e)
                {
                    MessageBoxA(m_hMainWnd, e.GetMessageString(), "Sweep Exception", MB_OK | MB_ICONERROR);
                }
                catch (...)
                {
                    MessageBoxW(m_hMainWnd, L"Sweep operation threw an unknown exception.", L"Sweep Error", MB_OK | MB_ICONERROR);
                }
                EndSweepSelectionMode(); // End mode regardless of success/failure after attempt
            }
            else
            {
                std::wstring message = L"Sweep Selection Mode Active\n\n";
                message += L"Profile (Face): ";
                message += (m_hasSweepProfile ? L"✓ Selected" : L"✗ Click on a FACE");
                message += L"\nPath (Edge/Wire): ";
                message += (m_hasSweepPath ? L"✓ Selected" : L"✗ Click on an EDGE or WIRE");
                message += L"\n\nClick 'Sweep' again when both are selected.\nPress ESC or Right-Click to cancel.";
                MessageBoxW(m_hMainWnd, message.c_str(), L"Sweep Mode", MB_OK | MB_ICONINFORMATION);
            }
        }
    }

    void AppCore::StartSweepSelectionMode()
    {
        m_isInSweepSelectionMode = true;
        m_hasSweepProfile = false;
        m_hasSweepPath = false;
        m_sweepProfile.Nullify();
        m_sweepPath.Nullify();

        ClearAllContextSelections(); // Clear general selections
        // No special selection mode configuration needed, as user picks face then edge.
        UpdateWindowTitle(L"OCCT Viewer - Sweep Mode: Click on a FACE for profile. ESC/RClick to cancel.");
        MessageBoxW(m_hMainWnd, L"Sweep Selection Mode Started!\n\nStep 1: Click on a FACE (profile)\nStep 2: Click on an EDGE or WIRE (path)\nStep 3: Click 'Sweep' button again\n\nPress ESC or Right-Click to cancel", L"Sweep Mode", MB_OK | MB_ICONINFORMATION);
    }

    void AppCore::EndSweepSelectionMode()
    {
        m_isInSweepSelectionMode = false;
        // Don't nullify profile/path here if an operation was attempted.
        // ClearAllContextSelections(); // Maybe not, if user wants to see selected profile/path.
        UpdateWindowTitle(L"OCCT Viewer - Click to select");
    }

    void AppCore::HandleSweepModeClick(int x, int y) // Called from LButtonDown
    {
        if (!m_isInSweepSelectionMode || m_context.IsNull())
            return;

        m_context->MoveTo(x, y, m_view, Standard_False);
        if (m_context->HasDetected())
        {
            Handle(SelectMgr_EntityOwner) owner = m_context->DetectedOwner();
            if (owner && owner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
            {
                Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(owner);
                if (brepOwner->HasShape())
                {
                    TopoDS_Shape subShape = brepOwner->Shape();
                    std::wstring msg;
                    bool selectionChanged = false;

                    if (!m_hasSweepProfile)
                    { // Expecting Profile (Face)
                        if (subShape.ShapeType() == TopAbs_FACE)
                        {
                            m_sweepProfile = subShape;
                            m_hasSweepProfile = true;
                            m_context->ClearSelected(Standard_False); // Clear previous before selecting new
                            m_context->AddOrRemoveSelected(brepOwner, Standard_True);
                            msg = L"Profile face selected! Now click on an EDGE or WIRE for the sweep path.";
                            UpdateWindowTitle(L"OCCT Viewer - Sweep: Profile ✓, Click Path (Edge/Wire)");
                            selectionChanged = true;
                        }
                        else
                        {
                            msg = L"Expecting a FACE for the profile. Clicked on a " + MyCadUtils::ShapeTypeToString(subShape.ShapeType()) + L".";
                        }
                    }
                    else if (!m_hasSweepPath)
                    { // Expecting Path (Edge or Wire)
                        if (subShape.ShapeType() == TopAbs_EDGE || subShape.ShapeType() == TopAbs_WIRE)
                        {
                            m_sweepPath = subShape;
                            m_hasSweepPath = true;
                            // Keep profile selected, add path to selection
                            m_context->AddOrRemoveSelected(brepOwner, Standard_True);
                            msg = L"Path " + MyCadUtils::ShapeTypeToString(subShape.ShapeType()) + L" selected! Both profile and path are ready. Click 'Sweep' button.";
                            UpdateWindowTitle(L"OCCT Viewer - Sweep: Profile ✓, Path ✓. Click Sweep button.");
                            selectionChanged = true;
                        }
                        else
                        {
                            msg = L"Expecting an EDGE or WIRE for the path. Clicked on a " + MyCadUtils::ShapeTypeToString(subShape.ShapeType()) + L".";
                        }
                    }
                    else
                    { // Both already selected, user might be re-selecting
                        msg = L"Profile and Path already selected. Click 'Sweep' button or re-select if needed.";
                        // Allow re-selection:
                        if (subShape.ShapeType() == TopAbs_FACE)
                        { // Re-selecting profile
                            m_sweepProfile = subShape;
                            m_hasSweepPath = false;
                            m_sweepPath.Nullify();
                            m_context->ClearSelected(Standard_False);
                            m_context->AddOrRemoveSelected(brepOwner, Standard_True);
                            msg = L"Profile re-selected. Click path.";
                            UpdateWindowTitle(L"OCCT Viewer - Sweep: Profile ✓ (Re), Click Path");
                            selectionChanged = true;
                        }
                        else if (subShape.ShapeType() == TopAbs_EDGE || subShape.ShapeType() == TopAbs_WIRE)
                        { // Re-selecting path
                            m_sweepPath = subShape;
                            // Need to re-select profile in context too if it was cleared
                            // This logic gets complex quickly. Simpler: tell user to click Sweep or cancel to restart.
                            msg = L"Path re-selected. Click 'Sweep' button.";
                            Handle(AIS_InteractiveObject) profileObj; // Get first selected object
                            m_context->InitSelected();
                            if (m_context->MoreSelected())
                            {
                                profileObj = m_context->SelectedInteractive();
                            }
                            m_context->ClearSelected(Standard_False);
                            if (!profileObj.IsNull())
                                m_context->AddOrRemoveSelected(profileObj, Standard_True); // Re-select profile
                            m_context->AddOrRemoveSelected(brepOwner, Standard_True);      // Select new path

                            UpdateWindowTitle(L"OCCT Viewer - Sweep: Profile ✓, Path ✓ (Re). Click Sweep.");
                            selectionChanged = true;
                        }
                    }
                    if (selectionChanged)
                        RedrawView();
                    MessageBoxW(m_hMainWnd, msg.c_str(), L"Sweep Mode", MB_OK | MB_ICONINFORMATION);
                }
            }
        }
    }

    void AppCore::HandleTestSweepOperation()
    {
        try
        {
            UpdateWindowTitle(L"OCCT Viewer - Creating Test Sweep...");

            TopoDS_Wire profile = MyCadGeom::CreateCircularProfile(10.0, gp_Pnt(0, 0, 0));
            if (profile.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"Failed to create circular profile for test sweep.", L"Test Sweep Error", MB_OK | MB_ICONERROR);
                return;
            }
            TopoDS_Wire path = MyCadGeom::CreateCurvedPath();
            if (path.IsNull())
            {
                MessageBoxW(m_hMainWnd, L"Failed to create curved path for test sweep.", L"Test Sweep Error", MB_OK | MB_ICONERROR);
                return;
            }
            TopoDS_Shape sweptShape = MyCadGeom::SweepProfileAlongPath(profile, path);

            if (!sweptShape.IsNull())
            {
                auto command = std::make_unique<SceneCommand>(this, "Test Sweep Operation");
                command->CaptureBeforeState();
                command->SetOperation([this, sweptShape, profile, path]()
                                      {
                    // ClearSceneInternal(); // Clear everything for a clean test display
                    AddObjectToScene(profile, Quantity_NameOfColor::Quantity_NOC_GREEN, "TestProfile", false);
                    AddObjectToScene(path, Quantity_NameOfColor::Quantity_NOC_BLUE, "TestPath", false);
                    AddObjectToScene(sweptShape, Quantity_NameOfColor::Quantity_NOC_ORANGE, "TestSweepResult", true); });
                command->CaptureAfterState();
                ExecuteCommand(std::move(command));
                UpdateWindowTitle(L"OCCT Viewer - Test Sweep Complete");
                MessageBoxW(m_hMainWnd, L"Test sweep added to scene. Profile (Green), Path (Blue), Result (Orange).", L"Test Sweep", MB_OK);
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Test sweep operation failed (result is null).", L"Test Sweep Error", MB_OK | MB_ICONERROR);
            }
        }
        catch (const Standard_Failure &e)
        {
            MessageBoxA(m_hMainWnd, e.GetMessageString(), "Test Sweep Exception", MB_OK | MB_ICONERROR);
        }
        catch (...)
        {
            MessageBoxW(m_hMainWnd, L"Test sweep operation threw an unknown exception.", L"Test Sweep Error", MB_OK | MB_ICONERROR);
        }
    }

    // Model Tree Management Implementation
    bool AppCore::InitializeModelTree(HWND parentWindow, int x, int y, int width, int height)
    {
        if (!m_modelTreeManager)
            return false;
        bool success = m_modelTreeManager->Initialize(parentWindow, x, y, width, height, this);
        if (success)
        {
            m_modelTreeManager->OnObjectSelected = [this](int objectId)
            { OnTreeObjectSelected(objectId); };
            m_modelTreeManager->OnVisibilityChanged = [this](int objectId, bool visible)
            { OnTreeObjectVisibilityChanged(objectId, visible); };
            m_modelTreeManager->OnObjectDeleted = [this](int objectId)
            { OnTreeObjectDeleted(objectId); };
            m_modelTreeManager->OnObjectRenamed = [this](int objectId, const std::string &newName)
            { OnTreeObjectRenamed(objectId, newName); };
            UpdateModelTree(); // Populate with any existing objects
        }
        return success;
    }

    void AppCore::UpdateModelTree()
    {
        if (!m_modelTreeManager)
            return;
        m_modelTreeManager->ClearAll();
        for (const auto &obj : m_sceneObjects)
        {
            bool isVisible = true; // Default assumption
            if (!m_context.IsNull() && !obj.aisObject.IsNull())
            {
                isVisible = m_context->IsDisplayed(obj.aisObject);
            }
            m_modelTreeManager->AddObject(obj.id, obj.name, isVisible);
        }
    }

    void AppCore::ResizeModelTree(int x, int y, int width, int height)
    {
        if (m_modelTreeManager)
            m_modelTreeManager->Resize(x, y, width, height);
    }

    void AppCore::ShowModelTree(bool show)
    {
        if (m_modelTreeManager)
            m_modelTreeManager->Show(show);
    }

    void AppCore::HandleModelTreeNotify(LPNMHDR pnmh)
    {
        if (m_modelTreeManager)
            m_modelTreeManager->HandleTreeViewNotify(pnmh);
    }

    void AppCore::OnTreeObjectSelected(int objectId)
    {
        auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                               [objectId](const SceneObject &obj)
                               { return obj.id == objectId; });
        if (it != m_sceneObjects.end() && !m_context.IsNull() && !it->aisObject.IsNull())
        {
            if (m_context->IsDisplayed(it->aisObject))
            {                                                         // Only select if visible
                ClearAllContextSelections();                          // Clear any previous 3D view selections
                AddToSelection(it->aisObject);                        // Use new multiple selection system
                m_context->SetSelected(it->aisObject, Standard_True); // Select and update viewer

                // Update window title to show selected object
                std::wstring title = L"OCCT Viewer - Selected: " + std::wstring(it->name.begin(), it->name.end());
                UpdateWindowTitle(title.c_str());
            }
            else
            {
                MessageBoxW(m_hMainWnd, L"Cannot select a hidden object. Please make it visible first.", L"Selection Info", MB_OK | MB_ICONINFORMATION);
            }
        }
    }

    void AppCore::OnTreeObjectVisibilityChanged(int objectId, bool visible)
    {
        auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                               [objectId](const SceneObject &obj)
                               { return obj.id == objectId; });
        if (it != m_sceneObjects.end() && !m_context.IsNull() && !it->aisObject.IsNull())
        {
            if (visible)
            {
                m_context->Display(it->aisObject, Standard_True); // Display and update
            }
            else
            {
                if (m_selection1_IO == it->aisObject)
                    m_selection1_IO.Nullify(); // Clear if it was selected
                if (m_selection2_IO == it->aisObject)
                    m_selection2_IO.Nullify();
                if (m_parentShapeOfSelectedFaces == it->aisObject)
                {
                    m_selectedFacesList.Clear();
                    m_parentShapeOfSelectedFaces.Nullify();
                }
                if (m_context->IsSelected(it->aisObject))
                {
                    m_context->AddOrRemoveSelected(it->aisObject, Standard_False); // Deselect if selected
                }
                m_context->Erase(it->aisObject, Standard_True); // Erase and update
            }

            // Update the tree's visual state to reflect the new visibility
            if (m_modelTreeManager)
            {
                m_modelTreeManager->UpdateObjectVisibility(objectId, visible);
            }
        }
    }

    void AppCore::OnTreeObjectDeleted(int objectId)
    {
        // Command pattern should be used here if undo/redo for tree deletion is desired
        RemoveObjectFromScene(objectId); // This already handles context erase and list removal
        // ModelTreeManager should have already removed it from its own view.
        // No need to call m_modelTreeManager->RemoveObject here as it initiated the call.
    }

    void AppCore::OnTreeObjectRenamed(int objectId, const std::string &newName)
    {
        auto it = std::find_if(m_sceneObjects.begin(), m_sceneObjects.end(),
                               [&](SceneObject &obj)
                               { return obj.id == objectId; }); // Note: lambda captures `this` implicitly if needed
        if (it != m_sceneObjects.end())
        {
            it->name = newName;
            // The tree manager itself handles updating the label. No further action needed here typically.
        }
    }

    // ============================================================================
    // FILL OPERATIONS - Create smooth surfaces from closed boundaries
    // ============================================================================

    void AppCore::CreateFillOperation() // Test Fill
    {
        try
        {
            gp_Pnt p1(-20, -20, 0);
            gp_Pnt p2(20, -20, 0);
            gp_Pnt p3(20, 20, 0);
            gp_Pnt p4(-20, 20, 0);
            BRepBuilderAPI_MakeEdge e1(p1, p2), e2(p2, p3), e3(p3, p4), e4(p4, p1);
            BRepBuilderAPI_MakeWire wire;
            wire.Add(e1.Edge());
            wire.Add(e2.Edge());
            wire.Add(e3.Edge());
            wire.Add(e4.Edge());
            if (!wire.IsDone())
            { /* error */
                return;
            }
            TopoDS_Face filledSurface = MyCadGeom::CreateFilledSurface(wire.Wire(), m_fillDegree, m_fillMaxSegments);
            if (filledSurface.IsNull())
            { /* error */
                return;
            }
            AddObjectToScene(filledSurface, Quantity_NameOfColor::Quantity_NOC_PALEGREEN, "TestFilledSurface");
            MessageBoxA(m_hMainWnd, "Test Fill completed.", "Fill", MB_OK);
        }
        catch (const Standard_Failure &e)
        {
            MessageBoxA(m_hMainWnd, e.GetMessageString(), "Fill Error", MB_OK | MB_ICONERROR);
        }
    }

    void AppCore::CreateFillFromSelectedEdges()
    {
        try
        {
            if (m_selectedEdgesForFill.IsEmpty())
            {
                MessageBoxA(m_hMainWnd, "No edges selected. Entering edge selection mode for Fill.\n\nClick edges to form a closed loop.\nRight-click when done.", "Fill Info", MB_OK | MB_ICONINFORMATION);
                m_isInFillSelectionMode = true;
                m_selectedEdgesForFill.Clear();
                ClearEdgeHighlights();        // Clear any previous highlights
                ConfigureSelectionForEdges(); // IMPORTANT: This sets context to select ONLY edges
                UpdateWindowTitle(L"OCCT Viewer - Fill Mode: Select edges (Right-click when done)");
                return;
            }
            // This part is now handled by HandleFillSelectionComplete, which is called on Right-Click.
            // So, this button effectively becomes "Enter Fill Edge Selection Mode" if no edges selected.
            // If edges *are* selected (e.g. from a previous unfinished attempt), maybe try to fill?
            // For clarity, let right-click be the only way to finalize.
            MessageBoxA(m_hMainWnd, "Edges are already being selected for Fill. Right-click in the 3D view when you have chosen all edges for the boundary.", "Fill Info", MB_OK | MB_ICONINFORMATION);
        }
        catch (const Standard_Failure &e)
        {
            MessageBoxA(m_hMainWnd, e.GetMessageString(), "Fill Error", MB_OK | MB_ICONERROR);
            m_isInFillSelectionMode = false; // Reset on error
            RestoreNormalSelection();
            ClearEdgeHighlights();
        }
    }

    void AppCore::CreateAdvancedFillOperation()
    {
        // Similar to CreateFillFromSelectedEdges, but might use different parameters or GeomPlate_BuildPlateSurface
        MessageBoxA(m_hMainWnd, "Advanced Fill: Select edges for a high-quality fill.\nThis will use G1/G2 continuity options if applicable.\n\nEntering edge selection mode.", "Advanced Fill", MB_OK | MB_ICONINFORMATION);
        m_isInFillSelectionMode = true; // Reuse same selection mechanism
        m_selectedEdgesForFill.Clear();
        ClearEdgeHighlights();
        ConfigureSelectionForEdges();
        // m_fillUseG1Continuity = true; m_fillUseG2Continuity = true; // Example for advanced
        UpdateWindowTitle(L"OCCT Viewer - Advanced Fill: Select edges (Right-click when done)");
    }

    void AppCore::HandleFillEdgeSelection(int x, int y) // Called from LButtonDown if m_isInFillSelectionMode
    {
        if (!m_isInFillSelectionMode || m_context.IsNull())
            return;
        try
        {
            m_context->MoveTo(x, y, m_view, Standard_False);
            if (m_context->HasDetected())
            {
                Handle(SelectMgr_EntityOwner) owner = m_context->DetectedOwner();
                if (owner && owner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
                {
                    Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(owner);
                    if (brepOwner->HasShape() && brepOwner->Shape().ShapeType() == TopAbs_EDGE)
                    {
                        TopoDS_Edge selectedEdge = TopoDS::Edge(brepOwner->Shape());
                        bool alreadySelected = false;
                        for (TopTools_ListIteratorOfListOfShape it(m_selectedEdgesForFill); it.More(); it.Next())
                        {
                            if (it.Value().IsSame(selectedEdge))
                            {
                                alreadySelected = true;
                                break;
                            }
                        }
                        if (!alreadySelected)
                        {
                            m_selectedEdgesForFill.Append(selectedEdge);
                            // Highlight selected edge
                            Handle(AIS_Shape) hl = new AIS_Shape(selectedEdge);
                            hl->SetColor(Quantity_NameOfColor::Quantity_NOC_RED);
                            hl->SetWidth(3.0);
                            hl->SetDisplayMode(0); // Wireframe
                            m_context->Display(hl, Standard_False);
                            m_edgeHighlights.push_back(hl);

                            UpdateWindowTitle(L"Fill: " + std::to_wstring(m_selectedEdgesForFill.Size()) + L" edges. RClick done.");
                        }
                        else
                        { // Toggle deselection
                            TopTools_ListOfShape newList;
                            for (TopTools_ListIteratorOfListOfShape it(m_selectedEdgesForFill); it.More(); it.Next())
                            {
                                if (!it.Value().IsSame(selectedEdge))
                                    newList.Append(it.Value());
                            }
                            m_selectedEdgesForFill = newList;
                            ClearEdgeHighlights(); // Simple: clear all and re-highlight remaining
                            for (TopTools_ListIteratorOfListOfShape it(m_selectedEdgesForFill); it.More(); it.Next())
                            {
                                Handle(AIS_Shape) hl = new AIS_Shape(it.Value());
                                hl->SetColor(Quantity_NameOfColor::Quantity_NOC_RED);
                                hl->SetWidth(3.0);
                                hl->SetDisplayMode(0);
                                m_context->Display(hl, Standard_False);
                                m_edgeHighlights.push_back(hl);
                            }
                            UpdateWindowTitle(L"Fill: " + std::to_wstring(m_selectedEdgesForFill.Size()) + L" edges. RClick done.");
                        }
                    }
                    else
                    {
                        MessageBoxA(m_hMainWnd, "Please click on an EDGE. Detected other type.", "Fill Edge Selection", MB_OK);
                    }
                }
            }
            RedrawView();
        }
        catch (const Standard_Failure &e)
        {
            MessageBoxA(m_hMainWnd, e.GetMessageString(), "Fill Edge Selection Error", MB_OK | MB_ICONERROR);
        }
    }

    void AppCore::HandleFillSelectionComplete() // Called from RButtonDown if m_isInFillSelectionMode
    {
        if (!m_isInFillSelectionMode)
            return;
        try
        {
            if (m_selectedEdgesForFill.IsEmpty())
            {
                MessageBoxA(m_hMainWnd, "No edges selected for fill.", "Fill Complete", MB_OK | MB_ICONINFORMATION);
            }
            else if (!MyCadGeom::ValidateEdgesForFill(m_selectedEdgesForFill))
            {
                MessageBoxA(m_hMainWnd, "Selected edges do not form a valid closed boundary. Please clear selection (e.g. by re-clicking fill button) and try again.", "Fill Validation Error", MB_OK | MB_ICONERROR);
                // Don't exit mode, let user continue or cancel. Or provide a clear selection mechanism.
                return; // Stay in mode for user to correct
            }
            else
            {
                TopoDS_Face filledSurface;
                // Could differentiate here if it was "Advanced Fill" mode
                // For now, assume standard fill.
                filledSurface = MyCadGeom::CreateFilledSurface(m_selectedEdgesForFill, m_fillDegree, m_fillMaxSegments);

                if (filledSurface.IsNull())
                {
                    MessageBoxA(m_hMainWnd, "Fill operation failed with selected edges.", "Fill Error", MB_OK | MB_ICONERROR);
                }
                else
                {
                    Quantity_Color clr = (m_fillUseG1Continuity || m_fillUseG2Continuity) ? Quantity_NameOfColor::Quantity_NOC_MEDIUMPURPLE : Quantity_NameOfColor::Quantity_NOC_PALEGREEN;
                    AddObjectToScene(filledSurface, clr, "FilledFromEdges");
                    MessageBoxA(m_hMainWnd, "Fill operation completed successfully!", "Fill Success", MB_OK | MB_ICONINFORMATION);
                }
            }
        }
        catch (const Standard_Failure &e)
        {
            MessageBoxA(m_hMainWnd, e.GetMessageString(), "Fill Completion Error", MB_OK | MB_ICONERROR);
        }

        // Exit fill selection mode
        m_isInFillSelectionMode = false;
        m_selectedEdgesForFill.Clear();
        ClearEdgeHighlights();
        RestoreNormalSelection(); // IMPORTANT: Restore normal context selection behavior
        UpdateWindowTitle(L"OCCT Viewer - Ready");
        RedrawView();
    }

    void AppCore::ConfigureSelectionForEdges()
    {
        if (m_context.IsNull())
            return;
        m_context->ClearSelected(Standard_False); // Clear any current selections in context

        for (const auto &obj : m_sceneObjects)
        {
            if (!obj.aisObject.IsNull())
            {
                // Clear all other modes and activate only EDGE selection (mode 2)
                m_context->Activate(obj.aisObject, AIS_Shape::SelectionMode(TopAbs_EDGE), Standard_True);
            }
        }
        m_context->SetPixelTolerance(5);  // Adjust pixel tolerance for edge picking
        m_context->UpdateCurrentViewer(); // Apply changes
        RedrawView();
    }

    void AppCore::RestoreNormalSelection()
    {
        if (m_context.IsNull())
            return;
        m_context->ClearSelected(Standard_False);

        for (const auto &obj : m_sceneObjects)
        {
            if (!obj.aisObject.IsNull())
            {
                // Activate mode 0 (object) and clear others for this object
                m_context->Activate(obj.aisObject, 0, Standard_True);
                // Add modes for sub-shapes without clearing mode 0
                m_context->Activate(obj.aisObject, AIS_Shape::SelectionMode(TopAbs_FACE), Standard_False);
                m_context->Activate(obj.aisObject, AIS_Shape::SelectionMode(TopAbs_EDGE), Standard_False);
                m_context->Activate(obj.aisObject, AIS_Shape::SelectionMode(TopAbs_SOLID), Standard_False);
                m_context->Activate(obj.aisObject, AIS_Shape::SelectionMode(TopAbs_VERTEX), Standard_False);
            }
        }
        m_context->SetPixelTolerance(2); // Restore default pixel tolerance
        m_context->UpdateCurrentViewer();
        RedrawView();
    }

    bool AppCore::FindEdgeUnderCursor(int x, int y, TopoDS_Edge &foundEdge, Handle(AIS_Shape) & parentShape)
    {
        // This function is somewhat redundant if ConfigureSelectionForEdges works perfectly,
        // as m_context->DetectedOwner() should already provide an edge.
        // However, it can be a fallback or used for different types of edge interactions.
        if (m_context.IsNull() || m_view.IsNull())
            return false;

        // Temporarily ensure only edge mode is active for detection for this specific call
        // This is heavy-handed if called frequently. Better to rely on global mode setting.
        // For now, assume ConfigureSelectionForEdges has already set the mode.

        m_context->MoveTo(x, y, m_view, Standard_False);
        if (m_context->HasDetected())
        {
            Handle(SelectMgr_EntityOwner) owner = m_context->DetectedOwner();
            if (owner && owner->IsKind(STANDARD_TYPE(StdSelect_BRepOwner)))
            {
                Handle(StdSelect_BRepOwner) brepOwner = Handle(StdSelect_BRepOwner)::DownCast(owner);
                if (brepOwner->HasShape() && brepOwner->Shape().ShapeType() == TopAbs_EDGE)
                {
                    foundEdge = TopoDS::Edge(brepOwner->Shape());
                    if (owner->Selectable()->IsKind(STANDARD_TYPE(AIS_Shape)))
                    {
                        parentShape = Handle(AIS_Shape)::DownCast(owner->Selectable());
                    }
                    return true;
                }
            }
        }
        return false;
    }

    void AppCore::ClearEdgeHighlights()
    {
        if (m_context.IsNull())
            return;
        for (auto &hlShape : m_edgeHighlights)
        {
            if (!hlShape.IsNull())
            {
                m_context->Erase(hlShape, Standard_False); // Erase without immediate redraw
            }
        }
        m_edgeHighlights.clear();
        RedrawView(); // Redraw once after all erasures
    }

    void AppCore::DebugListEdgesInShape(const TopoDS_Shape &shape)
    {
        if (shape.IsNull())
            return;

        // Use sets with comparators to remove duplicates, like in your reference code
        std::set<gp_Pnt, ComparePoints> uniqueVertices;
        std::set<std::pair<gp_Pnt, gp_Pnt>, CompareEdge> uniqueEdges;
        int faceCount = 0;

        // Count unique edges using comparator like your reference code
        for (TopExp_Explorer exp(shape, TopAbs_EDGE); exp.More(); exp.Next())
        {
            TopoDS_Edge edge = TopoDS::Edge(exp.Current());

            Standard_Real first, last;
            Handle(Geom_Curve) curve = BRep_Tool::Curve(edge, first, last);

            if (!curve.IsNull())
            {
                gp_Pnt p1 = curve->Value(first);
                gp_Pnt p2 = curve->Value(last);

                // Add to unique edges set (automatically removes duplicates)
                if (CompareEdge()(std::make_pair(p1, p2), std::make_pair(p2, p1)))
                {
                    uniqueEdges.insert(std::make_pair(p1, p2));
                }

                // Add vertices to unique set
                uniqueVertices.insert(p1);
                uniqueVertices.insert(p2);
            }
        }

        // Count faces
        for (TopExp_Explorer exp(shape, TopAbs_FACE); exp.More(); exp.Next())
        {
            faceCount++;
        }

        char totalMsg[200];
        sprintf_s(totalMsg, "Shape topology (unique):\n%d edges (should be 12 for cube)\n%d faces (should be 6 for cube)\n%d vertices (should be 8 for cube)",
                  (int)uniqueEdges.size(), faceCount, (int)uniqueVertices.size());

        // Show topology debug info
        MessageBoxA(m_hMainWnd, totalMsg, "Debug: Shape Topology", MB_OK);
    }

} // namespace MyCad