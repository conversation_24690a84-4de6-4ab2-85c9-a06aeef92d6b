﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{002742B7-2A7B-3A22-BC54-475D3678D188}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\MyCadApplication\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\MyCadApplication\build\ZERO_CHECK.vcxproj">
      <Project>{C8052788-5054-376F-BA25-361432419525}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\MyCadApplication\build\MyCadApplication.vcxproj">
      <Project>{E89AC21A-8551-3738-B369-8D883CBC5D63}</Project>
      <Name>MyCadApplication</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>