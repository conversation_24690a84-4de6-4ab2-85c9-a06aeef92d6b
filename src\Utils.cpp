#include "MyCad/Utils.h" // NEW
#include <commdlg.h> // For OPENFILENAMEW

namespace MyCadUtils {

std::string WideCharToStdString(LPCWSTR wstr) {
    if (!wstr) return "";
    int bufferSize = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
    if (bufferSize == 0) return "";
    std::string str(bufferSize - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr, -1, &str[0], bufferSize, NULL, NULL);
    return str;
}

std::wstring StringToWString(const std::string& str) {
    if (str.empty()) return L"";
    int bufferSize = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, NULL, 0);
    if (bufferSize == 0) return L"";
    std::wstring wstr(bufferSize - 1, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &wstr[0], bufferSize);
    return wstr;
}

std::string StringToLower(const std::string& s) {
    std::string result = s;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c){ return std::tolower(c); });
    return result;
}

std::string GetFileExtension(const std::string& filePath) {
    size_t dotPos = filePath.find_last_of(".");
    if (dotPos != std::string::npos && dotPos < filePath.length() - 1) {
        return StringToLower(filePath.substr(dotPos + 1));
    }
    return "";
}

std::string GetFilePathFromDialog(HWND hwndOwner, const wchar_t* filter, const wchar_t* defaultExt) {
    OPENFILENAMEW ofn = {};
    wchar_t szFile[MAX_PATH] = {};
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = hwndOwner;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile) / sizeof(wchar_t);
    ofn.lpstrFilter = filter;
    ofn.nFilterIndex = 1;
    ofn.lpstrDefExt = defaultExt;
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_EXPLORER;
    return (GetOpenFileNameW(&ofn) == TRUE) ? WideCharToStdString(ofn.lpstrFile) : "";
}

std::string GetSaveFilePathFromDialog(HWND hwndOwner, const wchar_t* filter, const wchar_t* defaultExt) {
    OPENFILENAMEW ofn = {};
    wchar_t szFile[MAX_PATH] = {};
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = hwndOwner;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile) / sizeof(wchar_t);
    ofn.lpstrFilter = filter;
    ofn.nFilterIndex = 1;
    ofn.lpstrDefExt = defaultExt;
    ofn.Flags = OFN_OVERWRITEPROMPT | OFN_PATHMUSTEXIST | OFN_EXPLORER;
    return (GetSaveFileNameW(&ofn) == TRUE) ? WideCharToStdString(ofn.lpstrFile) : "";
}

std::wstring ShapeTypeToString(TopAbs_ShapeEnum shapeType) {
    switch (shapeType) {
        case TopAbs_COMPOUND: return L"COMPOUND";
        case TopAbs_COMPSOLID: return L"COMPSOLID";
        case TopAbs_SOLID: return L"SOLID";
        case TopAbs_SHELL: return L"SHELL";
        case TopAbs_FACE: return L"FACE";
        case TopAbs_WIRE: return L"WIRE";
        case TopAbs_EDGE: return L"EDGE";
        case TopAbs_VERTEX: return L"VERTEX";
        case TopAbs_SHAPE: return L"SHAPE";
        default: return L"UNKNOWN";
    }
}

} // namespace MyCadUtils