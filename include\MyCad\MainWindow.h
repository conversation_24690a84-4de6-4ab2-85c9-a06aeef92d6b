#pragma once

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <string>

namespace MyCad {

class MainWindow {
public:
    MainWindow(HINSTANCE hInstance);
    ~MainWindow();

    bool Create(const std::wstring& title, int nCmdShow, int width = 1024, int height = 768);
    HWND GetHwnd() const { return m_hWnd; }
    HWND GetOccHwnd() const { return m_hOccWindow; } // Expose OCC child window handle

    static LRESULT CALLBACK WndProcRouter(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);

private:
    LRESULT HandleMessage(UINT msg, WPARAM wParam, LPARAM lParam);
    void CreateButtons();
    void ResizeUIElements();
    void ResizeOccView();
    INT_PTR CALLBACK DlgProcRevolveParams(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);

    HW<PERSON> m_hWnd;
    HW<PERSON> m_hOccWindow; // Child window for OCC rendering
    HINSTANCE m_hInstance;

    // Button Handles
    HWND m_hImportButton;
    HWND m_hCreateCubeButton;
    HWND m_hCreateCylinderButton;
    HWND m_hCutButton;
    HWND m_hThickButton;
    HWND m_hClearButton;
    HWND m_hExportButton;
    HWND m_hExtrudeButton;
    HWND m_hFilletButton;
    HWND m_hChamferButton;
    HWND m_hRevolveButton;
    HWND m_hUndoButton;
    HWND m_hRedoButton;
    HWND m_hTrimButton;
    HWND m_hUntrimButton;
    HWND m_hSplitButton;
    HWND m_hJoinButton;
    HWND m_hMirrorButton;
    HWND m_hOffsetButton;
    HWND m_hThickSolidButton;
    HWND m_hSweepButton;
    HWND m_hTestSweepButton;
    HWND m_hFillButton;
    HWND m_hFillEdgesButton;
    HWND m_hAdvancedFillButton;
    // Other UI elements if needed
};

} // namespace MyCad
