# CMake generation dependency list for this directory.
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDependentOption.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCInformation.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/FindZLIB.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake
C:/Program Files/CMake/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake
C:/vcpkg/installed/x64-windows/share/freetype/freetype-config-version.cmake
C:/vcpkg/installed/x64-windows/share/freetype/freetype-config.cmake
C:/vcpkg/installed/x64-windows/share/freetype/freetype-targets-debug.cmake
C:/vcpkg/installed/x64-windows/share/freetype/freetype-targets-release.cmake
C:/vcpkg/installed/x64-windows/share/freetype/freetype-targets.cmake
C:/vcpkg/installed/x64-windows/share/freetype/vcpkg-cmake-wrapper.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEApplicationFrameworkTargets-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEApplicationFrameworkTargets-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEApplicationFrameworkTargets.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADECompileDefinitionsAndFlags-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADECompileDefinitionsAndFlags-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEConfig.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEConfigVersion.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEDataExchangeTargets-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEDataExchangeTargets-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEDataExchangeTargets.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEFoundationClassesTargets-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEFoundationClassesTargets-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEFoundationClassesTargets.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEModelingAlgorithmsTargets-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEModelingAlgorithmsTargets-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEModelingAlgorithmsTargets.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEModelingDataTargets-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEModelingDataTargets-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEModelingDataTargets.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEVisualizationTargets-debug.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEVisualizationTargets-release.cmake
C:/vcpkg/installed/x64-windows/share/opencascade/OpenCASCADEVisualizationTargets.cmake
C:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake
C:/vcpkg/scripts/buildsystems/vcpkg.cmake
D:/MyCadApplication/CMakeLists.txt
D:/MyCadApplication/build/CMakeFiles/3.31.3/CMakeCXXCompiler.cmake
D:/MyCadApplication/build/CMakeFiles/3.31.3/CMakeRCCompiler.cmake
D:/MyCadApplication/build/CMakeFiles/3.31.3/CMakeSystem.cmake
