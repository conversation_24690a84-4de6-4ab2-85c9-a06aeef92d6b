#pragma once

// Windows Resource IDs
// Single source of truth for all resource identifiers

// Standard Windows dialog button IDs (if not already defined by windows.h)
#ifndef IDOK
#define IDOK                        1
#endif
#ifndef IDCANCEL
#define IDCANCEL                    2
#endif

// Standard Windows dialog button IDs
#define IDOK                        1
#define IDCANCEL                    2

// Dialog IDs (100-199 range)
#define IDD_THICKNESS_DIALOG        101
#define IDD_REVOLVE_DIALOG          110
#define IDD_OFFSET_DIALOG           111

// Control/Button IDs (1000-1999 range)
#define IDC_IMPORT_SHAPE_BUTTON     1001
#define IDC_CREATE_CUBE_BUTTON      1002
#define IDC_CREATE_CYLINDER_BUTTON  1003
#define IDC_CUT_BUTTON              1004
#define IDC_THICK_BUTTON            1005
#define IDC_CLEAR_BUTTON            1006
#define IDC_EXPORT_BUTTON           1007
#define IDC_EXTRUDE_BUTTON          1008
#define IDC_FILLET_BUTTON           1009
#define IDC_CHAMFER_BUTTON          1010
#define IDC_REVOLVE_BUTTON          1011
#define IDC_UNDO_BUTTON             1012
#define IDC_REDO_BUTTON             1013
#define IDC_TRIM_BUTTON             1014
#define IDC_UNTRIM_BUTTON           1015
#define IDC_SPLIT_BUTTON            1016
#define IDC_JOIN_BUTTON             1017
#define IDC_MIRROR_BUTTON           1018
#define IDC_OFFSET_BUTTON           1019
#define IDC_THICK_SOLID_BUTTON      1020
#define IDC_SWEEP_BUTTON            1021
#define IDC_TEST_SWEEP_BUTTON       1022
#define IDC_FILL_BUTTON             1023
#define IDC_FILL_EDGES_BUTTON       1024
#define IDC_ADVANCED_FILL_BUTTON    1025

// Mirror Dialog IDs
#define IDD_MIRROR_DIALOG           2000
#define IDC_RADIO_XY_PLANE          2001
#define IDC_RADIO_XZ_PLANE          2002
#define IDC_RADIO_YZ_PLANE          2003
#define IDC_RADIO_CUSTOM_PLANE      2004
#define IDC_BUTTON_SELECT_FACE      2005
#define IDC_STATIC_SELECTED_FACE    2006
#define IDC_CHECK_KEEP_ORIGINAL     2007

// Offset Dialog IDs
#define IDC_EDIT_OFFSET_VALUE       2100
#define IDC_STATIC_OFFSET_VALUE     2101
#define IDC_RADIO_OFFSET_OUTWARD    2102
#define IDC_RADIO_OFFSET_INWARD     2103
#define IDC_RADIO_OFFSET_BOTH       2104
#define IDC_COMBO_OFFSET_MODE       2105
#define IDC_STATIC_OFFSET_MODE      2106
#define IDC_CHECK_KEEP_ORIGINAL_OFFSET 2107
#define IDC_STATIC_PREVIEW          2108
#define IDC_BUTTON_PREVIEW          2109
#define IDC_COMBO_JOIN_TYPE         2110
#define IDC_STATIC_JOIN_TYPE        2111

// Dialog control IDs (specific to dialogs)
#define IDC_EDIT_THICKNESS          1101
#define IDC_STATIC_THICKNESS        1102
#define IDC_ANGLE_EDIT              1001
#define IDC_ANGLE_SPIN              1002
#define IDC_ANGLE_LABEL             1003
#define IDC_STATIC                  -1

// Menu IDs (2000-2999 range) - for future use
#define IDM_FILE_OPEN               2001
#define IDM_FILE_SAVE               2002
#define IDM_FILE_EXIT               2003
#define ID_OPERATIONS_REVOLVE       40010  // Use a unique ID

// String resource IDs (3000-3999 range) - for future use
#define IDS_APP_TITLE               3001
#define IDS_ERROR_TITLE             3002

// Icon IDs (4000-4999 range) - for future use
#define IDI_MAIN_ICON               4001
