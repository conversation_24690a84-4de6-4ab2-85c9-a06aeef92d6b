#include "../include/resource.h"
#include <windows.h>

IDD_THICKNESS_DIALOG DIALOGEX 0, 0, 250, 120
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Set Thickness Value"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LTEXT           "Thickness:",IDC_STATIC_THICKNESS,20,20,50,14
    EDITTEXT        IDC_EDIT_THICKNESS,80,18,100,14,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,80,50,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,140,50,50,14
END

// Add this dialog template
IDD_REVOLVE_DIALOG DIALOGEX 0, 0, 250, 100
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Revolution Parameters"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",ID<PERSON>,135,79,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,193,79,50,14
    LTEXT           "Revolution Angle (degrees):",IDC_ANGLE_LABEL,15,20,100,8
    EDITTEXT        IDC_ANGLE_EDIT,120,18,50,14,ES_AUTOHSCROLL
    CONTROL         "",IDC_ANGLE_SPIN,"msctls_updown32",UDS_SETBUDDYINT | UDS_ALIGNRIGHT | UDS_AUTOBUDDY | UDS_ARROWKEYS,170,18,11,14
    LTEXT           "Select an edge for axis after clicking OK",IDC_STATIC,15,45,200,8
END

IDD_MIRROR_DIALOG DIALOGEX 0, 0, 320, 250
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Mirror Parameters"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,210,220,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,265,220,50,14

    GROUPBOX        "Mirror Plane",IDC_STATIC,20,20,280,120
    AUTORADIOBUTTON "XY Plane (Z = 0)",IDC_RADIO_XY_PLANE,30,40,100,10,WS_GROUP
    AUTORADIOBUTTON "XZ Plane (Y = 0)",IDC_RADIO_XZ_PLANE,30,60,100,10
    AUTORADIOBUTTON "YZ Plane (X = 0)",IDC_RADIO_YZ_PLANE,30,80,100,10
    AUTORADIOBUTTON "Custom Plane/Face",IDC_RADIO_CUSTOM_PLANE,30,100,100,10

    PUSHBUTTON      "Select Face",IDC_BUTTON_SELECT_FACE,150,98,80,14
    LTEXT           "Selected Face: None",IDC_STATIC_SELECTED_FACE,150,118,120,8

    GROUPBOX        "Options",IDC_STATIC,20,150,280,60
    AUTOCHECKBOX    "Keep Original Shape",IDC_CHECK_KEEP_ORIGINAL,30,170,120,10

    LTEXT           "Note: Mirror creates a parametric copy linked to the original shape.",IDC_STATIC,20,190,250,16
END

IDD_OFFSET_DIALOG DIALOGEX 0, 0, 380, 320
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Offset Parameters"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,260,290,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,315,290,50,14
    PUSHBUTTON      "Preview",IDC_BUTTON_PREVIEW,205,290,50,14

    GROUPBOX        "Offset Value",IDC_STATIC,20,20,340,60
    LTEXT           "Offset Distance:",IDC_STATIC_OFFSET_VALUE,30,40,80,14
    EDITTEXT        IDC_EDIT_OFFSET_VALUE,120,38,100,14,ES_AUTOHSCROLL
    LTEXT           "units",IDC_STATIC,225,40,20,14

    GROUPBOX        "Offset Direction",IDC_STATIC,20,90,340,80
    AUTORADIOBUTTON "Outward (+)",IDC_RADIO_OFFSET_OUTWARD,30,110,80,10,WS_GROUP
    AUTORADIOBUTTON "Inward (-)",IDC_RADIO_OFFSET_INWARD,30,130,80,10
    AUTORADIOBUTTON "Both Directions",IDC_RADIO_OFFSET_BOTH,30,150,80,10
    LTEXT           "Outward: Expands the shape\nInward: Contracts the shape\nBoth: Creates inner and outer shapes",IDC_STATIC,130,110,200,50

    GROUPBOX        "Offset Mode",IDC_STATIC,20,180,340,80
    LTEXT           "Mode:",IDC_STATIC_OFFSET_MODE,30,200,30,14
    COMBOBOX        IDC_COMBO_OFFSET_MODE,70,198,200,60,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "Skin: Surface offset\nThick: Hollow solid\nSimple: Basic offset\nPipe: Tube creation",IDC_STATIC,30,220,300,35

    GROUPBOX        "Options",IDC_STATIC,20,270,160,40
    AUTOCHECKBOX    "Keep Original Shape",IDC_CHECK_KEEP_ORIGINAL_OFFSET,30,285,120,10

    LTEXT           "Preview:",IDC_STATIC_PREVIEW,190,275,40,8
END

// Add to your menu resource
IDR_MAINMENU MENU
BEGIN
    // ... existing menu items
    POPUP "&Operations"
    BEGIN
        // ... existing operations
        MENUITEM "&Revolve...",                ID_OPERATIONS_REVOLVE
    END
END
