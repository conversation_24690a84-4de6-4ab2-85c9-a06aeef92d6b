cmake_minimum_required(VERSION 3.15)

project(MyCadApplication LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

if(NOT CMAKE_TOOLCHAIN_FILE AND EXISTS "C:/vcpkg/scripts/buildsystems/vcpkg.cmake")
    set(CMAKE_TOOLCHAIN_FILE "C:/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE STRING "vcpkg toolchain file")
endif()

find_package(OpenCASCADE REQUIRED)
message(STATUS "Found OpenCASCADE version: ${OpenCASCADE_VERSION}")
message(STATUS "OpenCASCADE_INCLUDE_DIRS: ${OpenCASCADE_INCLUDE_DIRS}") # Will be empty if includes are via targets
message(STATUS "OpenCASCADE_LIBRARIES: ${OpenCASCADE_LIBRARIES}")       # This is populated in your case

if(WIN32)
    set(CMAKE_EXE_LINKER_FLAGS_INIT "/SUBSYSTEM:WINDOWS")
endif()

# Define source files
set(APPLICATION_SOURCES
    src/main.cpp
    src/AppCore.cpp
    src/MainWindow.cpp
    src/GeometryOperations.cpp
    src/Utils.cpp
    src/UndoRedoSystem.cpp
    src/ModelTreeManager.cpp
)

# Define header files (for IDE organization)
set(APPLICATION_HEADERS
    include/MyCad/AppCore.h
    include/MyCad/MainWindow.h
    include/MyCad/GeometryOperations.h
    include/MyCad/Utils.h
    include/MyCad/Constants.h
    include/MyCad/UndoRedoSystem.h
    include/MyCad/ModelTreeManager.h
    include/resource.h
)

# Add Windows resource files if on Windows
if(WIN32)
    set(RESOURCE_FILE resources/resource.rc)
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/${RESOURCE_FILE}")
        list(APPEND APPLICATION_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/${RESOURCE_FILE}")
        message(STATUS "Including Windows resource file: ${RESOURCE_FILE}")
    else()
        message(WARNING "Windows resource file not found: ${RESOURCE_FILE}")
    endif()
endif()

# Create the executable
if(WIN32)
    add_executable(MyCadApplication WIN32 ${APPLICATION_SOURCES} ${APPLICATION_HEADERS})
else()
    add_executable(MyCadApplication ${APPLICATION_SOURCES} ${APPLICATION_HEADERS})
endif()

# Set up include directories
target_include_directories(MyCadApplication PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}/include"     # Your project's main include directory
    "${CMAKE_CURRENT_SOURCE_DIR}/include/MyCad" # Your project's specific headers
    ${OpenCASCADE_INCLUDE_DIRS}               # OpenCASCADE includes
)

# Link libraries
target_link_libraries(MyCadApplication PRIVATE
    ${OpenCASCADE_LIBRARIES} # Link all libraries found by find_package
)

# Link Windows-specific libraries
if(WIN32)
    target_link_libraries(MyCadApplication PRIVATE
        comctl32  # For TreeView and other common controls
        user32    # For Windows API
        gdi32     # For graphics
    )
endif()

# MSVC-specific settings
if(MSVC)
    set(CMAKE_DEBUG_POSTFIX "d")
    target_compile_options(MyCadApplication PRIVATE "/Zi")
    target_link_options(MyCadApplication PRIVATE "/DEBUG")

    # Enable resource compilation
    set_target_properties(MyCadApplication PROPERTIES
        VS_DEBUGGER_WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
    )
endif()

# Organize files in IDE (Visual Studio solution explorer)
if(WIN32 AND MSVC)
    source_group("Source Files" FILES ${APPLICATION_SOURCES})
    source_group("Header Files" FILES ${APPLICATION_HEADERS})
    source_group("Resource Files" FILES "${CMAKE_CURRENT_SOURCE_DIR}/resources/resource.rc")
endif()

# Copy resources to build directory if needed (optional)
if(WIN32)
    file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/resources/"
         DESTINATION "${CMAKE_BINARY_DIR}/resources/")
endif()