#pragma once

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>

// Ensure TreeView types are available
#ifndef HTREEITEM
typedef struct _TREEITEM* HTREEITEM;
#endif

namespace MyCad
{
    // Forward declaration
    class AppCore;
    struct SceneObject;

    // Tree item data structure
    struct TreeItemData
    {
        int objectId;
        std::string name;
        bool isVisible;
        bool isSelected;

        TreeItemData(int id, const std::string& itemName, bool visible = true, bool selected = false)
            : objectId(id), name(itemName), isVisible(visible), isSelected(selected) {}
    };

    class ModelTreeManager
    {
    public:
        ModelTreeManager();
        ~ModelTreeManager();

        // Initialize the tree view control
        bool Initialize(HWND parentWindow, int x, int y, int width, int height, AppCore* appCore);

        // Tree management
        void AddObject(int objectId, const std::string& name, bool visible = true);
        void RemoveObject(int objectId);
        void UpdateObjectName(int objectId, const std::string& newName);
        void UpdateObjectVisibility(int objectId, bool visible);
        void ClearAll();

        // Selection management
        void SelectObject(int objectId);
        void DeselectAll();
        std::vector<int> GetSelectedObjects() const;

        // Tree view operations
        void RefreshTree();
        void ExpandAll();
        void CollapseAll();

        // Window management
        void Resize(int x, int y, int width, int height);
        void Show(bool show);
        HWND GetTreeViewHandle() const { return m_hTreeView; }

        // Context menu
        void ShowContextMenu(int x, int y, int objectId);

        // Event handlers
        void HandleTreeViewNotify(LPNMHDR pnmh);
        void HandleRightClick(int x, int y);
        void HandleDoubleClick();
        void HandleKeyDown(WPARAM wParam);

        // Callbacks for tree operations
        std::function<void(int)> OnObjectSelected;
        std::function<void(int)> OnObjectDeselected;
        std::function<void(int, bool)> OnVisibilityChanged;
        std::function<void(int)> OnObjectDeleted;
        std::function<void(int, const std::string&)> OnObjectRenamed;

    private:
        // Tree view control
        HWND m_hTreeView;
        HWND m_hParentWindow;
        AppCore* m_appCore;

        // Tree item management
        std::unordered_map<int, HTREEITEM> m_objectToTreeItem;
        std::unordered_map<HTREEITEM, int> m_treeItemToObject;
        std::vector<TreeItemData> m_treeItems;

        // Root items
        HTREEITEM m_hRootItem;
        HTREEITEM m_hShapesRoot;

        // Context menu
        HMENU m_hContextMenu;
        int m_contextMenuObjectId;

        // Helper methods
        HTREEITEM AddTreeItem(HTREEITEM hParent, const std::string& text, int objectId);
        void UpdateTreeItemText(HTREEITEM hItem, const std::string& text);
        void UpdateTreeItemIcon(HTREEITEM hItem, bool visible);
        TreeItemData* FindTreeItemData(int objectId);
        void CreateContextMenu();
        void HandleContextMenuCommand(UINT commandId);

        // Tree view styling
        void SetupTreeViewStyle();
        void CreateImageList();

        // Constants
        static const int ICON_VISIBLE = 0;
        static const int ICON_HIDDEN = 1;
        static const int ICON_SHAPE = 2;
        static const int ICON_FOLDER = 3;

        // Context menu IDs
        static const UINT ID_CONTEXT_RENAME = 1001;
        static const UINT ID_CONTEXT_DELETE = 1002;
        static const UINT ID_CONTEXT_HIDE = 1003;
        static const UINT ID_CONTEXT_SHOW = 1004;
        static const UINT ID_CONTEXT_PROPERTIES = 1005;
    };

    // Helper class for in-place editing
    class TreeItemEditor
    {
    public:
        TreeItemEditor(HWND treeView);
        ~TreeItemEditor();

        bool StartEdit(HTREEITEM hItem);
        void EndEdit(bool save);
        bool IsEditing() const { return m_isEditing; }

        std::function<void(HTREEITEM, const std::string&)> OnEditComplete;

    private:
        HWND m_hTreeView;
        HWND m_hEditControl;
        HTREEITEM m_hEditItem;
        bool m_isEditing;
        std::string m_originalText;

        static LRESULT CALLBACK EditSubclassProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData);
    };
}
