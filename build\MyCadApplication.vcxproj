﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E89AC21A-8551-3738-B369-8D883CBC5D63}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>MyCadApplication</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\MyCadApplication\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MyCadApplication.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MyCadApplication</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <LocalDebuggerWorkingDirectory Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/MyCadApplication</LocalDebuggerWorkingDirectory>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\MyCadApplication\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MyCadApplication.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MyCadApplication</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <LocalDebuggerWorkingDirectory Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/MyCadApplication</LocalDebuggerWorkingDirectory>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\MyCadApplication\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">MyCadApplication.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">MyCadApplication</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <LocalDebuggerWorkingDirectory Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/MyCadApplication</LocalDebuggerWorkingDirectory>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\MyCadApplication\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">MyCadApplication.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">MyCadApplication</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
    <LocalDebuggerWorkingDirectory Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/MyCadApplication</LocalDebuggerWorkingDirectory>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/opencascade" /external:I "C:/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;UNICODE;_UNICODE;HAVE_FREETYPE;HAVE_OPENGL_EXT;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;UNICODE;_UNICODE;HAVE_FREETYPE;HAVE_OPENGL_EXT;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/MyCadApplication/build/Debug/MyCadApplication.exe -installedDir C:/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\debug\lib\TKOffset.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKFeat.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXMesh.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKOpenGl.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKMeshVS.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDESTEP.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDEIGES.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDESTL.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDEVRML.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDECascade.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBinXCAF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXmlXCAF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDEOBJ.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDEGLTF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDEPLY.lib;comctl32.lib;user32.lib;gdi32.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKFillet.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBool.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXSBase.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKStd.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKStdL.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBinTObj.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXmlTObj.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKTObj.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBin.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBinL.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXml.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXmlL.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKRWMesh.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKDE.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKXCAF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKVCAF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKV3d.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKHLR.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKMesh.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKService.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKCAF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBO.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKPrim.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKShHealing.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKTopAlgo.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKGeomAlgo.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKBRep.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKGeomBase.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKG3d.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKG2d.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKMath.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKLCAF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKCDF.lib;C:\vcpkg\installed\x64-windows\debug\lib\TKernel.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/MyCadApplication/build/Debug/MyCadApplication.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/MyCadApplication/build/Debug/MyCadApplication.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/opencascade" /external:I "C:/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;HAVE_FREETYPE;HAVE_OPENGL_EXT;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;HAVE_FREETYPE;HAVE_OPENGL_EXT;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/MyCadApplication/build/Release/MyCadApplication.exe -installedDir C:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\lib\TKOffset.lib;C:\vcpkg\installed\x64-windows\lib\TKFeat.lib;C:\vcpkg\installed\x64-windows\lib\TKXMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKOpenGl.lib;C:\vcpkg\installed\x64-windows\lib\TKMeshVS.lib;C:\vcpkg\installed\x64-windows\lib\TKDESTEP.lib;C:\vcpkg\installed\x64-windows\lib\TKDEIGES.lib;C:\vcpkg\installed\x64-windows\lib\TKDESTL.lib;C:\vcpkg\installed\x64-windows\lib\TKDEVRML.lib;C:\vcpkg\installed\x64-windows\lib\TKDECascade.lib;C:\vcpkg\installed\x64-windows\lib\TKBinXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKDEOBJ.lib;C:\vcpkg\installed\x64-windows\lib\TKDEGLTF.lib;C:\vcpkg\installed\x64-windows\lib\TKDEPLY.lib;comctl32.lib;user32.lib;gdi32.lib;C:\vcpkg\installed\x64-windows\lib\TKFillet.lib;C:\vcpkg\installed\x64-windows\lib\TKBool.lib;C:\vcpkg\installed\x64-windows\lib\TKXSBase.lib;C:\vcpkg\installed\x64-windows\lib\TKStd.lib;C:\vcpkg\installed\x64-windows\lib\TKStdL.lib;C:\vcpkg\installed\x64-windows\lib\TKBinTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKBin.lib;C:\vcpkg\installed\x64-windows\lib\TKBinL.lib;C:\vcpkg\installed\x64-windows\lib\TKXml.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlL.lib;C:\vcpkg\installed\x64-windows\lib\TKRWMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKDE.lib;C:\vcpkg\installed\x64-windows\lib\TKXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKVCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKV3d.lib;C:\vcpkg\installed\x64-windows\lib\TKHLR.lib;C:\vcpkg\installed\x64-windows\lib\TKMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKService.lib;C:\vcpkg\installed\x64-windows\lib\TKCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKBO.lib;C:\vcpkg\installed\x64-windows\lib\TKPrim.lib;C:\vcpkg\installed\x64-windows\lib\TKShHealing.lib;C:\vcpkg\installed\x64-windows\lib\TKTopAlgo.lib;C:\vcpkg\installed\x64-windows\lib\TKGeomAlgo.lib;C:\vcpkg\installed\x64-windows\lib\TKBRep.lib;C:\vcpkg\installed\x64-windows\lib\TKGeomBase.lib;C:\vcpkg\installed\x64-windows\lib\TKG3d.lib;C:\vcpkg\installed\x64-windows\lib\TKG2d.lib;C:\vcpkg\installed\x64-windows\lib\TKMath.lib;C:\vcpkg\installed\x64-windows\lib\TKLCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKCDF.lib;C:\vcpkg\installed\x64-windows\lib\TKernel.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/MyCadApplication/build/Release/MyCadApplication.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/MyCadApplication/build/Release/MyCadApplication.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/opencascade" /external:I "C:/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/MyCadApplication/build/MinSizeRel/MyCadApplication.exe -installedDir C:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\lib\TKOffset.lib;C:\vcpkg\installed\x64-windows\lib\TKFeat.lib;C:\vcpkg\installed\x64-windows\lib\TKXMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKOpenGl.lib;C:\vcpkg\installed\x64-windows\lib\TKMeshVS.lib;C:\vcpkg\installed\x64-windows\lib\TKDESTEP.lib;C:\vcpkg\installed\x64-windows\lib\TKDEIGES.lib;C:\vcpkg\installed\x64-windows\lib\TKDESTL.lib;C:\vcpkg\installed\x64-windows\lib\TKDEVRML.lib;C:\vcpkg\installed\x64-windows\lib\TKDECascade.lib;C:\vcpkg\installed\x64-windows\lib\TKBinXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKDEOBJ.lib;C:\vcpkg\installed\x64-windows\lib\TKDEGLTF.lib;C:\vcpkg\installed\x64-windows\lib\TKDEPLY.lib;comctl32.lib;user32.lib;gdi32.lib;C:\vcpkg\installed\x64-windows\lib\TKFillet.lib;C:\vcpkg\installed\x64-windows\lib\TKBool.lib;C:\vcpkg\installed\x64-windows\lib\TKXSBase.lib;C:\vcpkg\installed\x64-windows\lib\TKStd.lib;C:\vcpkg\installed\x64-windows\lib\TKStdL.lib;C:\vcpkg\installed\x64-windows\lib\TKBinTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKBin.lib;C:\vcpkg\installed\x64-windows\lib\TKBinL.lib;C:\vcpkg\installed\x64-windows\lib\TKXml.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlL.lib;C:\vcpkg\installed\x64-windows\lib\TKRWMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKDE.lib;C:\vcpkg\installed\x64-windows\lib\TKXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKVCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKV3d.lib;C:\vcpkg\installed\x64-windows\lib\TKHLR.lib;C:\vcpkg\installed\x64-windows\lib\TKMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKService.lib;C:\vcpkg\installed\x64-windows\lib\TKCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKBO.lib;C:\vcpkg\installed\x64-windows\lib\TKPrim.lib;C:\vcpkg\installed\x64-windows\lib\TKShHealing.lib;C:\vcpkg\installed\x64-windows\lib\TKTopAlgo.lib;C:\vcpkg\installed\x64-windows\lib\TKGeomAlgo.lib;C:\vcpkg\installed\x64-windows\lib\TKBRep.lib;C:\vcpkg\installed\x64-windows\lib\TKGeomBase.lib;C:\vcpkg\installed\x64-windows\lib\TKG3d.lib;C:\vcpkg\installed\x64-windows\lib\TKG2d.lib;C:\vcpkg\installed\x64-windows\lib\TKMath.lib;C:\vcpkg\installed\x64-windows\lib\TKLCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKCDF.lib;C:\vcpkg\installed\x64-windows\lib\TKernel.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/MyCadApplication/build/MinSizeRel/MyCadApplication.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/MyCadApplication/build/MinSizeRel/MyCadApplication.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/opencascade" /external:I "C:/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\MyCadApplication\include;D:\MyCadApplication\include\MyCad;C:\vcpkg\installed\x64-windows\include\opencascade;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/MyCadApplication/build/RelWithDebInfo/MyCadApplication.exe -installedDir C:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\lib\TKOffset.lib;C:\vcpkg\installed\x64-windows\lib\TKFeat.lib;C:\vcpkg\installed\x64-windows\lib\TKXMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKOpenGl.lib;C:\vcpkg\installed\x64-windows\lib\TKMeshVS.lib;C:\vcpkg\installed\x64-windows\lib\TKDESTEP.lib;C:\vcpkg\installed\x64-windows\lib\TKDEIGES.lib;C:\vcpkg\installed\x64-windows\lib\TKDESTL.lib;C:\vcpkg\installed\x64-windows\lib\TKDEVRML.lib;C:\vcpkg\installed\x64-windows\lib\TKDECascade.lib;C:\vcpkg\installed\x64-windows\lib\TKBinXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKDEOBJ.lib;C:\vcpkg\installed\x64-windows\lib\TKDEGLTF.lib;C:\vcpkg\installed\x64-windows\lib\TKDEPLY.lib;comctl32.lib;user32.lib;gdi32.lib;C:\vcpkg\installed\x64-windows\lib\TKFillet.lib;C:\vcpkg\installed\x64-windows\lib\TKBool.lib;C:\vcpkg\installed\x64-windows\lib\TKXSBase.lib;C:\vcpkg\installed\x64-windows\lib\TKStd.lib;C:\vcpkg\installed\x64-windows\lib\TKStdL.lib;C:\vcpkg\installed\x64-windows\lib\TKBinTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKTObj.lib;C:\vcpkg\installed\x64-windows\lib\TKBin.lib;C:\vcpkg\installed\x64-windows\lib\TKBinL.lib;C:\vcpkg\installed\x64-windows\lib\TKXml.lib;C:\vcpkg\installed\x64-windows\lib\TKXmlL.lib;C:\vcpkg\installed\x64-windows\lib\TKRWMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKDE.lib;C:\vcpkg\installed\x64-windows\lib\TKXCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKVCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKV3d.lib;C:\vcpkg\installed\x64-windows\lib\TKHLR.lib;C:\vcpkg\installed\x64-windows\lib\TKMesh.lib;C:\vcpkg\installed\x64-windows\lib\TKService.lib;C:\vcpkg\installed\x64-windows\lib\TKCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKBO.lib;C:\vcpkg\installed\x64-windows\lib\TKPrim.lib;C:\vcpkg\installed\x64-windows\lib\TKShHealing.lib;C:\vcpkg\installed\x64-windows\lib\TKTopAlgo.lib;C:\vcpkg\installed\x64-windows\lib\TKGeomAlgo.lib;C:\vcpkg\installed\x64-windows\lib\TKBRep.lib;C:\vcpkg\installed\x64-windows\lib\TKGeomBase.lib;C:\vcpkg\installed\x64-windows\lib\TKG3d.lib;C:\vcpkg\installed\x64-windows\lib\TKG2d.lib;C:\vcpkg\installed\x64-windows\lib\TKMath.lib;C:\vcpkg\installed\x64-windows\lib\TKLCAF.lib;C:\vcpkg\installed\x64-windows\lib\TKCDF.lib;C:\vcpkg\installed\x64-windows\lib\TKernel.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/MyCadApplication/build/RelWithDebInfo/MyCadApplication.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/MyCadApplication/build/RelWithDebInfo/MyCadApplication.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\MyCadApplication\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/MyCadApplication/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-file D:/MyCadApplication/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;C:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEApplicationFrameworkTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADECompileDefinitionsAndFlags-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfig.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEDataExchangeTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEFoundationClassesTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingAlgorithmsTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEModelingDataTargets.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\opencascade\OpenCASCADEVisualizationTargets.cmake;C:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\MyCadApplication\build\CMakeFiles\3.31.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\MyCadApplication\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\MyCadApplication\src\main.cpp" />
    <ClCompile Include="D:\MyCadApplication\src\AppCore.cpp" />
    <ClCompile Include="D:\MyCadApplication\src\MainWindow.cpp" />
    <ClCompile Include="D:\MyCadApplication\src\GeometryOperations.cpp" />
    <ClCompile Include="D:\MyCadApplication\src\Utils.cpp" />
    <ClCompile Include="D:\MyCadApplication\src\UndoRedoSystem.cpp" />
    <ClCompile Include="D:\MyCadApplication\src\ModelTreeManager.cpp" />
    <ResourceCompile Include="D:\MyCadApplication\resources\resource.rc" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\AppCore.h" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\MainWindow.h" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\GeometryOperations.h" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\Utils.h" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\Constants.h" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\UndoRedoSystem.h" />
    <ClInclude Include="D:\MyCadApplication\include\MyCad\ModelTreeManager.h" />
    <ClInclude Include="D:\MyCadApplication\include\resource.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\MyCadApplication\build\ZERO_CHECK.vcxproj">
      <Project>{C8052788-5054-376F-BA25-361432419525}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>