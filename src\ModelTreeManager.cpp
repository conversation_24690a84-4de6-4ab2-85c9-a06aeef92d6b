#include "MyCad/ModelTreeManager.h"
#include "MyCad/AppCore.h"
#include <windowsx.h>
#include <algorithm>

namespace MyCad
{
    ModelTreeManager::ModelTreeManager()
        : m_hTreeView(nullptr), m_hParentWindow(nullptr), m_appCore(nullptr),
          m_hRootItem(nullptr), m_hShapesRoot(nullptr), m_hContextMenu(nullptr),
          m_contextMenuObjectId(-1)
    {
    }

    ModelTreeManager::~ModelTreeManager()
    {
        if (m_hContextMenu)
        {
            DestroyMenu(m_hContextMenu);
        }
    }

    bool ModelTreeManager::Initialize(HWND parentWindow, int x, int y, int width, int height, AppCore* appCore)
    {
        m_hParentWindow = parentWindow;
        m_appCore = appCore;

        // Create the tree view control
        m_hTreeView = CreateWindowEx(
            WS_EX_CLIENTEDGE,
            WC_TREEVIEW,
            L"Model Tree",
            WS_VISIBLE | WS_CHILD | WS_BORDER | TVS_HASLINES | TVS_HASBUTTONS |
            TVS_LINESATROOT | TVS_SHOWSELALWAYS | TVS_EDITLABELS,
            x, y, width, height,
            parentWindow,
            (HMENU)2000, // Control ID
            GetModuleHandle(nullptr),
            nullptr
        );

        if (!m_hTreeView)
        {
            return false;
        }

        SetupTreeViewStyle();
        CreateImageList();
        CreateContextMenu();

        // Create root items
        TVINSERTSTRUCT tvInsert = {};
        tvInsert.hParent = TVI_ROOT;
        tvInsert.hInsertAfter = TVI_LAST;
        tvInsert.item.mask = TVIF_TEXT | TVIF_IMAGE | TVIF_SELECTEDIMAGE | TVIF_STATE;
        tvInsert.item.pszText = const_cast<LPWSTR>(L"Model");
        tvInsert.item.iImage = ICON_FOLDER;
        tvInsert.item.iSelectedImage = ICON_FOLDER;
        tvInsert.item.state = TVIS_EXPANDED;
        tvInsert.item.stateMask = TVIS_EXPANDED;

        m_hRootItem = TreeView_InsertItem(m_hTreeView, &tvInsert);

        // Create shapes folder
        tvInsert.hParent = m_hRootItem;
        tvInsert.item.pszText = const_cast<LPWSTR>(L"Shapes");
        m_hShapesRoot = TreeView_InsertItem(m_hTreeView, &tvInsert);

        // Expand the root items
        TreeView_Expand(m_hTreeView, m_hRootItem, TVE_EXPAND);
        TreeView_Expand(m_hTreeView, m_hShapesRoot, TVE_EXPAND);

        return true;
    }

    void ModelTreeManager::SetupTreeViewStyle()
    {
        if (!m_hTreeView) return;

        // Set extended styles
        DWORD dwStyle = TreeView_GetExtendedStyle(m_hTreeView);
        dwStyle |= TVS_EX_DOUBLEBUFFER;
        TreeView_SetExtendedStyle(m_hTreeView, dwStyle, dwStyle);

        // Set colors
        TreeView_SetBkColor(m_hTreeView, RGB(255, 255, 255));
        TreeView_SetTextColor(m_hTreeView, RGB(0, 0, 0));
        TreeView_SetLineColor(m_hTreeView, RGB(128, 128, 128));
    }

    void ModelTreeManager::CreateImageList()
    {
        if (!m_hTreeView) return;

        // Create image list for icons
        HIMAGELIST hImageList = ImageList_Create(16, 16, ILC_COLOR32 | ILC_MASK, 4, 4);

        if (hImageList)
        {
            // Load standard icons (you can replace these with custom icons)
            HICON hIconVisible = LoadIcon(nullptr, IDI_APPLICATION);
            HICON hIconHidden = LoadIcon(nullptr, IDI_QUESTION);
            HICON hIconShape = LoadIcon(nullptr, IDI_ASTERISK);
            HICON hIconFolder = LoadIcon(nullptr, IDI_WINLOGO);

            ImageList_AddIcon(hImageList, hIconVisible);   // ICON_VISIBLE
            ImageList_AddIcon(hImageList, hIconHidden);    // ICON_HIDDEN
            ImageList_AddIcon(hImageList, hIconShape);     // ICON_SHAPE
            ImageList_AddIcon(hImageList, hIconFolder);    // ICON_FOLDER

            TreeView_SetImageList(m_hTreeView, hImageList, TVSIL_NORMAL);
        }
    }

    void ModelTreeManager::CreateContextMenu()
    {
        m_hContextMenu = CreatePopupMenu();
        if (m_hContextMenu)
        {
            AppendMenu(m_hContextMenu, MF_STRING, ID_CONTEXT_RENAME, L"Rename");
            AppendMenu(m_hContextMenu, MF_SEPARATOR, 0, nullptr);
            AppendMenu(m_hContextMenu, MF_STRING, ID_CONTEXT_HIDE, L"Hide");
            AppendMenu(m_hContextMenu, MF_STRING, ID_CONTEXT_SHOW, L"Show");
            AppendMenu(m_hContextMenu, MF_SEPARATOR, 0, nullptr);
            AppendMenu(m_hContextMenu, MF_STRING, ID_CONTEXT_DELETE, L"Delete");
            AppendMenu(m_hContextMenu, MF_SEPARATOR, 0, nullptr);
            AppendMenu(m_hContextMenu, MF_STRING, ID_CONTEXT_PROPERTIES, L"Properties");
        }
    }

    void ModelTreeManager::AddObject(int objectId, const std::string& name, bool visible)
    {
        if (!m_hTreeView || !m_hShapesRoot) return;

        // Convert string to wide string
        std::wstring wideName(name.begin(), name.end());

        TVINSERTSTRUCT tvInsert = {};
        tvInsert.hParent = m_hShapesRoot;
        tvInsert.hInsertAfter = TVI_LAST;
        tvInsert.item.mask = TVIF_TEXT | TVIF_IMAGE | TVIF_SELECTEDIMAGE | TVIF_PARAM;
        tvInsert.item.pszText = const_cast<LPWSTR>(wideName.c_str());
        tvInsert.item.iImage = visible ? ICON_VISIBLE : ICON_HIDDEN;
        tvInsert.item.iSelectedImage = visible ? ICON_VISIBLE : ICON_HIDDEN;
        tvInsert.item.lParam = objectId;

        HTREEITEM hItem = TreeView_InsertItem(m_hTreeView, &tvInsert);

        if (hItem)
        {
            m_objectToTreeItem[objectId] = hItem;
            m_treeItemToObject[hItem] = objectId;
            m_treeItems.emplace_back(objectId, name, visible, false);

            // Expand the shapes folder to show the new item
            TreeView_Expand(m_hTreeView, m_hShapesRoot, TVE_EXPAND);
        }
    }

    void ModelTreeManager::RemoveObject(int objectId)
    {
        auto it = m_objectToTreeItem.find(objectId);
        if (it != m_objectToTreeItem.end())
        {
            HTREEITEM hItem = it->second;

            // Remove from maps
            m_treeItemToObject.erase(hItem);
            m_objectToTreeItem.erase(it);

            // Remove from tree items vector
            m_treeItems.erase(
                std::remove_if(m_treeItems.begin(), m_treeItems.end(),
                    [objectId](const TreeItemData& item) { return item.objectId == objectId; }),
                m_treeItems.end()
            );

            // Remove from tree view
            TreeView_DeleteItem(m_hTreeView, hItem);
        }
    }

    void ModelTreeManager::UpdateObjectName(int objectId, const std::string& newName)
    {
        auto it = m_objectToTreeItem.find(objectId);
        if (it != m_objectToTreeItem.end())
        {
            HTREEITEM hItem = it->second;
            std::wstring wideName(newName.begin(), newName.end());

            TVITEM tvItem = {};
            tvItem.hItem = hItem;
            tvItem.mask = TVIF_TEXT;
            tvItem.pszText = const_cast<LPWSTR>(wideName.c_str());

            TreeView_SetItem(m_hTreeView, &tvItem);

            // Update in tree items vector
            TreeItemData* itemData = FindTreeItemData(objectId);
            if (itemData)
            {
                itemData->name = newName;
            }
        }
    }

    void ModelTreeManager::UpdateObjectVisibility(int objectId, bool visible)
    {
        auto it = m_objectToTreeItem.find(objectId);
        if (it != m_objectToTreeItem.end())
        {
            HTREEITEM hItem = it->second;

            TVITEM tvItem = {};
            tvItem.hItem = hItem;
            tvItem.mask = TVIF_IMAGE | TVIF_SELECTEDIMAGE;
            tvItem.iImage = visible ? ICON_VISIBLE : ICON_HIDDEN;
            tvItem.iSelectedImage = visible ? ICON_VISIBLE : ICON_HIDDEN;

            TreeView_SetItem(m_hTreeView, &tvItem);

            // Update in tree items vector
            TreeItemData* itemData = FindTreeItemData(objectId);
            if (itemData)
            {
                itemData->isVisible = visible;
            }
        }
    }

    void ModelTreeManager::ClearAll()
    {
        if (!m_hTreeView) return;

        // Clear all data structures
        m_objectToTreeItem.clear();
        m_treeItemToObject.clear();
        m_treeItems.clear();

        // Remove all child items from shapes root
        if (m_hShapesRoot)
        {
            HTREEITEM hChild = TreeView_GetChild(m_hTreeView, m_hShapesRoot);
            while (hChild)
            {
                HTREEITEM hNext = TreeView_GetNextSibling(m_hTreeView, hChild);
                TreeView_DeleteItem(m_hTreeView, hChild);
                hChild = hNext;
            }
        }
    }

    TreeItemData* ModelTreeManager::FindTreeItemData(int objectId)
    {
        auto it = std::find_if(m_treeItems.begin(), m_treeItems.end(),
            [objectId](const TreeItemData& item) { return item.objectId == objectId; });

        return (it != m_treeItems.end()) ? &(*it) : nullptr;
    }

    void ModelTreeManager::Resize(int x, int y, int width, int height)
    {
        if (m_hTreeView)
        {
            SetWindowPos(m_hTreeView, nullptr, x, y, width, height, SWP_NOZORDER);
        }
    }

    void ModelTreeManager::Show(bool show)
    {
        if (m_hTreeView)
        {
            ShowWindow(m_hTreeView, show ? SW_SHOW : SW_HIDE);
        }
    }

    void ModelTreeManager::SelectObject(int objectId)
    {
        auto it = m_objectToTreeItem.find(objectId);
        if (it != m_objectToTreeItem.end())
        {
            TreeView_SelectItem(m_hTreeView, it->second);

            TreeItemData* itemData = FindTreeItemData(objectId);
            if (itemData)
            {
                itemData->isSelected = true;
            }
        }
    }

    void ModelTreeManager::DeselectAll()
    {
        TreeView_SelectItem(m_hTreeView, nullptr);

        for (auto& item : m_treeItems)
        {
            item.isSelected = false;
        }
    }

    std::vector<int> ModelTreeManager::GetSelectedObjects() const
    {
        std::vector<int> selectedIds;

        for (const auto& item : m_treeItems)
        {
            if (item.isSelected)
            {
                selectedIds.push_back(item.objectId);
            }
        }

        return selectedIds;
    }

    void ModelTreeManager::HandleTreeViewNotify(LPNMHDR pnmh)
    {
        if (!pnmh || pnmh->hwndFrom != m_hTreeView) return;

        switch (pnmh->code)
        {
        case TVN_SELCHANGED:
        {
            LPNMTREEVIEW pnmtv = (LPNMTREEVIEW)pnmh;
            if (pnmtv->itemNew.hItem)
            {
                auto it = m_treeItemToObject.find(pnmtv->itemNew.hItem);
                if (it != m_treeItemToObject.end())
                {
                    int objectId = it->second;
                    if (OnObjectSelected)
                    {
                        OnObjectSelected(objectId);
                    }
                }
            }
            break;
        }
        case NM_RCLICK:
        {
            POINT pt;
            GetCursorPos(&pt);
            ScreenToClient(m_hTreeView, &pt);

            TVHITTESTINFO hitTest = {};
            hitTest.pt = pt;
            HTREEITEM hItem = TreeView_HitTest(m_hTreeView, &hitTest);

            if (hItem)
            {
                auto it = m_treeItemToObject.find(hItem);
                if (it != m_treeItemToObject.end())
                {
                    m_contextMenuObjectId = it->second;
                    GetCursorPos(&pt);
                    ShowContextMenu(pt.x, pt.y, m_contextMenuObjectId);
                }
            }
            break;
        }
        case NM_DBLCLK:
        {
            HandleDoubleClick();
            break;
        }
        }
    }

    void ModelTreeManager::ShowContextMenu(int x, int y, int objectId)
    {
        if (!m_hContextMenu) return;

        m_contextMenuObjectId = objectId;

        // Update menu items based on object state
        TreeItemData* itemData = FindTreeItemData(objectId);
        if (itemData)
        {
            // Update hide/show menu items
            ModifyMenu(m_hContextMenu, ID_CONTEXT_HIDE, MF_BYCOMMAND | (itemData->isVisible ? MF_ENABLED : MF_GRAYED), ID_CONTEXT_HIDE, L"Hide");
            ModifyMenu(m_hContextMenu, ID_CONTEXT_SHOW, MF_BYCOMMAND | (!itemData->isVisible ? MF_ENABLED : MF_GRAYED), ID_CONTEXT_SHOW, L"Show");
        }

        // Show the context menu
        UINT result = TrackPopupMenu(m_hContextMenu, TPM_RETURNCMD | TPM_RIGHTBUTTON, x, y, 0, m_hParentWindow, nullptr);

        if (result > 0)
        {
            HandleContextMenuCommand(result);
        }
    }

    void ModelTreeManager::HandleContextMenuCommand(UINT commandId)
    {
        if (m_contextMenuObjectId == -1) return;

        switch (commandId)
        {
        case ID_CONTEXT_RENAME:
        {
            auto it = m_objectToTreeItem.find(m_contextMenuObjectId);
            if (it != m_objectToTreeItem.end())
            {
                TreeView_EditLabel(m_hTreeView, it->second);
            }
            break;
        }
        case ID_CONTEXT_DELETE:
        {
            if (OnObjectDeleted)
            {
                OnObjectDeleted(m_contextMenuObjectId);
            }
            break;
        }
        case ID_CONTEXT_HIDE:
        {
            if (OnVisibilityChanged)
            {
                OnVisibilityChanged(m_contextMenuObjectId, false);
            }
            break;
        }
        case ID_CONTEXT_SHOW:
        {
            if (OnVisibilityChanged)
            {
                OnVisibilityChanged(m_contextMenuObjectId, true);
            }
            break;
        }
        case ID_CONTEXT_PROPERTIES:
        {
            // TODO: Implement properties dialog
            MessageBox(m_hParentWindow, L"Properties dialog not implemented yet", L"Info", MB_OK | MB_ICONINFORMATION);
            break;
        }
        }

        m_contextMenuObjectId = -1;
    }

    void ModelTreeManager::HandleDoubleClick()
    {
        HTREEITEM hSelected = TreeView_GetSelection(m_hTreeView);
        if (hSelected)
        {
            auto it = m_treeItemToObject.find(hSelected);
            if (it != m_treeItemToObject.end())
            {
                int objectId = it->second;
                TreeItemData* itemData = FindTreeItemData(objectId);
                if (itemData && OnVisibilityChanged)
                {
                    // Toggle visibility on double-click
                    OnVisibilityChanged(objectId, !itemData->isVisible);
                }
            }
        }
    }

    void ModelTreeManager::RefreshTree()
    {
        if (m_hTreeView)
        {
            InvalidateRect(m_hTreeView, nullptr, TRUE);
            UpdateWindow(m_hTreeView);
        }
    }

    void ModelTreeManager::ExpandAll()
    {
        if (m_hTreeView && m_hRootItem)
        {
            TreeView_Expand(m_hTreeView, m_hRootItem, TVE_EXPAND);
            TreeView_Expand(m_hTreeView, m_hShapesRoot, TVE_EXPAND);
        }
    }

    void ModelTreeManager::CollapseAll()
    {
        if (m_hTreeView && m_hRootItem)
        {
            TreeView_Expand(m_hTreeView, m_hShapesRoot, TVE_COLLAPSE);
        }
    }
}
