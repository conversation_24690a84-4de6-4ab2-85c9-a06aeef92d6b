#pragma once

#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <TopoDS_Shape.hxx>
#include <AIS_InteractiveObject.hxx>
#include <Quantity_Color.hxx>

namespace MyCad
{
    // Forward declaration
    class AppCore;

    // Scene state for undo/redo
    struct SceneState
    {
        struct ObjectState
        {
            TopoDS_Shape shape;
            std::string name;
            Quantity_Color color;
            int id;
            bool isVisible;

            ObjectState(const TopoDS_Shape& s, const std::string& n, const Quantity_Color& c, int objId, bool visible = true)
                : shape(s), name(n), color(c), id(objId), isVisible(visible) {}
        };

        std::vector<ObjectState> objects;
        TopoDS_Shape currentShapeToExport;
        double currentPlacementXOffset;
        int nextObjectId;
        std::string description;

        SceneState(const std::string& desc = "") : description(desc), currentPlacementXOffset(0.0), nextObjectId(0) {}
    };

    // Base command interface
    class ICommand
    {
    public:
        virtual ~ICommand() = default;
        virtual void Execute() = 0;
        virtual void Undo() = 0;
        virtual std::string GetDescription() const = 0;
        virtual bool CanUndo() const { return true; }
    };

    // Command that captures scene state before and after operation
    class SceneCommand : public ICommand
    {
    public:
        SceneCommand(AppCore* appCore, const std::string& description);
        virtual ~SceneCommand() = default;

        void Execute() override;
        void Undo() override;
        std::string GetDescription() const override { return m_description; }

        // Call this before executing the actual operation
        void CaptureBeforeState();

        // Call this after executing the actual operation
        void CaptureAfterState();

        // Set the operation to execute
        void SetOperation(std::function<void()> operation);

    private:
        AppCore* m_appCore;
        std::string m_description;
        std::unique_ptr<SceneState> m_beforeState;
        std::unique_ptr<SceneState> m_afterState;
        std::function<void()> m_operation;
        bool m_executed;
    };

    // Specific command implementations
    class AddObjectCommand : public ICommand
    {
    public:
        AddObjectCommand(AppCore* appCore, const TopoDS_Shape& shape, const Quantity_Color& color,
                        const std::string& name, bool fitView = true);

        void Execute() override;
        void Undo() override;
        std::string GetDescription() const override;

    private:
        AppCore* m_appCore;
        TopoDS_Shape m_shape;
        Quantity_Color m_color;
        std::string m_name;
        bool m_fitView;
        int m_objectId;
        bool m_executed;
    };

    class RemoveObjectCommand : public ICommand
    {
    public:
        RemoveObjectCommand(AppCore* appCore, int objectId);

        void Execute() override;
        void Undo() override;
        std::string GetDescription() const override;

    private:
        AppCore* m_appCore;
        int m_objectId;
        TopoDS_Shape m_shape;
        Quantity_Color m_color;
        std::string m_name;
        bool m_executed;
    };

    class ClearSceneCommand : public ICommand
    {
    public:
        ClearSceneCommand(AppCore* appCore);

        void Execute() override;
        void Undo() override;
        std::string GetDescription() const override;

    private:
        AppCore* m_appCore;
        std::unique_ptr<SceneState> m_previousState;
        bool m_executed;
    };

    // Undo/Redo manager
    class UndoRedoManager
    {
    public:
        UndoRedoManager(size_t maxHistorySize = 50);
        ~UndoRedoManager() = default;

        // Execute a command and add it to history
        void ExecuteCommand(std::unique_ptr<ICommand> command);

        // Undo/Redo operations
        bool CanUndo() const;
        bool CanRedo() const;
        void Undo();
        void Redo();

        // History management
        void Clear();
        size_t GetHistorySize() const { return m_undoStack.size(); }
        size_t GetRedoSize() const { return m_redoStack.size(); }

        // Get descriptions for UI
        std::string GetUndoDescription() const;
        std::string GetRedoDescription() const;

    private:
        std::vector<std::unique_ptr<ICommand>> m_undoStack;
        std::vector<std::unique_ptr<ICommand>> m_redoStack;
        size_t m_maxHistorySize;

        void TrimHistory();
    };

} // namespace MyCad
