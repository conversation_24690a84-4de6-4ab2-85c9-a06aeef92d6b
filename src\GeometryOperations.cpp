#ifndef WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>

#include "MyCad/GeometryOperations.h"
#include "MyCad/Utils.h"


#include <string>
#include <cmath>
#include <stdexcept>

// OpenCASCADE Headers
#include <BRepPrimAPI_MakeBox.hxx>
#include <BRepPrimAPI_MakeCylinder.hxx>
#include <BRepAlgoAPI_Cut.hxx>
#include <BRepAlgoAPI_Fuse.hxx>
#include <BRepOffsetAPI_MakeThickSolid.hxx>
#include <BRepPrimAPI_MakePrism.hxx>
#include <BRepAdaptor_Surface.hxx>
#include <gp_Pnt.hxx>
#include <gp_Ax2.hxx>
#include <gp_Dir.hxx>
#include <gp_Vec.hxx>
#include <Precision.hxx>
#include <BRepBuilderAPI_Transform.hxx>
#include <gp_Trsf.hxx>
#include <STEPControl_Reader.hxx>
#include <IGESControl_Reader.hxx>
#include <STEPControl_Writer.hxx>
#include <IGESControl_Writer.hxx>
#include <IFSelect_ReturnStatus.hxx>
#include <Standard_Failure.hxx>
#include <GeomAbs_JoinType.hxx>
#include <TopTools_ListOfShape.hxx>
#include <TopTools_ListIteratorOfListOfShape.hxx>
#include <TopoDS.hxx>
#include <TopAbs.hxx>
#include <BRepFilletAPI_MakeFillet.hxx>
#include <BRepFilletAPI_MakeChamfer.hxx>
#include <BRepPrimAPI_MakeRevol.hxx>
#include <gp_Ax1.hxx>
#include <TopExp_Explorer.hxx>
#include <TopoDS_Edge.hxx>
#include <BRepAdaptor_Curve.hxx>
#include <GC_MakeSegment.hxx>
#include <Geom_Line.hxx>
// #include <BRepClass_SolidClassifier.hxx> // REMOVED as per previous instruction

// Additional includes for trim/untrim operations
#include <BRepBuilderAPI_MakeFace.hxx>
#include <BRepBuilderAPI_MakeWire.hxx>
#include <BRepBuilderAPI_MakeEdge.hxx>
#include <BRepAlgoAPI_Section.hxx>
#include <BRepOffsetAPI_MakeOffset.hxx>
#include <BRepFill_OffsetWire.hxx>
#include <GeomAPI_ProjectPointOnSurf.hxx>
#include <GeomAPI_ProjectPointOnCurve.hxx>
#include <BRepClass_FaceClassifier.hxx>
#include <BRepAdaptor_Curve.hxx>
#include <BRepAdaptor_Surface.hxx>
#include <ShapeAnalysis_Wire.hxx>
#include <ShapeAnalysis_Surface.hxx>
#include <BRepTools_WireExplorer.hxx>
#include <TopExp_Explorer.hxx>
#include <BRep_Tool.hxx>
#include <GeomLib_IsPlanarSurface.hxx>
#include <gp_Pln.hxx>
#include <Geom_Plane.hxx>
#include <BRepBuilderAPI_MakePolygon.hxx>
#include <BRepAlgoAPI_Common.hxx>
#include <BRepAlgoAPI_Splitter.hxx>
#include <TopTools_ListIteratorOfListOfShape.hxx>
#include <BRepGProp.hxx>
#include <GProp_GProps.hxx>
#include <gp_Trsf.hxx>
#include <gp_Ax2.hxx>
#include <gp_Pln.hxx>
#include <Bnd_Box.hxx>
#include <BRepBndLib.hxx>
#include <GeomAPI_IntCS.hxx>
#include <Geom2dAPI_InterCurveCurve.hxx>
#include <GeomAPI_ProjectPointOnCurve.hxx>
#include <Geom_TrimmedCurve.hxx>
#include <GeomAdaptor_Curve.hxx>
#include <Geom2dAdaptor_Curve.hxx>
#include <IntRes2d_IntersectionPoint.hxx>
#include <BRepAdaptor_Curve.hxx>
#include <BRepBuilderAPI_MakeVertex.hxx>
#include <BRepBuilderAPI_MakeWire.hxx>
#include <BRepFeat_SplitShape.hxx>
#include <BRepTools.hxx>
#include <BRepAlgoAPI_Splitter.hxx>
#include <BRepOffsetAPI_MakeOffsetShape.hxx>
#include <BRepOffsetAPI_MakeThickSolid.hxx>
#include <BRepOffsetAPI_MakeOffset.hxx>
#include <BRepOffsetAPI_MakePipe.hxx>
#include <BRepOffsetAPI_MakeFilling.hxx>
#include <Geom_OffsetSurface.hxx>
#include <BRepOffset_Mode.hxx>
#include <GeomAbs_JoinType.hxx>
#include <TopExp.hxx>
#include <TopExp_Explorer.hxx>
#include <BRepCheck_Wire.hxx>
#include <BRepCheck_Analyzer.hxx>
#include <gp_Circ.hxx>
#include <GC_MakeCircle.hxx>
#include <Geom_TrimmedCurve.hxx>
#include <Geom_Circle.hxx>
#include <algorithm>
#include <vector>

namespace MyCadGeom {

TopoDS_Shape CreateCube(double size) {
    try {
        BRepPrimAPI_MakeBox boxMaker(gp_Pnt(-size / 2.0, -size / 2.0, -size / 2.0), size, size, size);
        return boxMaker.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Error Creating Cube", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

TopoDS_Shape CreateCylinder(const gp_Ax2& axis, double radius, double height) {
    try {
        BRepPrimAPI_MakeCylinder cylMaker(axis, radius, height);
        return cylMaker.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Error Creating Cylinder", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

TopoDS_Shape CutShapes(const TopoDS_Shape& objectShape, const TopoDS_Shape& toolShape) {
    try {
        if (objectShape.IsNull() || toolShape.IsNull()) {
            MessageBoxA(NULL, "One or both shapes for cut are null", "Cut Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }
        BRepAlgoAPI_Cut cutOp(objectShape, toolShape);
        cutOp.Build();
        if (!cutOp.IsDone()) {
            MessageBoxA(NULL, "Cut operation failed", "Cut Error", MB_OK | MB_ICONERROR);
            return objectShape;
        }
        return cutOp.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Cut Operation Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

TopoDS_Shape FuseShapes(const TopoDS_Shape& shape1, const TopoDS_Shape& shape2) {
    try {
        if (shape1.IsNull() || shape2.IsNull()) {
            MessageBoxA(NULL, "One of the shapes for fusion is null", "Fusion Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        BRepAlgoAPI_Fuse fuseMaker(shape1, shape2);
        fuseMaker.Build();

        if (!fuseMaker.IsDone() || fuseMaker.Shape().IsNull()) {
            MessageBoxA(NULL, "Fusion operation failed", "Fusion Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        return fuseMaker.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Fusion Operation Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}


TopoDS_Shape ThickenShape(const TopoDS_Shape& shape, double thickness, const TopTools_ListOfShape& facesToRemove) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for thicken is null", "Thick Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }
        BRepOffsetAPI_MakeThickSolid thickOp;
        thickOp.MakeThickSolidByJoin(shape, facesToRemove, thickness, Precision::Confusion(),
                                     BRepOffset_Skin, false, false, GeomAbs_Arc, false);
        if (!thickOp.IsDone()) {
            MessageBoxA(NULL, "Thick operation failed. Shape might not be suitable.", "Thick Error", MB_OK | MB_ICONERROR);
            return shape;
        }
        return thickOp.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Thick Operation Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

TopoDS_Shape OffsetFace(const TopoDS_Solid& parentSolid, const TopoDS_Face& selectedFace, double thickness) {
     if (parentSolid.IsNull() || selectedFace.IsNull()) {
        MessageBoxA(NULL, "Invalid input for OffsetFace: Parent solid or selected face is null.", "Offset Face Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
    try {
        BRepAdaptor_Surface surfAdaptor(selectedFace, Standard_True);
        gp_Pnt pntOnFace;
        gp_Vec normalVec, d1u, d1v;
        Standard_Real uMid = (surfAdaptor.FirstUParameter() + surfAdaptor.LastUParameter()) / 2.0;
        Standard_Real vMid = (surfAdaptor.FirstVParameter() + surfAdaptor.LastVParameter()) / 2.0;
        surfAdaptor.D1(uMid, vMid, pntOnFace, d1u, d1v);
        normalVec = d1u.Crossed(d1v);
        normalVec.Normalize();

        if (selectedFace.Orientation() == TopAbs_REVERSED) {
            normalVec.Reverse();
        }

        gp_Dir extrusionDir(normalVec);
        if (std::abs(thickness) < Precision::Confusion()) {
            return parentSolid;
        }

        BRepPrimAPI_MakePrism prismMaker(selectedFace, gp_Vec(extrusionDir.XYZ()) * thickness, Standard_False, Standard_True);
        if (!prismMaker.IsDone() || prismMaker.Shape().IsNull()) {
            MessageBoxA(NULL, "Failed to create extrusion prism for offset.", "Offset Face Error", MB_OK | MB_ICONERROR);
            return parentSolid;
        }
        TopoDS_Shape prismShape = prismMaker.Shape();

        TopoDS_Shape resultShape;
        if (thickness > 0) {
            resultShape = FuseShapes(parentSolid, prismShape);
        } else {
            resultShape = CutShapes(parentSolid, prismShape);
        }
        if(resultShape.IsNull() || resultShape.IsSame(parentSolid)){
             MessageBoxA(NULL, "Offset Face Boolean operation did not produce a new valid shape.", "Offset Face Error", MB_OK | MB_ICONWARNING);
             return parentSolid;
        }
        return resultShape;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Offset Face Operation Error (Exception)", MB_OK | MB_ICONERROR);
        return parentSolid;
    }
}


TopoDS_Shape TranslateShape(const TopoDS_Shape& shape, double offsetX, double offsetY, double offsetZ) {
    if (shape.IsNull()) return shape;
    gp_Trsf translation;
    translation.SetTranslation(gp_Vec(offsetX, offsetY, offsetZ));
    BRepBuilderAPI_Transform transformer(shape, translation);
    return transformer.Shape();
}

TopoDS_Shape ImportStepIgesFile(HWND hwndParent, const std::string& filePath) {
    TopoDS_Shape shape;
    std::string ext = MyCadUtils::GetFileExtension(filePath);
    try {
        if (ext == "stp" || ext == "step") {
            STEPControl_Reader reader;
            if (reader.ReadFile(filePath.c_str()) == IFSelect_RetDone) {
                reader.TransferRoots();
                shape = reader.OneShape();
            } else {
                MessageBoxW(hwndParent, L"Failed to read STEP file", L"Import Error", MB_OK | MB_ICONERROR);
            }
        } else if (ext == "igs" || ext == "iges") {
            IGESControl_Reader reader;
            if (reader.ReadFile(filePath.c_str()) == IFSelect_RetDone) {
                reader.TransferRoots();
                shape = reader.OneShape();
            } else {
                MessageBoxW(hwndParent, L"Failed to read IGES file", L"Import Error", MB_OK | MB_ICONERROR);
            }
        } else {
            MessageBoxW(hwndParent, L"Unsupported file type for import.", L"Import Error", MB_OK | MB_ICONERROR);
        }
    } catch (const Standard_Failure& e) {
        std::string errStr = e.GetMessageString();
        std::wstring wErrMsg;
        int len = MultiByteToWideChar(CP_UTF8, 0, errStr.c_str(), -1, NULL, 0);
        if (len > 0) {
            wErrMsg.resize(len -1);
            MultiByteToWideChar(CP_UTF8, 0, errStr.c_str(), -1, &wErrMsg[0], len);
             MessageBoxW(hwndParent, wErrMsg.c_str(), L"OCC Import Exception", MB_OK | MB_ICONERROR);
        } else {
             MessageBoxW(hwndParent, L"OCC Import Exception (conversion failed)", L"Error", MB_OK | MB_ICONERROR);
        }
    }
    return shape;
}

bool ExportStepIgesFile(HWND hwndParent, const TopoDS_Shape& shape, const std::string& filePath) {
    if (shape.IsNull()) {
        MessageBoxW(hwndParent, L"No shape to export.", L"Export Error", MB_OK | MB_ICONERROR);
        return false;
    }
    std::string ext = MyCadUtils::GetFileExtension(filePath);
    try {
        if (ext == "stp" || ext == "step") {
            STEPControl_Writer writer;
            if (writer.Transfer(shape, STEPControl_AsIs) == IFSelect_RetDone) {
                if (writer.Write(filePath.c_str()) == IFSelect_RetDone) {
                    MessageBoxW(hwndParent, L"STEP file exported successfully.", L"Export Success", MB_OK | MB_ICONINFORMATION);
                    return true;
                }
            }
            MessageBoxW(hwndParent, L"Failed to write STEP file.", L"Export Error", MB_OK | MB_ICONERROR);
        } else if (ext == "igs" || ext == "iges") {
            IGESControl_Writer writer;
            writer.AddShape(shape);
            if (writer.Write(filePath.c_str())) {
                MessageBoxW(hwndParent, L"IGES file exported successfully.", L"Export Success", MB_OK | MB_ICONINFORMATION);
                return true;
            }
            MessageBoxW(hwndParent, L"Failed to write IGES file.", L"Export Error", MB_OK | MB_ICONERROR);
        } else {
            MessageBoxW(hwndParent, L"Unsupported file type for export.", L"Export Error", MB_OK | MB_ICONERROR);
        }
    } catch (const Standard_Failure& e) {
        std::string errStr = e.GetMessageString();
        std::wstring wErrMsg;
        int len = MultiByteToWideChar(CP_UTF8, 0, errStr.c_str(), -1, NULL, 0);
        if (len > 0) {
            wErrMsg.resize(len-1);
            MultiByteToWideChar(CP_UTF8, 0, errStr.c_str(), -1, &wErrMsg[0], len);
            MessageBoxW(hwndParent, wErrMsg.c_str(), L"OCC Export Exception", MB_OK | MB_ICONERROR);
        } else {
            MessageBoxW(hwndParent, L"OCC Export Exception (conversion failed)", L"Error", MB_OK | MB_ICONERROR);
        }
    }
    return false;
}

gp_Dir GetCorrectedFaceNormal(const TopoDS_Solid& solid, const TopoDS_Face& face) {
    if (face.IsNull()) { // solid parameter not used in this simplified version
        Standard_Failure::Raise("GetCorrectedFaceNormal: Null input face.");
        return gp_Dir(0,0,1);
    }

    BRepAdaptor_Surface surfAdaptor(face, Standard_True);
    gp_Pnt pntOnFace;
    gp_Vec normalVec, d1u, d1v;

    Standard_Real uMid = (surfAdaptor.FirstUParameter() + surfAdaptor.LastUParameter()) / 2.0;
    Standard_Real vMid = (surfAdaptor.FirstVParameter() + surfAdaptor.LastVParameter()) / 2.0;

    try {
        surfAdaptor.D1(uMid, vMid, pntOnFace, d1u, d1v);
        normalVec = d1u.Crossed(d1v);
    } catch (const Standard_Failure&) {
        try {
            surfAdaptor.D1(surfAdaptor.FirstUParameter(), surfAdaptor.FirstVParameter(), pntOnFace, d1u, d1v);
            normalVec = d1u.Crossed(d1v);
        } catch (const Standard_Failure& ) { // Removed e2 as it's not used
            Standard_Failure::Raise("GetCorrectedFaceNormal: Could not compute geometric normal (D1 failed at multiple points).");
            return gp_Dir(0,0,1);
        }
    }

    if (normalVec.Magnitude() < Precision::Confusion()) {
        Standard_Failure::Raise("GetCorrectedFaceNormal: Computed geometric normal has zero magnitude.");
        return gp_Dir(0,0,1);
    }
    normalVec.Normalize();

    gp_Dir finalNormal(normalVec);
    if (face.Orientation() == TopAbs_REVERSED) {
        finalNormal.Reverse();
    }
    return finalNormal;
}


TopoDS_Shape ExtrudeFace(const TopoDS_Solid& solid, const TopoDS_Face& faceToExtrude, double distance, const gp_Dir& direction) {
    if (faceToExtrude.IsNull() || solid.IsNull() || std::abs(distance) < Precision::Confusion()) {
        return solid;
    }

    gp_Dir extrusionDir = direction;
    if (extrusionDir.XYZ().Modulus() < Precision::Confusion()) {
        try {
             extrusionDir = GetCorrectedFaceNormal(solid, faceToExtrude);
        } catch (const Standard_Failure& e) {
            MessageBoxA(NULL, e.GetMessageString(), "Extrude (Normal Calc) Error", MB_OK | MB_ICONERROR);
            return solid;
        }
    }

    BRepPrimAPI_MakePrism prismMaker(faceToExtrude, gp_Vec(extrusionDir.XYZ()) * distance, Standard_False, Standard_True);
    if (!prismMaker.IsDone() || prismMaker.Shape().IsNull()) {
        MessageBoxA(NULL, "Extrude: Prism creation failed.", "Geometry Error", MB_OK | MB_ICONERROR);
        return solid;
    }
    TopoDS_Shape prism = prismMaker.Shape();

    TopoDS_Shape resultShape;
    try {
        if (distance >= 0) {
            resultShape = FuseShapes(solid, prism);
             if (resultShape.IsNull() || resultShape.IsSame(solid)) {
                 MessageBoxA(NULL, "Extrude: Fuse operation failed or no change.", "Geometry Error", MB_OK | MB_ICONERROR);
                 return solid;
            }
        } else {
            resultShape = CutShapes(solid, prism);
            if (resultShape.IsNull() || resultShape.IsSame(solid)) {
                 MessageBoxA(NULL, "Extrude: Cut operation failed or no change.", "Geometry Error", MB_OK | MB_ICONERROR);
                 return solid;
            }
        }
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Extrude Operation Error", MB_OK | MB_ICONERROR);
        return solid;
    }
    return resultShape;
}

TopoDS_Shape FilletEdges(const TopoDS_Shape& shape, const TopTools_ListOfShape& edges, double radius) {
    if (shape.IsNull() || edges.IsEmpty() || radius <= Precision::Confusion()) {
        return shape;
    }
    try {
        BRepFilletAPI_MakeFillet filletMaker(shape);
        for (TopTools_ListIteratorOfListOfShape it(edges); it.More(); it.Next()) {
            const TopoDS_Shape& currentEdgeShape = it.Value();
            if (currentEdgeShape.ShapeType() == TopAbs_EDGE) {
                filletMaker.Add(radius, TopoDS::Edge(currentEdgeShape));
            }
        }
        filletMaker.Build();
        if (!filletMaker.IsDone() || filletMaker.Shape().IsNull()) {
             MessageBoxA(NULL, "Fillet operation failed.", "Geometry Error", MB_OK | MB_ICONERROR);
            return shape;
        }
        return filletMaker.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Fillet Operation Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape ChamferEdges(const TopoDS_Shape& shape, const TopTools_ListOfShape& edges, double distance) {
    if (shape.IsNull() || edges.IsEmpty() || distance <= Precision::Confusion()) {
        return shape;
    }
    try {
        BRepFilletAPI_MakeChamfer chamferMaker(shape);
        for (TopTools_ListIteratorOfListOfShape it(edges); it.More(); it.Next()) {
            const TopoDS_Shape& currentEdgeShape = it.Value();
            if (currentEdgeShape.ShapeType() == TopAbs_EDGE) {
                chamferMaker.Add(distance, TopoDS::Edge(currentEdgeShape));
            }
        }
        chamferMaker.Build();
        if (!chamferMaker.IsDone() || chamferMaker.Shape().IsNull()) {
            MessageBoxA(NULL, "Chamfer operation failed.", "Geometry Error", MB_OK | MB_ICONERROR);
            return shape;
        }
        return chamferMaker.Shape();
    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Chamfer Operation Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape RevolveShape(const TopoDS_Shape& profileShape, const gp_Ax1& axis, double angle) {
    try {
        if (profileShape.IsNull()) {
            return TopoDS_Shape();
        }

        // Validate angle for OCCT 7.9.0
        if (angle <= 0.0 || angle > 360.0) {
            return TopoDS_Shape();
        }

        // Validate axis direction
        if (axis.Direction().XYZ().Modulus() < Precision::Confusion()) {
            return TopoDS_Shape();
        }

        // Convert angle to radians
        double angleRad = angle * M_PI / 180.0;

        // For OCCT 7.9.0 - use direct constructor approach
        BRepPrimAPI_MakeRevol revolMaker(profileShape, axis, angleRad, Standard_False);
        revolMaker.Build();

        // Check if the operation was successful
        if (!revolMaker.IsDone()) {
            return TopoDS_Shape();
        }

        TopoDS_Shape result = revolMaker.Shape();
        if (result.IsNull()) {
            return TopoDS_Shape();
        }

        return result;
    } catch (const Standard_Failure& e) {
        // OCCT 7.9.0 may throw different exceptions
        return TopoDS_Shape();
    } catch (const std::exception& e) {
        return TopoDS_Shape();
    } catch (...) {
        return TopoDS_Shape();
    }
}

// Helper function to extract axis from an edge
gp_Ax1 ExtractAxisFromEdge(const TopoDS_Edge& edge) {
    BRepAdaptor_Curve curve(edge);
    if (curve.GetType() == GeomAbs_Line) {
        gp_Lin line = curve.Line();
        return line.Position();
    }
    // Default axis if not a line
    return gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
}

// ============================================================================
// TRIM AND UNTRIM OPERATIONS
// ============================================================================

TopoDS_Shape TrimSurfaceWithCurve(const TopoDS_Face& surface, const TopoDS_Wire& trimmingWire, bool keepInside) {
    try {
        if (surface.IsNull() || trimmingWire.IsNull()) {
            MessageBoxA(NULL, "Surface or trimming wire is null", "Trim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Project the wire onto the surface if needed
        TopoDS_Wire projectedWire = ProjectWireOnSurface(trimmingWire, surface);
        if (projectedWire.IsNull()) {
            projectedWire = trimmingWire; // Use original if projection fails
        }

        // Get the underlying surface from the face
        Handle(Geom_Surface) geomSurface = BRep_Tool::Surface(surface);
        if (geomSurface.IsNull()) {
            MessageBoxA(NULL, "Failed to get surface geometry", "Trim Error", MB_OK | MB_ICONERROR);
            return surface;
        }

        // Create a new face with the wire as boundary
        BRepBuilderAPI_MakeFace faceMaker(geomSurface, projectedWire, Standard_True);
        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create trimmed face", "Trim Error", MB_OK | MB_ICONERROR);
            return surface;
        }

        TopoDS_Face trimmedFace = faceMaker.Face();

        if (keepInside) {
            return trimmedFace;
        } else {
            // Create the complement (outside part)
            // This is more complex and would require boolean operations
            return trimmedFace; // Simplified for now
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Surface Error", MB_OK | MB_ICONERROR);
        return surface;
    }
}

TopoDS_Shape TrimSurfaceWithSurface(const TopoDS_Face& surfaceToTrim, const TopoDS_Face& trimmingSurface) {
    try {
        if (surfaceToTrim.IsNull() || trimmingSurface.IsNull()) {
            MessageBoxA(NULL, "One or both surfaces are null", "Trim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Find intersection between surfaces
        BRepAlgoAPI_Section sectionOp(surfaceToTrim, trimmingSurface);
        sectionOp.Build();

        if (!sectionOp.IsDone()) {
            MessageBoxA(NULL, "Failed to find intersection between surfaces", "Trim Error", MB_OK | MB_ICONERROR);
            return surfaceToTrim;
        }

        TopoDS_Shape sectionResult = sectionOp.Shape();

        // Extract wires from section result
        TopTools_ListOfShape wires;
        TopExp_Explorer wireExp(sectionResult, TopAbs_WIRE);
        for (; wireExp.More(); wireExp.Next()) {
            wires.Append(wireExp.Current());
        }

        if (wires.IsEmpty()) {
            MessageBoxA(NULL, "No intersection curves found", "Trim Info", MB_OK | MB_ICONINFORMATION);
            return surfaceToTrim;
        }

        // Use the first wire for trimming
        TopoDS_Wire trimmingWire = TopoDS::Wire(wires.First());
        return TrimSurfaceWithCurve(surfaceToTrim, trimmingWire, true);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Surface with Surface Error", MB_OK | MB_ICONERROR);
        return surfaceToTrim;
    }
}

TopoDS_Shape TrimCurveWithPoints(const TopoDS_Edge& curve, const gp_Pnt& startPoint, const gp_Pnt& endPoint) {
    try {
        if (curve.IsNull()) {
            MessageBoxA(NULL, "Curve is null", "Trim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get curve geometry
        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            MessageBoxA(NULL, "Failed to get curve geometry", "Trim Error", MB_OK | MB_ICONERROR);
            return curve;
        }

        // Project points onto curve to find parameters
        GeomAPI_ProjectPointOnCurve projStart(startPoint, geomCurve);
        GeomAPI_ProjectPointOnCurve projEnd(endPoint, geomCurve);

        if (projStart.NbPoints() == 0 || projEnd.NbPoints() == 0) {
            MessageBoxA(NULL, "Failed to project points onto curve", "Trim Error", MB_OK | MB_ICONERROR);
            return curve;
        }

        Standard_Real paramStart = projStart.LowerDistanceParameter();
        Standard_Real paramEnd = projEnd.LowerDistanceParameter();

        // Ensure correct parameter order
        if (paramStart > paramEnd) {
            Standard_Real temp = paramStart;
            paramStart = paramEnd;
            paramEnd = temp;
        }

        // Create trimmed curve
        BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, paramStart, paramEnd);
        if (!edgeMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create trimmed curve", "Trim Error", MB_OK | MB_ICONERROR);
            return curve;
        }

        return edgeMaker.Edge();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Curve Error", MB_OK | MB_ICONERROR);
        return curve;
    }
}

TopoDS_Shape TrimShapeWithPlane(const TopoDS_Shape& shape, const gp_Pln& plane, bool keepPositiveSide) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape is null", "Trim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Create a large face from the plane for cutting
        gp_Pnt planeOrigin = plane.Location();
        gp_Dir planeNormal = plane.Axis().Direction();

        // Create a large rectangular face on the plane
        gp_Dir uDir = plane.XAxis().Direction();
        gp_Dir vDir = plane.YAxis().Direction();

        double size = 1000.0; // Large enough to encompass the shape
        gp_Pnt p1 = planeOrigin.Translated(gp_Vec(uDir.XYZ() * (-size) + vDir.XYZ() * (-size)));
        gp_Pnt p2 = planeOrigin.Translated(gp_Vec(uDir.XYZ() * size + vDir.XYZ() * (-size)));
        gp_Pnt p3 = planeOrigin.Translated(gp_Vec(uDir.XYZ() * size + vDir.XYZ() * size));
        gp_Pnt p4 = planeOrigin.Translated(gp_Vec(uDir.XYZ() * (-size) + vDir.XYZ() * size));

        BRepBuilderAPI_MakePolygon polyMaker;
        polyMaker.Add(p1);
        polyMaker.Add(p2);
        polyMaker.Add(p3);
        polyMaker.Add(p4);
        polyMaker.Close();

        if (!polyMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create cutting plane", "Trim Error", MB_OK | MB_ICONERROR);
            return shape;
        }

        BRepBuilderAPI_MakeFace faceMaker(polyMaker.Wire());
        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create cutting face", "Trim Error", MB_OK | MB_ICONERROR);
            return shape;
        }

        TopoDS_Face cuttingFace = faceMaker.Face();

        // Use boolean operation to trim
        if (keepPositiveSide) {
            // Keep the part on the positive side of the plane normal
            BRepAlgoAPI_Cut cutOp(shape, cuttingFace);
            cutOp.Build();
            if (cutOp.IsDone()) {
                return cutOp.Shape();
            }
        } else {
            // Keep the part on the negative side
            BRepAlgoAPI_Common commonOp(shape, cuttingFace);
            commonOp.Build();
            if (commonOp.IsDone()) {
                return commonOp.Shape();
            }
        }

        return shape;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim with Plane Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape TrimFaceWithWire(const TopoDS_Face& face, const TopoDS_Wire& wire, bool removeInside) {
    try {
        if (face.IsNull() || wire.IsNull()) {
            MessageBoxA(NULL, "Face or wire is null", "Trim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Project wire onto face surface if needed
        TopoDS_Wire projectedWire = ProjectWireOnSurface(wire, face);
        if (projectedWire.IsNull()) {
            projectedWire = wire;
        }

        if (removeInside) {
            // Create face with hole - get surface from face first
            Handle(Geom_Surface) geomSurface = BRep_Tool::Surface(face);
            if (geomSurface.IsNull()) {
                MessageBoxA(NULL, "Failed to get surface from face", "Trim Error", MB_OK | MB_ICONERROR);
                return face;
            }

            // Get outer boundary of the face
            TopoDS_Wire outerWire;
            TopExp_Explorer wireExp(face, TopAbs_WIRE);
            if (wireExp.More()) {
                outerWire = TopoDS::Wire(wireExp.Current());
            }

            if (outerWire.IsNull()) {
                MessageBoxA(NULL, "Failed to get outer boundary of face", "Trim Error", MB_OK | MB_ICONERROR);
                return face;
            }

            BRepBuilderAPI_MakeFace faceMaker(geomSurface, outerWire, Standard_True);
            faceMaker.Add(projectedWire);

            if (!faceMaker.IsDone()) {
                MessageBoxA(NULL, "Failed to create face with hole", "Trim Error", MB_OK | MB_ICONERROR);
                return face;
            }

            return faceMaker.Face();
        } else {
            // Create face only inside the wire
            return TrimSurfaceWithCurve(face, projectedWire, true);
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Face with Wire Error", MB_OK | MB_ICONERROR);
        return face;
    }
}

// ============================================================================
// UNTRIM OPERATIONS
// ============================================================================

TopoDS_Shape UntrimSurface(const TopoDS_Face& trimmedSurface) {
    try {
        if (trimmedSurface.IsNull()) {
            MessageBoxA(NULL, "Trimmed surface is null", "Untrim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the underlying surface
        Handle(Geom_Surface) surface = BRep_Tool::Surface(trimmedSurface);
        if (surface.IsNull()) {
            MessageBoxA(NULL, "Failed to get underlying surface", "Untrim Error", MB_OK | MB_ICONERROR);
            return trimmedSurface;
        }

        // Create a new face with the full surface bounds
        BRepBuilderAPI_MakeFace faceMaker(surface, Precision::Confusion());
        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create untrimmed surface", "Untrim Error", MB_OK | MB_ICONERROR);
            return trimmedSurface;
        }

        return faceMaker.Face();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Untrim Surface Error", MB_OK | MB_ICONERROR);
        return trimmedSurface;
    }
}

TopoDS_Shape UntrimCurve(const TopoDS_Edge& trimmedCurve) {
    try {
        if (trimmedCurve.IsNull()) {
            MessageBoxA(NULL, "Trimmed curve is null", "Untrim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the underlying curve
        Standard_Real first, last;
        Handle(Geom_Curve) curve = BRep_Tool::Curve(trimmedCurve, first, last);
        if (curve.IsNull()) {
            MessageBoxA(NULL, "Failed to get underlying curve", "Untrim Error", MB_OK | MB_ICONERROR);
            return trimmedCurve;
        }

        // Create a new edge with the full curve
        BRepBuilderAPI_MakeEdge edgeMaker(curve);
        if (!edgeMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create untrimmed curve", "Untrim Error", MB_OK | MB_ICONERROR);
            return trimmedCurve;
        }

        return edgeMaker.Edge();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Untrim Curve Error", MB_OK | MB_ICONERROR);
        return trimmedCurve;
    }
}

// ============================================================================
// ADVANCED TRIMMING OPERATIONS
// ============================================================================

TopoDS_Shape TrimShapeWithShape(const TopoDS_Shape& shapeToTrim, const TopoDS_Shape& trimmingTool, bool keepInside) {
    try {
        if (shapeToTrim.IsNull() || trimmingTool.IsNull()) {
            MessageBoxA(NULL, "One or both shapes are null", "Trim Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        if (keepInside) {
            // Keep the intersection
            BRepAlgoAPI_Common commonOp(shapeToTrim, trimmingTool);
            commonOp.Build();
            if (!commonOp.IsDone()) {
                MessageBoxA(NULL, "Boolean intersection failed", "Trim Error", MB_OK | MB_ICONERROR);
                return shapeToTrim;
            }
            return commonOp.Shape();
        } else {
            // Remove the intersection
            BRepAlgoAPI_Cut cutOp(shapeToTrim, trimmingTool);
            cutOp.Build();
            if (!cutOp.IsDone()) {
                MessageBoxA(NULL, "Boolean cut failed", "Trim Error", MB_OK | MB_ICONERROR);
                return shapeToTrim;
            }
            return cutOp.Shape();
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Shape with Shape Error", MB_OK | MB_ICONERROR);
        return shapeToTrim;
    }
}

TopTools_ListOfShape SplitShapeWithShape(const TopoDS_Shape& shapeToSplit, const TopoDS_Shape& splittingTool) {
    TopTools_ListOfShape resultList;

    try {
        if (shapeToSplit.IsNull() || splittingTool.IsNull()) {
            MessageBoxA(NULL, "One or both shapes are null", "Split Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Use splitter algorithm
        BRepAlgoAPI_Splitter splitter;

        // Add arguments and tools using the correct API
        TopTools_ListOfShape arguments;
        arguments.Append(shapeToSplit);
        splitter.SetArguments(arguments);

        TopTools_ListOfShape tools;
        tools.Append(splittingTool);
        splitter.SetTools(tools);

        splitter.Build();

        if (!splitter.IsDone()) {
            MessageBoxA(NULL, "Split operation failed", "Split Error", MB_OK | MB_ICONERROR);
            resultList.Append(shapeToSplit);
            return resultList;
        }

        // Extract all resulting shapes
        TopExp_Explorer exp(splitter.Shape(), TopAbs_SOLID);
        for (; exp.More(); exp.Next()) {
            resultList.Append(exp.Current());
        }

        // If no solids found, try faces
        if (resultList.IsEmpty()) {
            TopExp_Explorer faceExp(splitter.Shape(), TopAbs_FACE);
            for (; faceExp.More(); faceExp.Next()) {
                resultList.Append(faceExp.Current());
            }
        }

        // If still empty, return the whole result
        if (resultList.IsEmpty()) {
            resultList.Append(splitter.Shape());
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Split Shape Error", MB_OK | MB_ICONERROR);
        resultList.Append(shapeToSplit);
    }

    return resultList;
}

TopoDS_Shape CreateTrimmedSurface(const Handle(Geom_Surface)& surface, const TopoDS_Wire& outerBoundary, const TopTools_ListOfShape& innerBoundaries) {
    try {
        if (surface.IsNull() || outerBoundary.IsNull()) {
            MessageBoxA(NULL, "Surface or outer boundary is null", "Create Trimmed Surface Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Create face with outer boundary
        BRepBuilderAPI_MakeFace faceMaker(surface, outerBoundary, Standard_True);
        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create face with outer boundary", "Create Trimmed Surface Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Add inner boundaries (holes)
        TopTools_ListIteratorOfListOfShape it(innerBoundaries);
        for (; it.More(); it.Next()) {
            const TopoDS_Shape& boundary = it.Value();
            if (boundary.ShapeType() == TopAbs_WIRE) {
                faceMaker.Add(TopoDS::Wire(boundary));
            }
        }

        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to add inner boundaries", "Create Trimmed Surface Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        return faceMaker.Face();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Trimmed Surface Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

// ============================================================================
// UTILITY FUNCTIONS FOR TRIMMING
// ============================================================================

TopoDS_Wire ProjectWireOnSurface(const TopoDS_Wire& wire, const TopoDS_Face& surface) {
    try {
        if (wire.IsNull() || surface.IsNull()) {
            return TopoDS_Wire();
        }

        // Get surface geometry
        Handle(Geom_Surface) geomSurface = BRep_Tool::Surface(surface);
        if (geomSurface.IsNull()) {
            return TopoDS_Wire();
        }

        BRepBuilderAPI_MakeWire wireMaker;

        // Project each edge of the wire onto the surface
        BRepTools_WireExplorer wireExp(wire);
        for (; wireExp.More(); wireExp.Next()) {
            TopoDS_Edge edge = wireExp.Current();

            // Get edge curve
            Standard_Real first, last;
            Handle(Geom_Curve) curve = BRep_Tool::Curve(edge, first, last);
            if (curve.IsNull()) continue;

            // Sample points along the curve and project them
            int numSamples = 20;
            TopTools_ListOfShape projectedEdges;

            for (int i = 0; i < numSamples - 1; i++) {
                Standard_Real param1 = first + (last - first) * i / (numSamples - 1);
                Standard_Real param2 = first + (last - first) * (i + 1) / (numSamples - 1);

                gp_Pnt p1 = curve->Value(param1);
                gp_Pnt p2 = curve->Value(param2);

                // Project points onto surface
                GeomAPI_ProjectPointOnSurf proj1(p1, geomSurface);
                GeomAPI_ProjectPointOnSurf proj2(p2, geomSurface);

                if (proj1.NbPoints() > 0 && proj2.NbPoints() > 0) {
                    gp_Pnt projP1 = proj1.NearestPoint();
                    gp_Pnt projP2 = proj2.NearestPoint();

                    // Create edge between projected points
                    if (projP1.Distance(projP2) > Precision::Confusion()) {
                        BRepBuilderAPI_MakeEdge edgeMaker(projP1, projP2);
                        if (edgeMaker.IsDone()) {
                            wireMaker.Add(edgeMaker.Edge());
                        }
                    }
                }
            }
        }

        if (wireMaker.IsDone()) {
            return wireMaker.Wire();
        }

        return TopoDS_Wire(); // Return null wire if projection failed

    } catch (const Standard_Failure& e) {
        return TopoDS_Wire();
    }
}

bool IsPointInsideWire(const gp_Pnt& point, const TopoDS_Wire& wire, const TopoDS_Face& face) {
    try {
        if (wire.IsNull() || face.IsNull()) {
            return false;
        }

        // Get the underlying surface from the face
        Handle(Geom_Surface) geomSurface = BRep_Tool::Surface(face);
        if (geomSurface.IsNull()) {
            return false;
        }

        // Create a face from the wire for classification
        BRepBuilderAPI_MakeFace faceMaker(geomSurface, wire, Standard_True);
        if (!faceMaker.IsDone()) {
            return false;
        }

        TopoDS_Face testFace = faceMaker.Face();

        // Use face classifier to determine if point is inside
        BRepClass_FaceClassifier classifier(testFace, point, Precision::Confusion());

        return (classifier.State() == TopAbs_IN);

    } catch (const Standard_Failure& e) {
        return false;
    }
}

TopoDS_Wire CreateRectangularWire(const gp_Pnt& corner1, const gp_Pnt& corner2, const gp_Dir& normal) {
    try {
        // Create a coordinate system with the given normal
        gp_Ax2 coordSys(corner1, normal);

        // Project corner2 onto the plane defined by corner1 and normal
        gp_Pln plane(corner1, normal);
        GeomAPI_ProjectPointOnSurf projector(corner2, new Geom_Plane(plane));

        gp_Pnt projectedCorner2;
        if (projector.NbPoints() > 0) {
            projectedCorner2 = projector.NearestPoint();
        } else {
            projectedCorner2 = corner2;
        }

        // Calculate the other two corners to form a rectangle
        gp_Vec diagonal = gp_Vec(corner1, projectedCorner2);
        gp_Dir uDir = coordSys.XDirection();
        gp_Dir vDir = coordSys.YDirection();

        // Project diagonal onto u and v directions
        double uLength = diagonal.Dot(gp_Vec(uDir));
        double vLength = diagonal.Dot(gp_Vec(vDir));

        gp_Pnt p1 = corner1;
        gp_Pnt p2 = corner1.Translated(gp_Vec(uDir) * uLength);
        gp_Pnt p3 = corner1.Translated(gp_Vec(uDir) * uLength + gp_Vec(vDir) * vLength);
        gp_Pnt p4 = corner1.Translated(gp_Vec(vDir) * vLength);

        // Create the rectangular wire
        BRepBuilderAPI_MakePolygon polyMaker;
        polyMaker.Add(p1);
        polyMaker.Add(p2);
        polyMaker.Add(p3);
        polyMaker.Add(p4);
        polyMaker.Close();

        if (polyMaker.IsDone()) {
            return polyMaker.Wire();
        }

        return TopoDS_Wire();

    } catch (const Standard_Failure& e) {
        return TopoDS_Wire();
    }
}

// ============================================================================
// JOIN OPERATIONS
// ============================================================================

TopoDS_Shape JoinTwoShapes(const TopoDS_Shape& shape1, const TopoDS_Shape& shape2) {
    try {
        if (shape1.IsNull() || shape2.IsNull()) {
            MessageBoxA(NULL, "One or both shapes for join are null", "Join Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Use the existing FuseShapes function which performs boolean union
        return FuseShapes(shape1, shape2);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Join Two Shapes Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

TopoDS_Shape JoinShapes(const TopTools_ListOfShape& shapes) {
    try {
        if (shapes.IsEmpty()) {
            MessageBoxA(NULL, "No shapes provided for join operation", "Join Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        if (shapes.Extent() == 1) {
            return shapes.First();
        }

        // Start with the first shape
        TopoDS_Shape result = shapes.First();

        // Progressively fuse with each subsequent shape
        TopTools_ListIteratorOfListOfShape it(shapes);
        it.Next(); // Skip the first shape since we already have it as result

        for (; it.More(); it.Next()) {
            const TopoDS_Shape& currentShape = it.Value();
            if (!currentShape.IsNull()) {
                TopoDS_Shape newResult = FuseShapes(result, currentShape);
                if (!newResult.IsNull()) {
                    result = newResult;
                } else {
                    MessageBoxA(NULL, "Failed to join one of the shapes in the list", "Join Warning", MB_OK | MB_ICONWARNING);
                    // Continue with the previous result
                }
            }
        }

        return result;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Join Shapes Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

// ============================================================================
// MIRROR OPERATIONS
// ============================================================================

TopoDS_Shape MirrorShape(const TopoDS_Shape& shape, const gp_Pln& mirrorPlane) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Create transformation for mirroring across the plane
        gp_Trsf mirrorTransform;
        mirrorTransform.SetMirror(mirrorPlane.Axis());

        // Apply the transformation
        BRepBuilderAPI_Transform transformer(shape, mirrorTransform);
        if (!transformer.IsDone()) {
            MessageBoxA(NULL, "Failed to apply mirror transformation", "Mirror Error", MB_OK | MB_ICONERROR);
            return shape;
        }

        return transformer.Shape();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Mirror Shape Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

// Enhanced mirror function that creates side-by-side mirrored objects
TopoDS_Shape MirrorShapeWithSideBySidePlacement(const TopoDS_Shape& shape, const gp_Pln& mirrorPlane, Standard_Real spacing) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box of the original shape
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate shape center and dimensions
        gp_Pnt shapeCenter((xmin + xmax) / 2.0, (ymin + ymax) / 2.0, (zmin + zmax) / 2.0);
        Standard_Real xSize = xmax - xmin;
        Standard_Real ySize = ymax - ymin;
        Standard_Real zSize = zmax - zmin;

        // Get mirror plane normal and position
        gp_Dir planeNormal = mirrorPlane.Axis().Direction();
        gp_Pnt planePosition = mirrorPlane.Location();

        // Calculate the distance from shape center to mirror plane
        gp_Vec centerToPlane(shapeCenter, planePosition);
        Standard_Real distanceToPlane = centerToPlane.Dot(planeNormal);

        // Create the basic mirror transformation
        gp_Trsf mirrorTransform;
        mirrorTransform.SetMirror(mirrorPlane.Axis());

        // Apply mirror transformation
        BRepBuilderAPI_Transform transformer(shape, mirrorTransform);
        if (!transformer.IsDone()) {
            MessageBoxA(NULL, "Failed to apply mirror transformation", "Mirror Error", MB_OK | MB_ICONERROR);
            return shape;
        }

        TopoDS_Shape mirroredShape = transformer.Shape();

        // Calculate proper separation for side-by-side placement
        Standard_Real separationDistance = 0.0;

        // Determine separation distance based on plane normal direction
        if (abs(planeNormal.X()) > 0.9) { // YZ plane (X normal) - separate along X axis
            separationDistance = xSize + spacing;
        } else if (abs(planeNormal.Y()) > 0.9) { // XZ plane (Y normal) - separate along Y axis
            separationDistance = ySize + spacing;
        } else if (abs(planeNormal.Z()) > 0.9) { // XY plane (Z normal) - separate along Z axis
            separationDistance = zSize + spacing;
        } else {
            // For custom planes, use the largest dimension plus spacing
            separationDistance = (std::max)((std::max)(xSize, ySize), zSize) + spacing;
        }

        // Apply translation to move the mirrored object away from the original
        if (separationDistance > 0.0) {
            // Create separation vector in the direction of the plane normal
            gp_Vec separationVector = gp_Vec(planeNormal) * separationDistance;

            // If the original object is on the negative side of the plane,
            // move the mirrored object in the positive direction, and vice versa
            if (distanceToPlane > 0) {
                separationVector.Reverse(); // Move in negative direction
            }

            gp_Trsf translationTransform;
            translationTransform.SetTranslation(separationVector);

            BRepBuilderAPI_Transform translator(mirroredShape, translationTransform);
            if (translator.IsDone()) {
                mirroredShape = translator.Shape();
            }
        }

        return mirroredShape;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Enhanced Mirror Shape Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape MirrorShapeAcrossXYPlane(const TopoDS_Shape& shape) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for XY mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box to determine offset
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate the center Z and offset for proper positioning
        Standard_Real centerZ = (zmin + zmax) / 2.0;
        Standard_Real offsetZ = centerZ > 0 ? -2.0 * centerZ : -2.0 * centerZ;

        // Create XY plane at the center Z position for mirroring
        gp_Pln xyPlane(gp_Pnt(0, 0, centerZ), gp_Dir(0, 0, 1));

        // Mirror the shape
        TopoDS_Shape mirroredShape = MirrorShape(shape, xyPlane);

        // If the shape is centered at origin, add additional offset to separate them
        if (abs(centerZ) < 1.0) {
            gp_Trsf translateTransform;
            translateTransform.SetTranslation(gp_Vec(0, 0, -(zmax - zmin + 2.0)));
            BRepBuilderAPI_Transform transformer(mirroredShape, translateTransform);
            if (transformer.IsDone()) {
                mirroredShape = transformer.Shape();
            }
        }

        return mirroredShape;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Mirror XY Plane Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

// Enhanced XY plane mirror with proper side-by-side spacing
TopoDS_Shape MirrorShapeAcrossXYPlaneWithSpacing(const TopoDS_Shape& shape, Standard_Real spacing) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for XY mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box of the original shape
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate shape center
        gp_Pnt shapeCenter((xmin + xmax) / 2.0, (ymin + ymax) / 2.0, (zmin + zmax) / 2.0);
        Standard_Real zSize = zmax - zmin;

        // Create XY plane positioned to create proper side-by-side separation
        // Place the mirror plane at the edge of the object plus half the desired spacing
        Standard_Real mirrorPlaneZ = shapeCenter.Z() - (zSize / 2.0) - (spacing / 2.0);
        gp_Pln xyPlane(gp_Pnt(shapeCenter.X(), shapeCenter.Y(), mirrorPlaneZ), gp_Dir(0, 0, 1));

        // Use the enhanced mirror function
        return MirrorShapeWithSideBySidePlacement(shape, xyPlane, spacing);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Enhanced XY Mirror Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape MirrorShapeAcrossXZPlane(const TopoDS_Shape& shape) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for XZ mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box to determine offset
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate the center Y and offset for proper positioning
        Standard_Real centerY = (ymin + ymax) / 2.0;

        // Create XZ plane at the center Y position for mirroring
        gp_Pln xzPlane(gp_Pnt(0, centerY, 0), gp_Dir(0, 1, 0));

        // Mirror the shape
        TopoDS_Shape mirroredShape = MirrorShape(shape, xzPlane);

        // If the shape is centered at origin, add additional offset to separate them
        if (abs(centerY) < 1.0) {
            gp_Trsf translateTransform;
            translateTransform.SetTranslation(gp_Vec(0, -(ymax - ymin + 2.0), 0));
            BRepBuilderAPI_Transform transformer(mirroredShape, translateTransform);
            if (transformer.IsDone()) {
                mirroredShape = transformer.Shape();
            }
        }

        return mirroredShape;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Mirror XZ Plane Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

// Enhanced XZ plane mirror with proper side-by-side spacing
TopoDS_Shape MirrorShapeAcrossXZPlaneWithSpacing(const TopoDS_Shape& shape, Standard_Real spacing) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for XZ mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box of the original shape
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate shape center
        gp_Pnt shapeCenter((xmin + xmax) / 2.0, (ymin + ymax) / 2.0, (zmin + zmax) / 2.0);
        Standard_Real ySize = ymax - ymin;

        // Create XZ plane positioned to create proper side-by-side separation
        // Place the mirror plane at the edge of the object plus half the desired spacing
        Standard_Real mirrorPlaneY = shapeCenter.Y() - (ySize / 2.0) - (spacing / 2.0);
        gp_Pln xzPlane(gp_Pnt(shapeCenter.X(), mirrorPlaneY, shapeCenter.Z()), gp_Dir(0, 1, 0));

        // Use the enhanced mirror function
        return MirrorShapeWithSideBySidePlacement(shape, xzPlane, spacing);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Enhanced XZ Mirror Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape MirrorShapeAcrossYZPlane(const TopoDS_Shape& shape) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for YZ mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box to determine offset
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate the center X and offset for proper positioning
        Standard_Real centerX = (xmin + xmax) / 2.0;

        // Create YZ plane at the center X position for mirroring
        gp_Pln yzPlane(gp_Pnt(centerX, 0, 0), gp_Dir(1, 0, 0));

        // Mirror the shape
        TopoDS_Shape mirroredShape = MirrorShape(shape, yzPlane);

        // If the shape is centered at origin, add additional offset to separate them
        if (abs(centerX) < 1.0) {
            gp_Trsf translateTransform;
            translateTransform.SetTranslation(gp_Vec(-(xmax - xmin + 2.0), 0, 0));
            BRepBuilderAPI_Transform transformer(mirroredShape, translateTransform);
            if (transformer.IsDone()) {
                mirroredShape = transformer.Shape();
            }
        }

        return mirroredShape;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Mirror YZ Plane Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

// Enhanced YZ plane mirror with proper side-by-side spacing
TopoDS_Shape MirrorShapeAcrossYZPlaneWithSpacing(const TopoDS_Shape& shape, Standard_Real spacing) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for YZ mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the bounding box of the original shape
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate shape center
        gp_Pnt shapeCenter((xmin + xmax) / 2.0, (ymin + ymax) / 2.0, (zmin + zmax) / 2.0);
        Standard_Real xSize = xmax - xmin;

        // Create YZ plane positioned to create proper side-by-side separation
        // Place the mirror plane at the edge of the object plus half the desired spacing
        Standard_Real mirrorPlaneX = shapeCenter.X() - (xSize / 2.0) - (spacing / 2.0);
        gp_Pln yzPlane(gp_Pnt(mirrorPlaneX, shapeCenter.Y(), shapeCenter.Z()), gp_Dir(1, 0, 0));

        // Use the enhanced mirror function
        return MirrorShapeWithSideBySidePlacement(shape, yzPlane, spacing);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Enhanced YZ Mirror Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

// Enhanced custom face mirror with proper side-by-side spacing
TopoDS_Shape MirrorShapeAcrossFaceWithSpacing(const TopoDS_Shape& shape, const TopoDS_Face& mirrorFace, Standard_Real spacing) {
    try {
        if (shape.IsNull() || mirrorFace.IsNull()) {
            MessageBoxA(NULL, "Shape or mirror face is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Extract plane from the face
        gp_Pln mirrorPlane = ExtractPlaneFromFace(mirrorFace);

        // Use the enhanced mirror function
        return MirrorShapeWithSideBySidePlacement(shape, mirrorPlane, spacing);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Enhanced Face Mirror Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape MirrorShapeAcrossAxis(const TopoDS_Shape& shape, const gp_Ax1& axis) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape for axis mirror is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Create transformation for mirroring across the axis
        gp_Trsf mirrorTransform;
        mirrorTransform.SetMirror(axis);

        // Apply the transformation
        BRepBuilderAPI_Transform transformer(shape, mirrorTransform);
        if (!transformer.IsDone()) {
            MessageBoxA(NULL, "Failed to apply axis mirror transformation", "Mirror Error", MB_OK | MB_ICONERROR);
            return shape;
        }

        return transformer.Shape();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Mirror Axis Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape MirrorShapeAcrossFace(const TopoDS_Shape& shape, const TopoDS_Face& mirrorFace) {
    try {
        if (shape.IsNull() || mirrorFace.IsNull()) {
            MessageBoxA(NULL, "Shape or mirror face is null", "Mirror Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Extract plane from the face
        gp_Pln mirrorPlane = ExtractPlaneFromFace(mirrorFace);
        return MirrorShape(shape, mirrorPlane);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Mirror Across Face Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

gp_Pln ExtractPlaneFromFace(const TopoDS_Face& face) {
    try {
        if (face.IsNull()) {
            throw Standard_Failure("Face is null");
        }

        // Get the surface from the face
        Handle(Geom_Surface) surface = BRep_Tool::Surface(face);
        if (surface.IsNull()) {
            throw Standard_Failure("Cannot get surface from face");
        }

        // Try to convert to a plane
        Handle(Geom_Plane) plane = Handle(Geom_Plane)::DownCast(surface);
        if (!plane.IsNull()) {
            return plane->Pln();
        }

        // If not a plane, try to fit a plane to the face
        // Get some points from the face and fit a plane
        TopExp_Explorer vertexExp(face, TopAbs_VERTEX);
        if (!vertexExp.More()) {
            throw Standard_Failure("No vertices found on face");
        }

        // Get first three vertices to define a plane
        gp_Pnt p1, p2, p3;
        bool hasP1 = false, hasP2 = false, hasP3 = false;

        for (int i = 0; vertexExp.More() && i < 3; vertexExp.Next(), i++) {
            TopoDS_Vertex vertex = TopoDS::Vertex(vertexExp.Current());
            gp_Pnt point = BRep_Tool::Pnt(vertex);

            if (i == 0) { p1 = point; hasP1 = true; }
            else if (i == 1) { p2 = point; hasP2 = true; }
            else if (i == 2) { p3 = point; hasP3 = true; }
        }

        if (!hasP1 || !hasP2 || !hasP3) {
            throw Standard_Failure("Not enough points to define a plane");
        }

        // Create plane from three points
        gp_Vec v1(p1, p2);
        gp_Vec v2(p1, p3);
        gp_Vec normal = v1.Crossed(v2);

        if (normal.Magnitude() < Precision::Confusion()) {
            throw Standard_Failure("Points are collinear, cannot define plane");
        }

        normal.Normalize();
        return gp_Pln(p1, gp_Dir(normal));

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Extract Plane Error", MB_OK | MB_ICONERROR);
        // Return XY plane as fallback
        return gp_Pln(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
    }
}

// ============================================================================
// ENHANCED 2D CURVE TRIMMING OPERATIONS
// ============================================================================

std::vector<gp_Pnt> FindCurveCurveIntersections(const TopoDS_Edge& curve1, const TopoDS_Edge& curve2) {
    std::vector<gp_Pnt> intersectionPoints;

    try {
        if (curve1.IsNull() || curve2.IsNull()) {
            return intersectionPoints;
        }

        // Get curve geometries
        Standard_Real first1, last1, first2, last2;
        Handle(Geom_Curve) geomCurve1 = BRep_Tool::Curve(curve1, first1, last1);
        Handle(Geom_Curve) geomCurve2 = BRep_Tool::Curve(curve2, first2, last2);

        if (geomCurve1.IsNull() || geomCurve2.IsNull()) {
            return intersectionPoints;
        }

        // Create curve adaptors for intersection
        GeomAdaptor_Curve adaptor1(geomCurve1, first1, last1);
        GeomAdaptor_Curve adaptor2(geomCurve2, first2, last2);

        // Use 3D curve intersection
        // For more robust intersection, we can sample points and find closest approaches
        const int numSamples = 100;
        const Standard_Real tolerance = Precision::Confusion() * 10;

        for (int i = 0; i <= numSamples; i++) {
            Standard_Real param1 = first1 + (last1 - first1) * i / numSamples;
            gp_Pnt point1 = geomCurve1->Value(param1);

            // Project point1 onto curve2 to find closest point
            GeomAPI_ProjectPointOnCurve projector(point1, geomCurve2, first2, last2);

            if (projector.NbPoints() > 0) {
                gp_Pnt closestPoint = projector.NearestPoint();
                Standard_Real distance = point1.Distance(closestPoint);

                if (distance < tolerance) {
                    // Check if this intersection point is already found
                    bool alreadyFound = false;
                    for (const auto& existingPoint : intersectionPoints) {
                        if (existingPoint.Distance(point1) < tolerance) {
                            alreadyFound = true;
                            break;
                        }
                    }

                    if (!alreadyFound) {
                        intersectionPoints.push_back(point1);
                    }
                }
            }
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Find Curve Intersections Error", MB_OK | MB_ICONERROR);
    }

    return intersectionPoints;
}

std::vector<Standard_Real> FindCurveParametersAtPoints(const TopoDS_Edge& curve, const std::vector<gp_Pnt>& points) {
    std::vector<Standard_Real> parameters;

    try {
        if (curve.IsNull() || points.empty()) {
            return parameters;
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            return parameters;
        }

        for (const auto& point : points) {
            GeomAPI_ProjectPointOnCurve projector(point, geomCurve, first, last);
            if (projector.NbPoints() > 0) {
                Standard_Real param = projector.LowerDistanceParameter();
                parameters.push_back(param);
            }
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Find Curve Parameters Error", MB_OK | MB_ICONERROR);
    }

    return parameters;
}

bool IsPointOnCurve(const TopoDS_Edge& curve, const gp_Pnt& point, Standard_Real tolerance) {
    try {
        if (curve.IsNull()) {
            return false;
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            return false;
        }

        GeomAPI_ProjectPointOnCurve projector(point, geomCurve, first, last);
        if (projector.NbPoints() > 0) {
            return projector.LowerDistance() <= tolerance;
        }

    } catch (const Standard_Failure& e) {
        // Silently fail for utility function
    }

    return false;
}

Standard_Real GetCurveParameterAtPoint(const TopoDS_Edge& curve, const gp_Pnt& point) {
    try {
        if (curve.IsNull()) {
            return 0.0;
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            return 0.0;
        }

        GeomAPI_ProjectPointOnCurve projector(point, geomCurve, first, last);
        if (projector.NbPoints() > 0) {
            return projector.LowerDistanceParameter();
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Get Curve Parameter Error", MB_OK | MB_ICONERROR);
    }

    return 0.0;
}

gp_Pnt GetCurvePointAtParameter(const TopoDS_Edge& curve, Standard_Real parameter) {
    try {
        if (curve.IsNull()) {
            return gp_Pnt();
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            return gp_Pnt();
        }

        // Clamp parameter to curve bounds
        if (parameter < first) parameter = first;
        if (parameter > last) parameter = last;

        return geomCurve->Value(parameter);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Get Curve Point Error", MB_OK | MB_ICONERROR);
    }

    return gp_Pnt();
}

TopTools_ListOfShape TrimCurvesAtIntersection(const TopoDS_Edge& curve1, const TopoDS_Edge& curve2, const gp_Pnt& intersectionPoint, bool keepBothSegments) {
    TopTools_ListOfShape resultList;

    try {
        if (curve1.IsNull() || curve2.IsNull()) {
            MessageBoxA(NULL, "One or both curves are null", "Trim at Intersection Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Find parameters at intersection point for both curves
        Standard_Real param1 = GetCurveParameterAtPoint(curve1, intersectionPoint);
        Standard_Real param2 = GetCurveParameterAtPoint(curve2, intersectionPoint);

        // Get curve bounds
        Standard_Real first1, last1, first2, last2;
        Handle(Geom_Curve) geomCurve1 = BRep_Tool::Curve(curve1, first1, last1);
        Handle(Geom_Curve) geomCurve2 = BRep_Tool::Curve(curve2, first2, last2);

        if (geomCurve1.IsNull() || geomCurve2.IsNull()) {
            MessageBoxA(NULL, "Failed to get curve geometries", "Trim at Intersection Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Create trimmed segments for curve1
        if (param1 > first1 + Precision::Confusion()) {
            // Segment before intersection
            BRepBuilderAPI_MakeEdge edgeMaker1(geomCurve1, first1, param1);
            if (edgeMaker1.IsDone()) {
                resultList.Append(edgeMaker1.Edge());
            }
        }

        if (param1 < last1 - Precision::Confusion()) {
            // Segment after intersection
            BRepBuilderAPI_MakeEdge edgeMaker2(geomCurve1, param1, last1);
            if (edgeMaker2.IsDone()) {
                resultList.Append(edgeMaker2.Edge());
            }
        }

        if (keepBothSegments) {
            // Create trimmed segments for curve2
            if (param2 > first2 + Precision::Confusion()) {
                // Segment before intersection
                BRepBuilderAPI_MakeEdge edgeMaker3(geomCurve2, first2, param2);
                if (edgeMaker3.IsDone()) {
                    resultList.Append(edgeMaker3.Edge());
                }
            }

            if (param2 < last2 - Precision::Confusion()) {
                // Segment after intersection
                BRepBuilderAPI_MakeEdge edgeMaker4(geomCurve2, param2, last2);
                if (edgeMaker4.IsDone()) {
                    resultList.Append(edgeMaker4.Edge());
                }
            }
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Curves at Intersection Error", MB_OK | MB_ICONERROR);
    }

    return resultList;
}

TopTools_ListOfShape FindCurveIntersections(const TopoDS_Edge& curve1, const TopoDS_Edge& curve2) {
    TopTools_ListOfShape intersectionPoints;

    try {
        std::vector<gp_Pnt> points = FindCurveCurveIntersections(curve1, curve2);

        for (const auto& point : points) {
            // Create a vertex for each intersection point
            BRepBuilderAPI_MakeVertex vertexMaker(point);
            if (vertexMaker.IsDone()) {
                intersectionPoints.Append(vertexMaker.Vertex());
            }
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Find Curve Intersections Error", MB_OK | MB_ICONERROR);
    }

    return intersectionPoints;
}

TopoDS_Shape TrimCurveAtParameter(const TopoDS_Edge& curve, Standard_Real parameter, bool keepStartSegment) {
    try {
        if (curve.IsNull()) {
            MessageBoxA(NULL, "Curve is null", "Trim at Parameter Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            MessageBoxA(NULL, "Failed to get curve geometry", "Trim at Parameter Error", MB_OK | MB_ICONERROR);
            return curve;
        }

        // Clamp parameter to curve bounds
        if (parameter < first) parameter = first;
        if (parameter > last) parameter = last;

        if (keepStartSegment) {
            // Keep segment from start to parameter
            if (parameter > first + Precision::Confusion()) {
                BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, first, parameter);
                if (edgeMaker.IsDone()) {
                    return edgeMaker.Edge();
                }
            }
        } else {
            // Keep segment from parameter to end
            if (parameter < last - Precision::Confusion()) {
                BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, parameter, last);
                if (edgeMaker.IsDone()) {
                    return edgeMaker.Edge();
                }
            }
        }

        return curve; // Return original if trimming failed

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Curve at Parameter Error", MB_OK | MB_ICONERROR);
        return curve;
    }
}

TopTools_ListOfShape TrimCurveMultipleSegments(const TopoDS_Edge& curve, const std::vector<Standard_Real>& parameters) {
    TopTools_ListOfShape resultList;

    try {
        if (curve.IsNull() || parameters.empty()) {
            MessageBoxA(NULL, "Curve is null or no parameters provided", "Multi-Segment Trim Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            MessageBoxA(NULL, "Failed to get curve geometry", "Multi-Segment Trim Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Sort parameters and add curve bounds
        std::vector<Standard_Real> sortedParams = parameters;
        sortedParams.push_back(first);
        sortedParams.push_back(last);
        std::sort(sortedParams.begin(), sortedParams.end());

        // Remove duplicates
        sortedParams.erase(std::unique(sortedParams.begin(), sortedParams.end(),
            [](Standard_Real a, Standard_Real b) {
                return std::abs(a - b) < Precision::Confusion();
            }), sortedParams.end());

        // Create segments between consecutive parameters
        for (size_t i = 0; i < sortedParams.size() - 1; i++) {
            Standard_Real param1 = sortedParams[i];
            Standard_Real param2 = sortedParams[i + 1];

            if (param2 - param1 > Precision::Confusion()) {
                BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, param1, param2);
                if (edgeMaker.IsDone()) {
                    resultList.Append(edgeMaker.Edge());
                }
            }
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Multi-Segment Trim Error", MB_OK | MB_ICONERROR);
    }

    return resultList;
}

TopoDS_Shape TrimCurveWithCurve(const TopoDS_Edge& curveToTrim, const TopoDS_Edge& trimmingCurve, bool keepInside) {
    try {
        if (curveToTrim.IsNull() || trimmingCurve.IsNull()) {
            MessageBoxA(NULL, "One or both curves are null", "Trim Curve with Curve Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Find intersection points
        std::vector<gp_Pnt> intersections = FindCurveCurveIntersections(curveToTrim, trimmingCurve);

        if (intersections.empty()) {
            MessageBoxA(NULL, "No intersections found between curves", "Trim Curve with Curve Info", MB_OK | MB_ICONINFORMATION);
            return curveToTrim;
        }

        // Convert intersection points to parameters on the curve to trim
        std::vector<Standard_Real> parameters = FindCurveParametersAtPoints(curveToTrim, intersections);

        if (parameters.empty()) {
            return curveToTrim;
        }

        // For simplicity, use the first two intersection points if available
        if (parameters.size() >= 2) {
            std::sort(parameters.begin(), parameters.end());
            Standard_Real param1 = parameters[0];
            Standard_Real param2 = parameters[1];

            Standard_Real first, last;
            Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curveToTrim, first, last);
            if (geomCurve.IsNull()) {
                return curveToTrim;
            }

            if (keepInside) {
                // Keep segment between intersections
                BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, param1, param2);
                if (edgeMaker.IsDone()) {
                    return edgeMaker.Edge();
                }
            } else {
                // This is more complex - would need to return multiple segments
                // For now, return the segment before first intersection
                if (param1 > first + Precision::Confusion()) {
                    BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, first, param1);
                    if (edgeMaker.IsDone()) {
                        return edgeMaker.Edge();
                    }
                }
            }
        }

        return curveToTrim;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Curve with Curve Error", MB_OK | MB_ICONERROR);
        return curveToTrim;
    }
}

// ============================================================================
// ENHANCED 3D SURFACE/SOLID TRIMMING OPERATIONS
// ============================================================================

TopoDS_Shape TrimSolidWithWire(const TopoDS_Solid& solid, const TopoDS_Wire& cuttingWire, const gp_Dir& direction) {
    try {
        if (solid.IsNull() || cuttingWire.IsNull()) {
            MessageBoxA(NULL, "Solid or cutting wire is null", "Trim Solid Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Create a face from the wire for cutting
        BRepBuilderAPI_MakeFace faceMaker(cuttingWire);
        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create face from wire", "Trim Solid Error", MB_OK | MB_ICONERROR);
            return solid;
        }

        TopoDS_Face cuttingFace = faceMaker.Face();

        // Extrude the face in the given direction to create a cutting solid
        gp_Vec extrusionVector(direction.XYZ() * 1000.0); // Large extrusion
        BRepPrimAPI_MakePrism prismMaker(cuttingFace, extrusionVector);
        if (!prismMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create cutting solid", "Trim Solid Error", MB_OK | MB_ICONERROR);
            return solid;
        }

        TopoDS_Shape cuttingSolid = prismMaker.Shape();

        // Perform boolean cut operation
        BRepAlgoAPI_Cut cutOp(solid, cuttingSolid);
        cutOp.Build();
        if (!cutOp.IsDone()) {
            MessageBoxA(NULL, "Boolean cut operation failed", "Trim Solid Error", MB_OK | MB_ICONERROR);
            return solid;
        }

        return cutOp.Shape();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Solid with Wire Error", MB_OK | MB_ICONERROR);
        return solid;
    }
}

TopoDS_Shape TrimFaceWithEdge(const TopoDS_Face& face, const TopoDS_Edge& cuttingEdge) {
    try {
        if (face.IsNull() || cuttingEdge.IsNull()) {
            MessageBoxA(NULL, "Face or cutting edge is null", "Trim Face Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Project the edge onto the face surface
        Handle(Geom_Surface) surface = BRep_Tool::Surface(face);
        if (surface.IsNull()) {
            MessageBoxA(NULL, "Failed to get face surface", "Trim Face Error", MB_OK | MB_ICONERROR);
            return face;
        }

        // For simplicity, create a wire from the edge and use existing trim function
        BRepBuilderAPI_MakeWire wireMaker(cuttingEdge);
        if (!wireMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create wire from edge", "Trim Face Error", MB_OK | MB_ICONERROR);
            return face;
        }

        return TrimFaceWithWire(face, wireMaker.Wire(), true);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Face with Edge Error", MB_OK | MB_ICONERROR);
        return face;
    }
}

TopTools_ListOfShape SplitFaceWithWire(const TopoDS_Face& face, const TopoDS_Wire& splittingWire) {
    TopTools_ListOfShape resultList;

    try {
        if (face.IsNull() || splittingWire.IsNull()) {
            MessageBoxA(NULL, "Face or splitting wire is null", "Split Face Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Use BRepFeat_SplitShape for splitting
        BRepFeat_SplitShape splitter(face);
        splitter.Add(splittingWire, face);
        splitter.Build();

        if (!splitter.IsDone()) {
            MessageBoxA(NULL, "Face splitting operation failed", "Split Face Error", MB_OK | MB_ICONERROR);
            resultList.Append(face);
            return resultList;
        }

        // Extract resulting faces
        TopExp_Explorer faceExp;
        faceExp.Init(splitter.Shape(), TopAbs_FACE);
        for (; faceExp.More(); faceExp.Next()) {
            resultList.Append(faceExp.Current());
        }

        if (resultList.IsEmpty()) {
            resultList.Append(face);
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Split Face with Wire Error", MB_OK | MB_ICONERROR);
        resultList.Append(face);
    }

    return resultList;
}

// ============================================================================
// ENHANCED UNTRIM OPERATIONS
// ============================================================================

TopoDS_Shape UntrimCurveToFullExtent(const TopoDS_Edge& trimmedCurve) {
    try {
        if (trimmedCurve.IsNull()) {
            MessageBoxA(NULL, "Trimmed curve is null", "Untrim to Full Extent Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Get the underlying curve
        Standard_Real first, last;
        Handle(Geom_Curve) curve = BRep_Tool::Curve(trimmedCurve, first, last);
        if (curve.IsNull()) {
            MessageBoxA(NULL, "Failed to get underlying curve", "Untrim to Full Extent Error", MB_OK | MB_ICONERROR);
            return trimmedCurve;
        }

        // Check if this is a trimmed curve
        Handle(Geom_TrimmedCurve) trimmedGeomCurve = Handle(Geom_TrimmedCurve)::DownCast(curve);
        if (!trimmedGeomCurve.IsNull()) {
            // Get the basis curve (full extent)
            Handle(Geom_Curve) basisCurve = trimmedGeomCurve->BasisCurve();
            if (!basisCurve.IsNull()) {
                BRepBuilderAPI_MakeEdge edgeMaker(basisCurve);
                if (edgeMaker.IsDone()) {
                    return edgeMaker.Edge();
                }
            }
        }

        // If not a trimmed curve, try to extend the curve to its natural bounds
        // This is curve-type dependent and complex, so for now return the original
        return trimmedCurve;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Untrim Curve to Full Extent Error", MB_OK | MB_ICONERROR);
        return trimmedCurve;
    }
}

TopTools_ListOfShape UntrimMultipleCurves(const TopTools_ListOfShape& trimmedCurves) {
    TopTools_ListOfShape resultList;

    try {
        TopTools_ListIteratorOfListOfShape it(trimmedCurves);
        for (; it.More(); it.Next()) {
            const TopoDS_Shape& shape = it.Value();
            if (shape.ShapeType() == TopAbs_EDGE) {
                TopoDS_Shape untrimmedShape = UntrimCurveToFullExtent(TopoDS::Edge(shape));
                if (!untrimmedShape.IsNull()) {
                    resultList.Append(untrimmedShape);
                }
            } else if (shape.ShapeType() == TopAbs_FACE) {
                TopoDS_Shape untrimmedShape = UntrimSurface(TopoDS::Face(shape));
                if (!untrimmedShape.IsNull()) {
                    resultList.Append(untrimmedShape);
                }
            } else {
                resultList.Append(shape); // Keep as-is if not edge or face
            }
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Untrim Multiple Curves Error", MB_OK | MB_ICONERROR);
    }

    return resultList;
}

// ============================================================================
// VISUAL FEEDBACK AND PREVIEW UTILITIES
// ============================================================================

TopoDS_Shape CreatePreviewTrimResult(const TopoDS_Edge& curve, const gp_Pnt& startPoint, const gp_Pnt& endPoint) {
    try {
        if (curve.IsNull()) {
            return TopoDS_Shape();
        }

        // Find parameters for start and end points
        Standard_Real startParam = GetCurveParameterAtPoint(curve, startPoint);
        Standard_Real endParam = GetCurveParameterAtPoint(curve, endPoint);

        // Ensure correct parameter order
        if (startParam > endParam) {
            Standard_Real temp = startParam;
            startParam = endParam;
            endParam = temp;
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            return curve;
        }

        // Create preview trimmed curve
        BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, startParam, endParam);
        if (edgeMaker.IsDone()) {
            return edgeMaker.Edge();
        }

    } catch (const Standard_Failure& e) {
        // Silently fail for preview function
    }

    return curve;
}

TopTools_ListOfShape CreatePreviewMultiTrimResult(const TopoDS_Edge& curve, const std::vector<gp_Pnt>& trimPoints) {
    TopTools_ListOfShape resultList;

    try {
        if (curve.IsNull() || trimPoints.empty()) {
            return resultList;
        }

        // Convert points to parameters
        std::vector<Standard_Real> parameters;
        for (const auto& point : trimPoints) {
            Standard_Real param = GetCurveParameterAtPoint(curve, point);
            parameters.push_back(param);
        }

        // Use existing multi-segment trim function
        resultList = TrimCurveMultipleSegments(curve, parameters);

    } catch (const Standard_Failure& e) {
        // Silently fail for preview function
    }

    return resultList;
}

TopoDS_Shape HighlightCurveSegment(const TopoDS_Edge& curve, Standard_Real startParam, Standard_Real endParam) {
    try {
        if (curve.IsNull()) {
            return TopoDS_Shape();
        }

        Standard_Real first, last;
        Handle(Geom_Curve) geomCurve = BRep_Tool::Curve(curve, first, last);
        if (geomCurve.IsNull()) {
            return curve;
        }

        // Clamp parameters to curve bounds
        if (startParam < first) startParam = first;
        if (endParam > last) endParam = last;
        if (startParam > endParam) {
            Standard_Real temp = startParam;
            startParam = endParam;
            endParam = temp;
        }

        // Create highlighted segment
        BRepBuilderAPI_MakeEdge edgeMaker(geomCurve, startParam, endParam);
        if (edgeMaker.IsDone()) {
            return edgeMaker.Edge();
        }

    } catch (const Standard_Failure& e) {
        // Silently fail for highlight function
    }

    return curve;
}

// ============================================================================
// SIMPLE TRIM UTILITY FUNCTIONS
// ============================================================================

gp_Pnt GetShapeCenter(const TopoDS_Shape& shape) {
    try {
        if (shape.IsNull()) {
            return gp_Pnt(0, 0, 0);
        }

        // Calculate bounding box center
        Bnd_Box boundingBox;
        BRepBndLib::Add(shape, boundingBox);

        if (boundingBox.IsVoid()) {
            return gp_Pnt(0, 0, 0);
        }

        Standard_Real xMin, yMin, zMin, xMax, yMax, zMax;
        boundingBox.Get(xMin, yMin, zMin, xMax, yMax, zMax);

        return gp_Pnt((xMin + xMax) / 2.0, (yMin + yMax) / 2.0, (zMin + zMax) / 2.0);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Get Shape Center Error", MB_OK | MB_ICONERROR);
        return gp_Pnt(0, 0, 0);
    }
}

TopoDS_Shape TrimFaceToHalf(const TopoDS_Face& face) {
    try {
        if (face.IsNull()) {
            return TopoDS_Shape();
        }

        // For face trimming, we'll use a simpler approach - trim with a plane through the center
        gp_Pnt center = GetShapeCenter(face);

        // Create a cutting plane through the center
        gp_Pln cuttingPlane(center, gp_Dir(1, 0, 0)); // Cut along X direction

        // Use the existing plane trimming function
        return TrimShapeWithPlane(face, cuttingPlane, true);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Face to Half Error", MB_OK | MB_ICONERROR);
        return face;
    }
}

TopoDS_Shape TrimCurveToHalf(const TopoDS_Edge& edge) {
    try {
        if (edge.IsNull()) {
            return TopoDS_Shape();
        }

        // Get curve geometry
        Standard_Real first, last;
        Handle(Geom_Curve) curve = BRep_Tool::Curve(edge, first, last);
        if (curve.IsNull()) {
            return edge;
        }

        // Calculate midpoint parameter
        Standard_Real midParam = first + (last - first) / 2.0;

        // Create trimmed curve from start to midpoint
        BRepBuilderAPI_MakeEdge edgeMaker(curve, first, midParam);
        if (edgeMaker.IsDone()) {
            return edgeMaker.Edge();
        }

        return edge;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Trim Curve to Half Error", MB_OK | MB_ICONERROR);
        return edge;
    }
}

// ============================================================================
// INTERACTIVE SPLIT OPERATIONS
// ============================================================================

TopTools_ListOfShape SplitSolidWithLine(const TopoDS_Shape& solid, const gp_Pnt& startPoint, const gp_Pnt& endPoint) {
    TopTools_ListOfShape resultList;

    try {
        if (solid.IsNull()) {
            MessageBoxA(NULL, "Solid is null", "Split Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Create a directional plane based on the line you drew
        gp_Pln splittingPlane = CreateDirectionalPlaneFromLine(solid, startPoint, endPoint);

        // Use plane splitting
        return SplitSolidWithPlane(solid, splittingPlane);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Split Solid with Line Error", MB_OK | MB_ICONERROR);
    }

    return resultList;
}

TopTools_ListOfShape SplitSolidWithPlane(const TopoDS_Shape& solid, const gp_Pln& splittingPlane) {
    TopTools_ListOfShape resultList;

    try {
        if (solid.IsNull()) {
            MessageBoxA(NULL, "Solid is null", "Split Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Create a large face from the plane to use as cutting tool
        BRepBuilderAPI_MakeFace faceMaker(splittingPlane, -1000, 1000, -1000, 1000);
        if (!faceMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create cutting face from plane", "Split Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        TopoDS_Face cuttingFace = faceMaker.Face();

        // Use BRepAlgoAPI_Splitter for splitting (updated API for OCCT 7.9.0)
        BRepAlgoAPI_Splitter splitter;

        // Add arguments (shapes to be split)
        TopTools_ListOfShape arguments;
        arguments.Append(solid);
        splitter.SetArguments(arguments);

        // Add tools (cutting shapes)
        TopTools_ListOfShape tools;
        tools.Append(cuttingFace);
        splitter.SetTools(tools);

        splitter.Build();

        if (!splitter.IsDone()) {
            MessageBoxA(NULL, "Split operation failed", "Split Error", MB_OK | MB_ICONERROR);
            return resultList;
        }

        // Extract resulting solids
        TopExp_Explorer solidExp;
        solidExp.Init(splitter.Shape(), TopAbs_SOLID);
        for (; solidExp.More(); solidExp.Next()) {
            resultList.Append(solidExp.Current());
        }

        // If no solids found, try to get faces or other shapes
        if (resultList.IsEmpty()) {
            TopExp_Explorer shapeExp;
            shapeExp.Init(splitter.Shape(), TopAbs_FACE);
            for (; shapeExp.More(); shapeExp.Next()) {
                resultList.Append(shapeExp.Current());
            }
        }

        // If still empty, add the original shape
        if (resultList.IsEmpty()) {
            resultList.Append(solid);
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Split Solid with Plane Error", MB_OK | MB_ICONERROR);
        resultList.Append(solid);
    }

    return resultList;
}

TopoDS_Wire CreateSplitLineWire(const gp_Pnt& startPoint, const gp_Pnt& endPoint) {
    try {
        // Create a line between the two points
        BRepBuilderAPI_MakeEdge edgeMaker(startPoint, endPoint);
        if (!edgeMaker.IsDone()) {
            return TopoDS_Wire();
        }

        // Create wire from the edge
        BRepBuilderAPI_MakeWire wireMaker(edgeMaker.Edge());
        if (wireMaker.IsDone()) {
            return wireMaker.Wire();
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Split Line Error", MB_OK | MB_ICONERROR);
    }

    return TopoDS_Wire();
}

gp_Pln CreatePlaneFromLine(const gp_Pnt& startPoint, const gp_Pnt& endPoint, const gp_Dir& normal) {
    try {
        // Calculate the midpoint of the line
        gp_Pnt midPoint((startPoint.X() + endPoint.X()) / 2.0,
                        (startPoint.Y() + endPoint.Y()) / 2.0,
                        (startPoint.Z() + endPoint.Z()) / 2.0);

        // Create direction vector from start to end
        gp_Vec lineVec(startPoint, endPoint);
        if (lineVec.Magnitude() < Precision::Confusion()) {
            // Points are too close, use default plane
            return gp_Pln(midPoint, normal);
        }

        gp_Dir lineDir(lineVec);

        // Create a plane perpendicular to the normal and containing the line
        // The plane normal should be perpendicular to both the line direction and the given normal
        gp_Vec planeNormalVec = gp_Vec(lineDir) ^ gp_Vec(normal);
        if (planeNormalVec.Magnitude() < Precision::Confusion()) {
            // Line is parallel to normal, use the normal directly
            return gp_Pln(midPoint, normal);
        }

        gp_Dir planeNormal(planeNormalVec);
        return gp_Pln(midPoint, planeNormal);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Plane from Line Error", MB_OK | MB_ICONERROR);
        return gp_Pln(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
    }
}

gp_Pln CreateDirectionalPlaneFromLine(const TopoDS_Shape& solid, const gp_Pnt& startPoint, const gp_Pnt& endPoint) {
    try {
        // Calculate the line direction vector
        gp_Vec lineVec(startPoint, endPoint);
        if (lineVec.Magnitude() < Precision::Confusion()) {
            // Points are too close, use default plane
            return gp_Pln(startPoint, gp_Dir(0, 0, 1));
        }

        gp_Dir lineDir(lineVec);

        // Get the solid's bounding box to understand its orientation
        Bnd_Box bbox;
        BRepBndLib::Add(solid, bbox);
        if (bbox.IsVoid()) {
            return gp_Pln(startPoint, gp_Dir(0, 0, 1));
        }

        Standard_Real xMin, yMin, zMin, xMax, yMax, zMax;
        bbox.Get(xMin, yMin, zMin, xMax, yMax, zMax);

        // Calculate solid dimensions
        Standard_Real xSize = xMax - xMin;
        Standard_Real ySize = yMax - yMin;
        Standard_Real zSize = zMax - zMin;

        // Determine the best cutting plane normal based on line direction
        gp_Dir planeNormal;

        // Get line direction components
        Standard_Real lineX = abs(lineDir.X());
        Standard_Real lineY = abs(lineDir.Y());
        Standard_Real lineZ = abs(lineDir.Z());

        // Professional CAD logic: Choose plane normal based on line direction
        if (lineX > lineY && lineX > lineZ) {
            // Line is mostly horizontal (X direction)
            // Cut with a plane perpendicular to X (YZ plane)
            planeNormal = gp_Dir(1, 0, 0);
        }
        else if (lineY > lineX && lineY > lineZ) {
            // Line is mostly in Y direction
            // Cut with a plane perpendicular to Y (XZ plane)
            planeNormal = gp_Dir(0, 1, 0);
        }
        else {
            // Line is mostly vertical (Z direction) or diagonal
            // Cut with a plane perpendicular to Z (XY plane)
            planeNormal = gp_Dir(0, 0, 1);
        }

        // Use the midpoint of the line as the plane origin
        gp_Pnt midPoint((startPoint.X() + endPoint.X()) / 2.0,
                        (startPoint.Y() + endPoint.Y()) / 2.0,
                        (startPoint.Z() + endPoint.Z()) / 2.0);

        // For even better results, adjust the plane position to go through solid center
        gp_Pnt solidCenter((xMin + xMax) / 2.0, (yMin + yMax) / 2.0, (zMin + zMax) / 2.0);

        // Use solid center but keep the line's plane orientation
        return gp_Pln(solidCenter, planeNormal);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Directional Plane Error", MB_OK | MB_ICONERROR);
        return gp_Pln(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
    }
}

// ============================================================================
// FREECAD-STYLE OFFSET OPERATIONS
// ============================================================================

TopoDS_Shape OffsetShape(const TopoDS_Shape& shape, Standard_Real offsetValue, Standard_Boolean makeThick) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "Shape is null", "Offset Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Determine the best offset method based on shape type
        TopAbs_ShapeEnum shapeType = shape.ShapeType();

        switch (shapeType) {
            case TopAbs_FACE:
                return OffsetFace(TopoDS::Face(shape), offsetValue);

            case TopAbs_WIRE:
                return OffsetWire(TopoDS::Wire(shape), offsetValue);

            case TopAbs_EDGE:
                return OffsetEdge(TopoDS::Edge(shape), offsetValue);

            case TopAbs_SOLID:
            case TopAbs_SHELL:
                if (makeThick) {
                    return CreateThickSolid(shape, TopTools_ListOfShape(), offsetValue);
                } else {
                    return OffsetSolid(shape, offsetValue);
                }

            default:
                // For compound shapes, try general offset using Skin mode (FreeCAD compatible)
                return PerformOffset(shape, offsetValue, OFFSET_SKIN);
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Offset Shape Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

TopoDS_Shape OffsetFace(const TopoDS_Face& face, Standard_Real offsetValue) {
    try {
        if (face.IsNull()) {
            return TopoDS_Shape();
        }

        // Use BRepOffsetAPI_MakeOffsetShape for face offset
        BRepOffsetAPI_MakeOffsetShape offsetMaker;
        offsetMaker.PerformByJoin(face, offsetValue, 1e-3);

        if (offsetMaker.IsDone()) {
            return offsetMaker.Shape();
        }

        // Fallback: Create offset using face normal
        Handle(Geom_Surface) surface = BRep_Tool::Surface(face);
        if (!surface.IsNull()) {
            // Get face center and normal
            Standard_Real uMin, uMax, vMin, vMax;
            BRepTools::UVBounds(face, uMin, uMax, vMin, vMax);
            Standard_Real uMid = (uMin + uMax) / 2.0;
            Standard_Real vMid = (vMin + vMax) / 2.0;

            gp_Pnt centerPoint;
            gp_Vec normal;
            surface->D1(uMid, vMid, centerPoint, normal, normal);

            if (normal.Magnitude() > Precision::Confusion()) {
                gp_Dir normalDir(normal);
                gp_Vec offsetVec = normalDir.XYZ() * offsetValue;

                // Create offset surface using the correct API
                Handle(Geom_OffsetSurface) offsetSurface = new Geom_OffsetSurface(surface, offsetValue);

                // Use the surface directly with BRepBuilderAPI_MakeFace
                BRepBuilderAPI_MakeFace faceMaker(offsetSurface, Precision::Confusion());
                if (faceMaker.IsDone()) {
                    return faceMaker.Face();
                }
            }
        }

        return face;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Offset Face Error", MB_OK | MB_ICONERROR);
        return face;
    }
}

TopoDS_Shape OffsetWire(const TopoDS_Wire& wire, Standard_Real offsetValue, const gp_Pln& plane) {
    try {
        if (wire.IsNull()) {
            return TopoDS_Shape();
        }

        // Use BRepOffsetAPI_MakeOffset for 2D wire offset (updated API)
        BRepOffsetAPI_MakeOffset offsetMaker;
        offsetMaker.Init(GeomAbs_Arc); // Use GeomAbs_JoinType instead of BRepOffset_Mode
        offsetMaker.AddWire(wire);
        offsetMaker.Perform(offsetValue);

        if (offsetMaker.IsDone()) {
            return offsetMaker.Shape();
        }

        return wire;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Offset Wire Error", MB_OK | MB_ICONERROR);
        return wire;
    }
}

TopoDS_Shape OffsetEdge(const TopoDS_Edge& edge, Standard_Real offsetValue, const gp_Pln& plane) {
    try {
        if (edge.IsNull()) {
            return TopoDS_Shape();
        }

        // Create a wire from the edge and offset it
        BRepBuilderAPI_MakeWire wireMaker(edge);
        if (wireMaker.IsDone()) {
            return OffsetWire(wireMaker.Wire(), offsetValue, plane);
        }

        return edge;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Offset Edge Error", MB_OK | MB_ICONERROR);
        return edge;
    }
}

TopoDS_Shape OffsetSolid(const TopoDS_Shape& solid, Standard_Real offsetValue, const TopTools_ListOfShape& facesToRemove) {
    try {
        if (solid.IsNull()) {
            return TopoDS_Shape();
        }

        // Use BRepOffsetAPI_MakeThickSolid for solid offset
        BRepOffsetAPI_MakeThickSolid thickMaker;
        thickMaker.MakeThickSolidByJoin(solid, facesToRemove, offsetValue, 1e-3);

        if (thickMaker.IsDone()) {
            return thickMaker.Shape();
        }

        // Fallback: Use general offset
        BRepOffsetAPI_MakeOffsetShape offsetMaker;
        offsetMaker.PerformByJoin(solid, offsetValue, 1e-3);

        if (offsetMaker.IsDone()) {
            return offsetMaker.Shape();
        }

        return solid;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Offset Solid Error", MB_OK | MB_ICONERROR);
        return solid;
    }
}

TopoDS_Shape CreateThickSolid(const TopoDS_Shape& solid, const TopTools_ListOfShape& facesToRemove, Standard_Real thickness, Standard_Real tolerance) {
    try {
        if (solid.IsNull()) {
            MessageBoxA(NULL, "Solid is null", "Thick Solid Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Use BRepOffsetAPI_MakeThickSolid (like FreeCAD Thickness tool)
        BRepOffsetAPI_MakeThickSolid thickMaker;
        thickMaker.MakeThickSolidByJoin(solid, facesToRemove, thickness, tolerance);

        if (thickMaker.IsDone()) {
            TopoDS_Shape result = thickMaker.Shape();
            if (!result.IsNull()) {
                return result;
            }
        }

        // Alternative method: Create shell offset
        BRepOffsetAPI_MakeOffsetShape offsetMaker;
        offsetMaker.PerformByJoin(solid, thickness, tolerance);

        if (offsetMaker.IsDone()) {
            return offsetMaker.Shape();
        }

        MessageBoxA(NULL, "Failed to create thick solid. Try different thickness value.", "Thick Solid Warning", MB_OK | MB_ICONWARNING);
        return solid;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Thick Solid Error", MB_OK | MB_ICONERROR);
        return solid;
    }
}

// FreeCAD-Compatible Offset Implementation (matches FreeCAD's approach exactly)
TopoDS_Shape PerformFreeCADOffset(const TopoDS_Shape& shape,
                                  Standard_Real offsetValue,
                                  OffsetMode mode,
                                  JoinType join,
                                  Standard_Boolean intersection,
                                  Standard_Boolean selfIntersection,
                                  Standard_Boolean fill,
                                  Standard_Real tolerance) {
    try {
        if (shape.IsNull()) {
            MessageBoxA(NULL, "No source shape provided", "FreeCAD Offset Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Match FreeCAD's offset implementation exactly
        switch (mode) {
            case OFFSET_SKIN: {
                // "Skin" mode - Surface offset (like FreeCAD)

                // For solids, use different approach based on offset direction and join type
                if (shape.ShapeType() == TopAbs_SOLID) {
                    if (offsetValue > 0) {
                        // Positive offset - expand the solid
                        TopoDS_Shape result;

                        // For sharp edges (JOIN_INTERSECTION), try face-by-face offset approach
                        if (join == JOIN_INTERSECTION) {
                            result = PerformSharpEdgeOffset(shape, offsetValue, tolerance);
                        }

                        // If sharp edge method failed or not requested, use standard method
                        if (result.IsNull()) {
                            BRepOffsetAPI_MakeOffsetShape offsetMaker;
                            offsetMaker.PerformByJoin(shape, offsetValue, tolerance);

                            if (offsetMaker.IsDone()) {
                                result = offsetMaker.Shape();
                                if (!result.IsNull() && !result.IsSame(shape)) {
                                    return result;
                                }
                            }
                        } else {
                            return result;
                        }

                        // Fallback: Try alternative method for positive offset
                        try {
                            BRepOffsetAPI_MakeThickSolid thickMaker;
                            TopTools_ListOfShape emptyList;
                            thickMaker.MakeThickSolidByJoin(shape, emptyList, offsetValue, tolerance);

                            if (thickMaker.IsDone()) {
                                TopoDS_Shape result = thickMaker.Shape();
                                if (!result.IsNull()) {
                                    return result;
                                }
                            }
                        } catch (...) {
                            // Continue to error message
                        }

                    } else {
                        // Negative offset - create hollow/thick solid
                        BRepOffsetAPI_MakeThickSolid thickMaker;
                        TopTools_ListOfShape emptyList; // No faces to remove - creates hollow
                        thickMaker.MakeThickSolidByJoin(shape, emptyList, offsetValue, tolerance);

                        if (thickMaker.IsDone()) {
                            TopoDS_Shape result = thickMaker.Shape();
                            if (!result.IsNull() && !result.IsSame(shape)) {
                                return result;
                            }
                        }
                    }
                } else {
                    // For non-solids, use standard offset
                    BRepOffsetAPI_MakeOffsetShape offsetMaker;
                    offsetMaker.PerformByJoin(shape, offsetValue, tolerance);

                    if (offsetMaker.IsDone()) {
                        TopoDS_Shape result = offsetMaker.Shape();
                        if (!result.IsNull() && !result.IsSame(shape)) {
                            return result;
                        }
                    }
                }

                // If we get here, offset failed
                char errorMsg[256];
                sprintf_s(errorMsg, "Skin offset failed for %s.\nOffset value: %.2f\nTry different parameters.",
                         (shape.ShapeType() == TopAbs_SOLID) ? "solid" : "shape", offsetValue);
                MessageBoxA(NULL, errorMsg, "FreeCAD Offset Error", MB_OK | MB_ICONERROR);
                return shape;
            }

            case OFFSET_PIPE: {
                // "Pipe" mode - Pipe/tube creation (like FreeCAD)
                // For 2D shapes, this creates a pipe-like offset
                BRepOffsetAPI_MakeOffset offsetMaker;
                offsetMaker.Init(static_cast<GeomAbs_JoinType>(join));

                // Add wires from the shape
                TopExp_Explorer wireExp(shape, TopAbs_WIRE);
                for (; wireExp.More(); wireExp.Next()) {
                    TopoDS_Wire wire = TopoDS::Wire(wireExp.Current());
                    offsetMaker.AddWire(wire);
                }

                offsetMaker.Perform(offsetValue);

                if (offsetMaker.IsDone()) {
                    return offsetMaker.Shape();
                } else {
                    MessageBoxA(NULL, "Pipe offset failed. Try different parameters.", "FreeCAD Offset Error", MB_OK | MB_ICONERROR);
                    return shape;
                }
            }

            case OFFSET_RECTOVERSO: {
                // "RectoVerso" mode - Both directions (like FreeCAD)
                TopoDS_Shape positiveOffset = PerformFreeCADOffset(shape, offsetValue, OFFSET_SKIN, join, intersection, selfIntersection, fill, tolerance);
                TopoDS_Shape negativeOffset = PerformFreeCADOffset(shape, -offsetValue, OFFSET_SKIN, join, intersection, selfIntersection, fill, tolerance);

                if (!positiveOffset.IsNull() && !negativeOffset.IsNull()) {
                    // Combine both offsets into a compound
                    BRep_Builder builder;
                    TopoDS_Compound compound;
                    builder.MakeCompound(compound);
                    builder.Add(compound, positiveOffset);
                    builder.Add(compound, negativeOffset);
                    return compound;
                } else {
                    MessageBoxA(NULL, "RectoVerso offset failed. Try different parameters.", "FreeCAD Offset Error", MB_OK | MB_ICONERROR);
                    return shape;
                }
            }

            default:
                MessageBoxA(NULL, "Unknown offset mode", "FreeCAD Offset Error", MB_OK | MB_ICONERROR);
                return shape;
        }

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "FreeCAD Offset Error", MB_OK | MB_ICONERROR);
        return shape;
    }
}

// Sharp Edge Offset Implementation - preserves truly sharp edges
TopoDS_Shape PerformSharpEdgeOffset(const TopoDS_Shape& shape, Standard_Real offsetValue, Standard_Real tolerance) {
    try {
        if (shape.IsNull() || shape.ShapeType() != TopAbs_SOLID) {
            return TopoDS_Shape();
        }

        // For simple box-like shapes, use scaling approach for perfect sharp edges
        if (IsBoxLikeShape(shape)) {
            return ScaleShapeUniformly(shape, offsetValue);
        }

        // For complex shapes, try face-by-face offset with sharp reconstruction
        return PerformFaceByFaceOffset(shape, offsetValue, tolerance);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Sharp Edge Offset Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

// Check if shape is box-like (rectangular solid with flat faces)
bool IsBoxLikeShape(const TopoDS_Shape& shape) {
    try {
        if (shape.ShapeType() != TopAbs_SOLID) return false;

        // Count faces and check if they are planar
        int faceCount = 0;
        TopExp_Explorer faceExp(shape, TopAbs_FACE);
        for (; faceExp.More(); faceExp.Next()) {
            faceCount++;
            TopoDS_Face face = TopoDS::Face(faceExp.Current());

            // Check if face is planar
            Handle(Geom_Surface) surface = BRep_Tool::Surface(face);
            if (surface.IsNull()) return false;

            Handle(Geom_Plane) plane = Handle(Geom_Plane)::DownCast(surface);
            if (plane.IsNull()) return false; // Not a planar face
        }

        // Box should have exactly 6 faces
        return (faceCount == 6);

    } catch (const Standard_Failure&) {
        return false;
    }
}

// Scale shape uniformly to create offset with perfect sharp edges
TopoDS_Shape ScaleShapeUniformly(const TopoDS_Shape& shape, Standard_Real offsetValue) {
    try {
        // Get bounding box to determine scale factor
        Bnd_Box bbox;
        BRepBndLib::Add(shape, bbox);
        if (bbox.IsVoid()) return TopoDS_Shape();

        Standard_Real xmin, ymin, zmin, xmax, ymax, zmax;
        bbox.Get(xmin, ymin, zmin, xmax, ymax, zmax);

        // Calculate average dimension for scaling
        Standard_Real avgDim = ((xmax - xmin) + (ymax - ymin) + (zmax - zmin)) / 3.0;
        if (avgDim < Precision::Confusion()) return TopoDS_Shape();

        // Calculate scale factor
        Standard_Real scaleFactor = (avgDim + 2.0 * offsetValue) / avgDim;
        if (scaleFactor <= 0) return TopoDS_Shape();

        // Create scaling transformation
        gp_Trsf scaling;
        scaling.SetScale(gp_Pnt(0, 0, 0), scaleFactor);

        // Apply transformation
        BRepBuilderAPI_Transform transformer(shape, scaling);
        if (!transformer.IsDone()) return TopoDS_Shape();

        return transformer.Shape();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Scale Shape Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

// Face-by-face offset for complex shapes
TopoDS_Shape PerformFaceByFaceOffset(const TopoDS_Shape& shape, Standard_Real offsetValue, Standard_Real tolerance) {
    try {
        // This is a simplified implementation
        // For truly sharp edges, we would need to offset each face individually
        // and then reconstruct the solid, which is quite complex

        // For now, use the standard method but with minimal tolerance
        BRepOffsetAPI_MakeOffsetShape offsetMaker;
        offsetMaker.PerformByJoin(shape, offsetValue, tolerance * 0.1); // Tighter tolerance

        if (offsetMaker.IsDone()) {
            return offsetMaker.Shape();
        }

        return TopoDS_Shape();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Face By Face Offset Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

TopoDS_Shape PerformOffset(const TopoDS_Shape& shape, Standard_Real offsetValue, OffsetMode mode, const TopTools_ListOfShape& facesToRemove) {
    // Legacy function - now calls FreeCAD-compatible version
    return PerformFreeCADOffset(shape, offsetValue, mode, JOIN_ARC, Standard_False, Standard_False, Standard_False, 1e-3);
}

// ============================================================================
// SWEEP OPERATIONS (FREECAD-STYLE)
// ============================================================================

TopoDS_Shape SweepProfileAlongPath(const TopoDS_Shape& profile, const TopoDS_Shape& path) {
    try {
        if (profile.IsNull() || path.IsNull()) {
            MessageBoxA(NULL, "Profile or path is null", "Sweep Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Convert path to wire if it's an edge
        TopoDS_Wire sweepPath;
        if (path.ShapeType() == TopAbs_EDGE) {
            BRepBuilderAPI_MakeWire wireMaker(TopoDS::Edge(path));
            if (!wireMaker.IsDone()) {
                MessageBoxA(NULL, "Failed to create wire from edge path.", "Sweep Error", MB_OK | MB_ICONERROR);
                return TopoDS_Shape();
            }
            sweepPath = wireMaker.Wire();
        } else if (path.ShapeType() == TopAbs_WIRE) {
            sweepPath = TopoDS::Wire(path);
        } else {
            MessageBoxA(NULL, "Path must be an edge or wire.", "Sweep Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Convert profile to wire
        TopoDS_Wire sweepProfile;
        if (profile.ShapeType() == TopAbs_WIRE) {
            sweepProfile = TopoDS::Wire(profile);
        } else if (profile.ShapeType() == TopAbs_FACE) {
            // Extract outer wire from face
            TopoDS_Face face = TopoDS::Face(profile);
            TopExp_Explorer exp(face, TopAbs_WIRE);
            if (exp.More()) {
                sweepProfile = TopoDS::Wire(exp.Current());
            } else {
                MessageBoxA(NULL, "Cannot extract wire from face profile.", "Sweep Error", MB_OK | MB_ICONERROR);
                return TopoDS_Shape();
            }
        } else {
            MessageBoxA(NULL, "Profile must be a wire or face.", "Sweep Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        // Perform the sweep operation using BRepOffsetAPI_MakePipe
        // Following the working pattern: BRepOffsetAPI_MakePipe(path, profile)
        BRepOffsetAPI_MakePipe pipeMaker(sweepPath, sweepProfile);

        if (!pipeMaker.IsDone()) {
            MessageBoxA(NULL, "Sweep operation failed.\nTry creating compatible profile and path shapes.", "Sweep Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        TopoDS_Shape result = pipeMaker.Shape();
        if (result.IsNull()) {
            MessageBoxA(NULL, "Sweep operation produced null result.", "Sweep Error", MB_OK | MB_ICONERROR);
            return TopoDS_Shape();
        }

        return result;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Sweep Operation Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

bool ValidateProfileForSweep(const TopoDS_Shape& profile) {
    try {
        if (profile.IsNull()) {
            return false;
        }

        TopAbs_ShapeEnum shapeType = profile.ShapeType();

        // Accept faces and wires
        if (shapeType == TopAbs_FACE) {
            return true;
        }

        if (shapeType == TopAbs_WIRE) {
            TopoDS_Wire wire = TopoDS::Wire(profile);

            // Check if wire is closed
            if (!BRep_Tool::IsClosed(wire)) {
                return false;
            }

            // Check wire validity
            BRepCheck_Wire wireChecker(wire);
            if (wireChecker.Status().First() != BRepCheck_NoError) {
                return false;
            }

            return true;
        }

        // For compound shapes, check if they contain valid profiles
        if (shapeType == TopAbs_COMPOUND) {
            TopExp_Explorer exp(profile, TopAbs_WIRE);
            if (exp.More()) {
                return ValidateProfileForSweep(exp.Current());
            }

            exp.Init(profile, TopAbs_FACE);
            if (exp.More()) {
                return ValidateProfileForSweep(exp.Current());
            }
        }

        return false;

    } catch (const Standard_Failure&) {
        return false;
    }
}

bool ValidatePathForSweep(const TopoDS_Shape& path) {
    try {
        if (path.IsNull()) {
            return false;
        }

        TopAbs_ShapeEnum shapeType = path.ShapeType();

        // Accept edges and wires
        if (shapeType == TopAbs_EDGE) {
            TopoDS_Edge edge = TopoDS::Edge(path);

            // Check if edge is degenerate
            if (BRep_Tool::Degenerated(edge)) {
                return false;
            }

            return true;
        }

        if (shapeType == TopAbs_WIRE) {
            TopoDS_Wire wire = TopoDS::Wire(path);

            // Check wire validity and continuity
            BRepCheck_Wire wireChecker(wire);
            if (wireChecker.Status().First() != BRepCheck_NoError) {
                return false;
            }

            return true;
        }

        // For compound shapes, check if they contain valid paths
        if (shapeType == TopAbs_COMPOUND) {
            TopExp_Explorer exp(path, TopAbs_WIRE);
            if (exp.More()) {
                return ValidatePathForSweep(exp.Current());
            }

            exp.Init(path, TopAbs_EDGE);
            if (exp.More()) {
                return ValidatePathForSweep(exp.Current());
            }
        }

        return false;

    } catch (const Standard_Failure&) {
        return false;
    }
}

TopoDS_Shape CreateSweepPreview(const TopoDS_Shape& profile, const TopoDS_Shape& path) {
    try {
        // For preview, we can create a simplified version or just return the full sweep
        // This could be enhanced to show a wireframe or simplified representation
        return SweepProfileAlongPath(profile, path);

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Sweep Preview Error", MB_OK | MB_ICONERROR);
        return TopoDS_Shape();
    }
}

// ============================================================================
// HELPER FUNCTIONS FOR CREATING SWEEP-COMPATIBLE SHAPES
// ============================================================================

TopoDS_Wire CreateCircularProfile(double radius, const gp_Pnt& center) {
    try {
        // Create a circular profile using the working pattern from your code
        gp_Ax2 axis(center, gp_Dir(0, 0, 1)); // Circle in XY plane at center
        Handle(Geom_Circle) circle = GC_MakeCircle(axis, radius);
        Handle(Geom_TrimmedCurve) arc = new Geom_TrimmedCurve(circle, 0, 2 * M_PI);

        BRepBuilderAPI_MakeEdge edgeMaker(arc);
        BRepBuilderAPI_MakeWire wireMaker(edgeMaker.Edge());

        if (!wireMaker.IsDone()) {
            MessageBoxA(NULL, "Failed to create circular profile wire", "Create Circular Profile Error", MB_OK | MB_ICONERROR);
            return TopoDS_Wire();
        }

        return wireMaker.Wire();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Circular Profile Error", MB_OK | MB_ICONERROR);
        return TopoDS_Wire();
    }
}

TopoDS_Wire CreateRectangularProfile(double width, double height, const gp_Pnt& center) {
    try {
        // Create a rectangular profile centered at the given point
        double halfWidth = width / 2.0;
        double halfHeight = height / 2.0;

        gp_Pnt p1(center.X() - halfWidth, center.Y() - halfHeight, center.Z());
        gp_Pnt p2(center.X() + halfWidth, center.Y() - halfHeight, center.Z());
        gp_Pnt p3(center.X() + halfWidth, center.Y() + halfHeight, center.Z());
        gp_Pnt p4(center.X() - halfWidth, center.Y() + halfHeight, center.Z());

        TopoDS_Edge e1 = BRepBuilderAPI_MakeEdge(p1, p2);
        TopoDS_Edge e2 = BRepBuilderAPI_MakeEdge(p2, p3);
        TopoDS_Edge e3 = BRepBuilderAPI_MakeEdge(p3, p4);
        TopoDS_Edge e4 = BRepBuilderAPI_MakeEdge(p4, p1);

        BRepBuilderAPI_MakeWire wireBuilder;
        wireBuilder.Add(e1);
        wireBuilder.Add(e2);
        wireBuilder.Add(e3);
        wireBuilder.Add(e4);

        if (!wireBuilder.IsDone()) {
            MessageBoxA(NULL, "Failed to create rectangular wire", "Create Rectangular Profile Error", MB_OK | MB_ICONERROR);
            return TopoDS_Wire();
        }

        return wireBuilder.Wire();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Rectangular Profile Error", MB_OK | MB_ICONERROR);
        return TopoDS_Wire();
    }
}

TopoDS_Wire CreateStraightPath(const gp_Pnt& start, const gp_Pnt& end) {
    try {
        TopoDS_Edge edge = BRepBuilderAPI_MakeEdge(start, end);
        TopoDS_Wire wire = BRepBuilderAPI_MakeWire(edge);

        return wire;

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Straight Path Error", MB_OK | MB_ICONERROR);
        return TopoDS_Wire();
    }
}

TopoDS_Wire CreateCurvedPath() {
    try {
        // Create a multi-segment curved path exactly like in your working code
        gp_Pnt p1(0, 0, 0);
        gp_Pnt p2(0, 0, 50);
        gp_Pnt p3(20, 0, 100);
        gp_Pnt p4(50, 0, 150);

        BRepBuilderAPI_MakeWire wireBuilder;
        wireBuilder.Add(BRepBuilderAPI_MakeEdge(p1, p2));
        wireBuilder.Add(BRepBuilderAPI_MakeEdge(p2, p3));
        wireBuilder.Add(BRepBuilderAPI_MakeEdge(p3, p4));

        if (!wireBuilder.IsDone()) {
            MessageBoxA(NULL, "Failed to create curved path wire", "Create Curved Path Error", MB_OK | MB_ICONERROR);
            return TopoDS_Wire();
        }

        return wireBuilder.Wire();

    } catch (const Standard_Failure& e) {
        MessageBoxA(NULL, e.GetMessageString(), "Create Curved Path Error", MB_OK | MB_ICONERROR);
        return TopoDS_Wire();
    }
}

// ============================================================================
// FILL OPERATIONS - Create smooth surfaces from closed boundaries
// ============================================================================

TopoDS_Face CreateFilledSurface(const TopoDS_Wire& boundary, int degree, int maxSegments) {
    try {
        // Validate the boundary wire
        if (!ValidateBoundaryForFill(boundary)) {
            MessageBoxA(NULL, "Invalid boundary wire for fill operation.\nBoundary must be closed and valid.", "Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        // Create the filling surface using BRepOffsetAPI_MakeFilling
        BRepOffsetAPI_MakeFilling fillMaker(degree, maxSegments);

        // Add the boundary wire as a constraint
        TopExp_Explorer edgeExp(boundary, TopAbs_EDGE);
        for (; edgeExp.More(); edgeExp.Next()) {
            TopoDS_Edge edge = TopoDS::Edge(edgeExp.Current());
            fillMaker.Add(edge, GeomAbs_C0); // C0 continuity (position continuity)
        }

        // Build the surface
        fillMaker.Build();

        if (!fillMaker.IsDone()) {
            MessageBoxA(NULL, "Fill operation failed.\nTry adjusting the boundary or parameters.", "Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        TopoDS_Shape result = fillMaker.Shape();
        if (result.IsNull() || result.ShapeType() != TopAbs_FACE) {
            MessageBoxA(NULL, "Fill operation produced invalid result.", "Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        return TopoDS::Face(result);

    } catch (const Standard_Failure& e) {
        std::string errorMsg = "Fill operation failed: ";
        errorMsg += e.GetMessageString();
        MessageBoxA(NULL, errorMsg.c_str(), "Fill Error", MB_OK | MB_ICONERROR);
        return TopoDS_Face();
    }
}

TopoDS_Face CreateFilledSurface(const TopTools_ListOfShape& edges, int degree, int maxSegments) {
    try {
        // Validate the edges
        if (!ValidateEdgesForFill(edges)) {
            MessageBoxA(NULL, "Invalid edges for fill operation.\nEdges must form a closed loop.", "Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        // Create a wire from the edges
        TopoDS_Wire boundary = CreateWireFromEdges(edges);
        if (boundary.IsNull()) {
            MessageBoxA(NULL, "Failed to create wire from edges.", "Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        // Use the wire-based fill function
        return CreateFilledSurface(boundary, degree, maxSegments);

    } catch (const Standard_Failure& e) {
        std::string errorMsg = "Fill operation failed: ";
        errorMsg += e.GetMessageString();
        MessageBoxA(NULL, errorMsg.c_str(), "Fill Error", MB_OK | MB_ICONERROR);
        return TopoDS_Face();
    }
}

TopoDS_Wire CreateWireFromEdges(const TopTools_ListOfShape& edges) {
    try {
        if (edges.IsEmpty()) {
            return TopoDS_Wire();
        }

        BRepBuilderAPI_MakeWire wireBuilder;

        // Add all edges to the wire builder
        TopTools_ListIteratorOfListOfShape it(edges);
        for (; it.More(); it.Next()) {
            const TopoDS_Shape& shape = it.Value();
            if (shape.ShapeType() == TopAbs_EDGE) {
                TopoDS_Edge edge = TopoDS::Edge(shape);
                wireBuilder.Add(edge);
            }
        }

        if (!wireBuilder.IsDone()) {
            MessageBoxA(NULL, "Failed to create wire from edges.\nEdges may not be properly connected.", "Wire Creation Error", MB_OK | MB_ICONERROR);
            return TopoDS_Wire();
        }

        return wireBuilder.Wire();

    } catch (const Standard_Failure& e) {
        std::string errorMsg = "Wire creation failed: ";
        errorMsg += e.GetMessageString();
        MessageBoxA(NULL, errorMsg.c_str(), "Wire Creation Error", MB_OK | MB_ICONERROR);
        return TopoDS_Wire();
    }
}

bool ValidateBoundaryForFill(const TopoDS_Wire& boundary) {
    try {
        if (boundary.IsNull()) {
            return false;
        }

        // Check if wire is closed
        if (!IsWireClosed(boundary)) {
            return false;
        }

        // Check wire validity
        BRepCheck_Wire wireChecker(boundary);
        if (wireChecker.Status().First() != BRepCheck_NoError) {
            return false;
        }

        // Check if wire has at least 3 edges (minimum for a meaningful boundary)
        int edgeCount = 0;
        TopExp_Explorer edgeExp(boundary, TopAbs_EDGE);
        for (; edgeExp.More(); edgeExp.Next()) {
            edgeCount++;
        }

        if (edgeCount < 3) {
            return false;
        }

        // Check for degenerate edges
        edgeExp.Init(boundary, TopAbs_EDGE);
        for (; edgeExp.More(); edgeExp.Next()) {
            TopoDS_Edge edge = TopoDS::Edge(edgeExp.Current());
            if (BRep_Tool::Degenerated(edge)) {
                return false;
            }
        }

        return true;

    } catch (const Standard_Failure&) {
        return false;
    }
}

bool ValidateEdgesForFill(const TopTools_ListOfShape& edges) {
    try {
        if (edges.IsEmpty()) {
            return false;
        }

        // Check if all shapes are edges
        TopTools_ListIteratorOfListOfShape it(edges);
        for (; it.More(); it.Next()) {
            if (it.Value().ShapeType() != TopAbs_EDGE) {
                return false;
            }
        }

        // Try to create a wire and validate it
        TopoDS_Wire testWire = CreateWireFromEdges(edges);
        if (testWire.IsNull()) {
            return false;
        }

        return ValidateBoundaryForFill(testWire);

    } catch (const Standard_Failure&) {
        return false;
    }
}

bool IsWireClosed(const TopoDS_Wire& wire) {
    try {
        if (wire.IsNull()) {
            return false;
        }

        return BRep_Tool::IsClosed(wire);

    } catch (const Standard_Failure&) {
        return false;
    }
}

TopoDS_Face CreateAdvancedFilledSurface(const TopoDS_Wire& boundary,
                                       int degree,
                                       int maxSegments,
                                       bool useG1Continuity,
                                       bool useG2Continuity) {
    try {
        // Validate the boundary wire
        if (!ValidateBoundaryForFill(boundary)) {
            MessageBoxA(NULL, "Invalid boundary wire for advanced fill operation.", "Advanced Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        // Create the filling surface with advanced continuity options
        BRepOffsetAPI_MakeFilling fillMaker(degree, maxSegments);

        // Add the boundary wire with specified continuity
        TopExp_Explorer edgeExp(boundary, TopAbs_EDGE);
        for (; edgeExp.More(); edgeExp.Next()) {
            TopoDS_Edge edge = TopoDS::Edge(edgeExp.Current());

            // Choose continuity based on parameters
            GeomAbs_Shape continuity = GeomAbs_C0; // Default: position continuity
            if (useG2Continuity) {
                continuity = GeomAbs_C2; // G2: curvature continuity
            } else if (useG1Continuity) {
                continuity = GeomAbs_C1; // G1: tangency continuity
            }

            fillMaker.Add(edge, continuity);
        }

        // Note: In OpenCASCADE 7.9.0, degree and maxSegments are set in constructor
        // Additional parameters can be set using other methods if available

        // Build the surface
        fillMaker.Build();

        if (!fillMaker.IsDone()) {
            MessageBoxA(NULL, "Advanced fill operation failed.\nTry reducing continuity requirements or adjusting parameters.", "Advanced Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        TopoDS_Shape result = fillMaker.Shape();
        if (result.IsNull() || result.ShapeType() != TopAbs_FACE) {
            MessageBoxA(NULL, "Advanced fill operation produced invalid result.", "Advanced Fill Error", MB_OK | MB_ICONERROR);
            return TopoDS_Face();
        }

        return TopoDS::Face(result);

    } catch (const Standard_Failure& e) {
        std::string errorMsg = "Advanced fill operation failed: ";
        errorMsg += e.GetMessageString();
        MessageBoxA(NULL, errorMsg.c_str(), "Advanced Fill Error", MB_OK | MB_ICONERROR);
        return TopoDS_Face();
    }
}

Standard_Real CalculateWireArea(const TopoDS_Wire& wire) {
    try {
        if (wire.IsNull() || !IsWireClosed(wire)) {
            return 0.0;
        }

        // Create a face from the wire to calculate area
        BRepBuilderAPI_MakeFace faceMaker(wire);
        if (!faceMaker.IsDone()) {
            return 0.0;
        }

        TopoDS_Face face = faceMaker.Face();
        GProp_GProps props;
        BRepGProp::SurfaceProperties(face, props);

        return props.Mass(); // Mass represents area for surface properties

    } catch (const Standard_Failure&) {
        return 0.0;
    }
}

gp_Pnt CalculateWireCentroid(const TopoDS_Wire& wire) {
    try {
        if (wire.IsNull()) {
            return gp_Pnt(0, 0, 0);
        }

        // Calculate centroid by averaging vertex positions
        gp_XYZ centroid(0, 0, 0);
        int vertexCount = 0;

        TopExp_Explorer vertexExp(wire, TopAbs_VERTEX);
        for (; vertexExp.More(); vertexExp.Next()) {
            TopoDS_Vertex vertex = TopoDS::Vertex(vertexExp.Current());
            gp_Pnt point = BRep_Tool::Pnt(vertex);
            centroid += point.XYZ();
            vertexCount++;
        }

        if (vertexCount > 0) {
            centroid /= vertexCount;
        }

        return gp_Pnt(centroid);

    } catch (const Standard_Failure&) {
        return gp_Pnt(0, 0, 0);
    }
}

bool CheckWireOrientation(const TopoDS_Wire& wire) {
    try {
        if (wire.IsNull() || !IsWireClosed(wire)) {
            return false;
        }

        // Create a face to check orientation
        BRepBuilderAPI_MakeFace faceMaker(wire);
        if (!faceMaker.IsDone()) {
            return false;
        }

        TopoDS_Face face = faceMaker.Face();

        // Check if the face is properly oriented (not reversed)
        return face.Orientation() == TopAbs_FORWARD;

    } catch (const Standard_Failure&) {
        return false;
    }
}

TopoDS_Wire FixWireOrientation(const TopoDS_Wire& wire) {
    try {
        if (wire.IsNull()) {
            return TopoDS_Wire();
        }

        // If orientation is correct, return as-is
        if (CheckWireOrientation(wire)) {
            return wire;
        }

        // Reverse the wire orientation
        TopoDS_Wire fixedWire = wire;
        fixedWire.Reverse();

        return fixedWire;

    } catch (const Standard_Failure&) {
        return wire; // Return original if fixing fails
    }
}

} // namespace MyCadGeom
