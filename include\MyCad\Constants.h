#pragma once

// Include resource IDs
#include "resource.h"

// Application UI Layout Constants
const int BUTTON_Y_OFFSET = 5;
const int BUTTON_HEIGHT = 30;
const int BUTTON_WIDTH = 80;
const int BUTTON_SPACING = 10;
const int BUTTON_ROW_SPACING = 5;
const int BUTTONS_PER_ROW = 10; // Maximum buttons per row
const int BUTTON_AREA_TOTAL_HEIGHT = BUTTON_Y_OFFSET + (BUTTON_HEIGHT * 2) + BUTTON_ROW_SPACING + 10; // Two rows of buttons
const double DEFAULT_EXTRUSION_DISTANCE = 10.0;
const double DEFAULT_FILLET_RADIUS = 5.0;
const double DEFAULT_CHAMFER_DISTANCE = 5.0;

// 3D Geometry Constants
static const double SHAPE_SPACING_LEGACY = 100.0; // Renamed from SHAPE_SPACING to avoid conflict if used elsewhere

// Window Constants
namespace MyCad {
    constexpr int DEFAULT_WINDOW_WIDTH = 800;
    constexpr int DEFAULT_WINDOW_HEIGHT = 600;
    constexpr int MIN_WINDOW_WIDTH = 400;
    constexpr int MIN_WINDOW_HEIGHT = 300;

    // 3D Viewport settings
    constexpr double DEFAULT_CAMERA_DISTANCE = 100.0;
    constexpr double ZOOM_FACTOR = 1.1;
    constexpr double ROTATION_SPEED = 0.01;

    // Application strings
    constexpr const wchar_t* APP_NAME = L"My CAD Application";
    constexpr const wchar_t* WINDOW_CLASS_NAME = L"MyCadMainWindow";

    // File filters for dialogs
    constexpr const wchar_t* STEP_FILE_FILTER = L"STEP Files (*.step;*.stp)\0*.step;*.stp\0All Files (*.*)\0*.*\0";
    constexpr const wchar_t* STL_FILE_FILTER = L"STL Files (*.stl)\0*.stl\0All Files (*.*)\0*.*\0";
}