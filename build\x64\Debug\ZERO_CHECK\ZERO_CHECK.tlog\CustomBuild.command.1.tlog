^D:\MYCADAPPLICATION\BUILD\CMAKEFILES\D00473247BE7E34454C7C0AF1B8CB0F0\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/MyCadApplication -BD:/MyCadApplication/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/MyCadApplication/build/MyCadApplication.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
