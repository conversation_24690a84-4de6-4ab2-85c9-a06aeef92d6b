#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <windowsx.h>
#include <commctrl.h>
#include <cstdio>
#include <string>

#include "MyCad/MainWindow.h"
#include "MyCad/AppCore.h"
#include "MyCad/Constants.h"
#include "resource.h"

namespace MyCad
{

    extern AppCore g_appCore;

    MainWindow::MainWindow(HINSTANCE hInstance)
        : m_hWnd(nullptr), m_hOccWindow(nullptr), m_hInstance(hInstance),
          m_hImportButton(nullptr), m_hCreateCubeButton(nullptr), m_hCreateCylinderButton(nullptr),
          m_hCutButton(nullptr), m_hThickButton(nullptr), m_hClearButton(nullptr), m_hExportButton(nullptr),
          m_hExtrudeButton(nullptr), m_hFilletButton(nullptr), m_hChamferButton(nullptr), m_hRevolveButton(nullptr),
          m_hUndoButton(nullptr), m_hRedoButton(nullptr), m_hTrimButton(nullptr), m_hUntrimButton(nullptr), m_hSplitButton(nullptr), m_hJoinButton(nullptr), m_hMirrorButton(nullptr), m_hOffsetButton(nullptr), m_hThickSolidButton(nullptr), m_hSweepButton(nullptr), m_hTestSweepButton(nullptr) {}

    MainWindow::~MainWindow() {}

    bool MainWindow::Create(const std::wstring &title, int nCmdShow, int width, int height)
    {
        // Check if hInstance is valid
        if (m_hInstance == NULL)
        {
            MessageBoxW(NULL, L"ERROR: hInstance is NULL!", L"Invalid hInstance", MB_OK | MB_ICONERROR);
            return false;
        }

        // Check if window class is already registered
        WNDCLASSW existingClass;
        if (GetClassInfoW(m_hInstance, L"MyCadWindowClass", &existingClass))
        {
            // Class already registered, skip registration
        }
        else
        {
            // Register the window class
            WNDCLASSW wc = {};
            wc.lpfnWndProc = MainWindow::WndProcRouter;
            wc.hInstance = m_hInstance;
            wc.lpszClassName = L"MyCadWindowClass";
            wc.hCursor = LoadCursor(NULL, IDC_ARROW);
            wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
            wc.style = CS_HREDRAW | CS_VREDRAW; // Add window class styles

            if (!RegisterClassW(&wc))
            {
                DWORD dwError = GetLastError();
                wchar_t errorMsgBuffer[512];
                FormatMessageW(
                    FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                    NULL, dwError, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                    errorMsgBuffer, sizeof(errorMsgBuffer) / sizeof(wchar_t), NULL);
                wchar_t finalMsg[1024];
                swprintf_s(finalMsg, _countof(finalMsg), L"Window Class Registration Failed!\nError Code: %lu\n%s", dwError, errorMsgBuffer);
                MessageBoxW(NULL, finalMsg, L"Initialization Error", MB_ICONEXCLAMATION | MB_OK);
                return false;
            }
        }

        // Create the window
        m_hWnd = CreateWindowExW(
            0,                            // Extended window style
            L"MyCadWindowClass",          // Window class name
            title.c_str(),                // Window title
            WS_OVERLAPPEDWINDOW,          // Window style
            CW_USEDEFAULT, CW_USEDEFAULT, // Position
            width, height,                // Size
            NULL,                         // Parent window
            NULL,                         // Menu
            m_hInstance,                  // Instance handle
            this                          // Additional application data
        );

        if (!m_hWnd)
        {
            DWORD dwError = GetLastError();
            LPVOID lpMsgBuf;
            FormatMessageW(
                FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                NULL, dwError, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                (LPWSTR)&lpMsgBuf, 0, NULL);
            wchar_t finalMsgBuffer[1024];
            if (lpMsgBuf)
            {
                swprintf_s(finalMsgBuffer, _countof(finalMsgBuffer),
                           L"Window Creation Failed for '%s'!\nError Code: %lu\nSystem Message: %s\nhInstance: %p",
                           title.c_str(), dwError, (LPWSTR)lpMsgBuf, m_hInstance);
                LocalFree(lpMsgBuf);
            }
            else
            {
                swprintf_s(finalMsgBuffer, _countof(finalMsgBuffer),
                           L"Window Creation Failed for '%s'!\nError Code: %lu\n(Could not retrieve system message)\nhInstance: %p",
                           title.c_str(), dwError, m_hInstance);
            }
            MessageBoxW(NULL, finalMsgBuffer, L"Creation Error", MB_ICONEXCLAMATION | MB_OK);
            return false;
        }

        ShowWindow(m_hWnd, nCmdShow);
        UpdateWindow(m_hWnd);
        return true;
    }

    LRESULT CALLBACK MainWindow::WndProcRouter(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
    {
        MainWindow *pThis = nullptr;
        if (msg == WM_NCCREATE)
        {
            CREATESTRUCT *pCreate = (CREATESTRUCT *)lParam;
            pThis = (MainWindow *)pCreate->lpCreateParams;
            SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)pThis);
            pThis->m_hWnd = hwnd; // Set the window handle here
        }
        else
        {
            pThis = (MainWindow *)GetWindowLongPtr(hwnd, GWLP_USERDATA);
        }

        if (pThis)
        {
            return pThis->HandleMessage(msg, wParam, lParam);
        }
        else
        {
            return DefWindowProc(hwnd, msg, wParam, lParam);
        }
    }

    void MainWindow::CreateButtons()
    {
        // First row of buttons
        int buttonX = 10;
        int buttonY = BUTTON_Y_OFFSET;
        int buttonCount = 0;

        // Row 1: Basic operations
        m_hImportButton = CreateWindowW(L"BUTTON", L"Import", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                        buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_IMPORT_SHAPE_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hCreateCubeButton = CreateWindowW(L"BUTTON", L"Cube", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                            buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_CREATE_CUBE_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hCreateCylinderButton = CreateWindowW(L"BUTTON", L"Cylinder", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                                buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_CREATE_CYLINDER_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hCutButton = CreateWindowW(L"BUTTON", L"Cut", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                     buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_CUT_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hExportButton = CreateWindowW(L"BUTTON", L"Export", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                        buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_EXPORT_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hThickButton = CreateWindowW(L"BUTTON", L"Thick/Off", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                       buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_THICK_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hExtrudeButton = CreateWindowW(L"BUTTON", L"Extrude", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                         buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_EXTRUDE_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hFilletButton = CreateWindowW(L"BUTTON", L"Fillet", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                        buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_FILLET_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hChamferButton = CreateWindowW(L"BUTTON", L"Chamfer", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                         buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_CHAMFER_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hRevolveButton = CreateWindowW(L"BUTTON", L"Revolve", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                         buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_REVOLVE_BUTTON, m_hInstance, NULL);
        buttonCount++;

        // Start second row
        buttonX = 10;
        buttonY = BUTTON_Y_OFFSET + BUTTON_HEIGHT + BUTTON_ROW_SPACING;
        buttonCount = 0;

        // Row 2: Advanced operations
        m_hUndoButton = CreateWindowW(L"BUTTON", L"Undo", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                      buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_UNDO_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hRedoButton = CreateWindowW(L"BUTTON", L"Redo", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                      buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_REDO_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hTrimButton = CreateWindowW(L"BUTTON", L"Trim", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                      buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_TRIM_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hUntrimButton = CreateWindowW(L"BUTTON", L"Untrim", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                        buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_UNTRIM_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hSplitButton = CreateWindowW(L"BUTTON", L"Split", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                       buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_SPLIT_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hJoinButton = CreateWindowW(L"BUTTON", L"Join", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                      buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_JOIN_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hMirrorButton = CreateWindowW(L"BUTTON", L"Mirror", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                        buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_MIRROR_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hOffsetButton = CreateWindowW(L"BUTTON", L"Offset", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                        buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_OFFSET_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hThickSolidButton = CreateWindowW(L"BUTTON", L"ThickSolid", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                            buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_THICK_SOLID_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        m_hSweepButton = CreateWindowW(L"BUTTON", L"Sweep", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                       buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_SWEEP_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        buttonCount++;

        // Continue on same row if space allows, otherwise start new section
        m_hTestSweepButton = CreateWindowW(L"BUTTON", L"TestSweep", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                           buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_TEST_SWEEP_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        // Fill operation buttons
        m_hFillButton = CreateWindowW(L"BUTTON", L"Fill", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                      buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_FILL_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        m_hFillEdgesButton = CreateWindowW(L"BUTTON", L"FillEdges", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                           buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_FILL_EDGES_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        m_hAdvancedFillButton = CreateWindowW(L"BUTTON", L"AdvFill", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                              buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_ADVANCED_FILL_BUTTON, m_hInstance, NULL);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        m_hClearButton = CreateWindowW(L"BUTTON", L"Clear", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                       buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)IDC_CLEAR_BUTTON, m_hInstance, NULL);
    }

    void MainWindow::ResizeOccView()
    {
        if (m_hOccWindow)
        {
            RECT clientRect;
            GetClientRect(m_hWnd, &clientRect);

            // Reserve space for model tree on the left (250 pixels)
            int treeWidth = 250;
            int occX = treeWidth;
            int occWidth = clientRect.right - treeWidth;
            int occHeight = clientRect.bottom - clientRect.top - BUTTON_AREA_TOTAL_HEIGHT;

            if (occWidth > 0 && occHeight > 0)
            {
                SetWindowPos(m_hOccWindow, NULL, occX, BUTTON_AREA_TOTAL_HEIGHT, occWidth, occHeight, SWP_NOZORDER);
                g_appCore.MustBeResized();
                g_appCore.RedrawView();
            }

            // Also resize the model tree
            g_appCore.ResizeModelTree(0, BUTTON_AREA_TOTAL_HEIGHT, treeWidth, occHeight);
        }
    }

    void MainWindow::ResizeUIElements()
    {
        ResizeOccView();

        // First row of buttons
        int buttonX = 10;
        int buttonY = BUTTON_Y_OFFSET;
        UINT commonShowFlags = SWP_NOZORDER | SWP_SHOWWINDOW;

        // Row 1: Basic operations
        if (m_hImportButton)
            SetWindowPos(m_hImportButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hCreateCubeButton)
            SetWindowPos(m_hCreateCubeButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hCreateCylinderButton)
            SetWindowPos(m_hCreateCylinderButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hCutButton)
            SetWindowPos(m_hCutButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hExportButton)
            SetWindowPos(m_hExportButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hThickButton)
            SetWindowPos(m_hThickButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hExtrudeButton)
            SetWindowPos(m_hExtrudeButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hFilletButton)
            SetWindowPos(m_hFilletButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hChamferButton)
            SetWindowPos(m_hChamferButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hRevolveButton)
            SetWindowPos(m_hRevolveButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);

        // Second row of buttons
        buttonX = 10;
        buttonY = BUTTON_Y_OFFSET + BUTTON_HEIGHT + BUTTON_ROW_SPACING;

        // Row 2: Advanced operations
        if (m_hUndoButton)
            SetWindowPos(m_hUndoButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hRedoButton)
            SetWindowPos(m_hRedoButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hTrimButton)
            SetWindowPos(m_hTrimButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hUntrimButton)
            SetWindowPos(m_hUntrimButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hSplitButton)
            SetWindowPos(m_hSplitButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hJoinButton)
            SetWindowPos(m_hJoinButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hMirrorButton)
            SetWindowPos(m_hMirrorButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hOffsetButton)
            SetWindowPos(m_hOffsetButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hThickSolidButton)
            SetWindowPos(m_hThickSolidButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hSweepButton)
            SetWindowPos(m_hSweepButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        // Continue on same row
        if (m_hTestSweepButton)
            SetWindowPos(m_hTestSweepButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        // Fill operation buttons
        if (m_hFillButton)
            SetWindowPos(m_hFillButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hFillEdgesButton)
            SetWindowPos(m_hFillEdgesButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;
        if (m_hAdvancedFillButton)
            SetWindowPos(m_hAdvancedFillButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
        buttonX += BUTTON_WIDTH + BUTTON_SPACING;

        if (m_hClearButton)
            SetWindowPos(m_hClearButton, HWND_TOP, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT, commonShowFlags);
    }

    LRESULT MainWindow::HandleMessage(UINT msg, WPARAM wParam, LPARAM lParam)
    {
        static bool isPanningS = false;

        switch (msg)
        {
        case WM_CREATE:
        {
            CreateButtons();
            m_hOccWindow = CreateWindowExW(0, L"STATIC", NULL, WS_CHILD | WS_VISIBLE | WS_CLIPSIBLINGS,
                                           0, BUTTON_AREA_TOTAL_HEIGHT, 100, 100,
                                           m_hWnd, NULL, m_hInstance, NULL);
            if (!m_hOccWindow)
            {
                MessageBoxW(m_hWnd, L"OCC Child Window Creation Failed!", L"Error", MB_OK | MB_ICONERROR);
                return -1;
            }
            g_appCore.SetWindowHandles(m_hWnd, m_hOccWindow, BUTTON_AREA_TOTAL_HEIGHT);
            if (!g_appCore.InitializeGraphics(m_hOccWindow))
            {
                MessageBoxW(m_hWnd, L"AppCore Graphics Initialization Failed!", L"Error", MB_OK | MB_ICONERROR);
                return -1;
            }
            g_appCore.InitializeContextStyles();

            // Initialize model tree (positioned on the left side)
            RECT clientRect;
            GetClientRect(m_hWnd, &clientRect);
            int treeWidth = 250;
            int treeHeight = clientRect.bottom - BUTTON_AREA_TOTAL_HEIGHT;
            if (!g_appCore.InitializeModelTree(m_hWnd, 0, BUTTON_AREA_TOTAL_HEIGHT, treeWidth, treeHeight))
            {
                MessageBoxW(m_hWnd, L"Model Tree Initialization Failed!", L"Warning", MB_OK | MB_ICONWARNING);
            }

            ResizeUIElements();
            g_appCore.UpdateWindowTitle(L"OCCT Viewer - Ready");
            return 0;
        }
        case WM_COMMAND:
            switch (LOWORD(wParam))
            {
            case IDC_IMPORT_SHAPE_BUTTON:
                g_appCore.HandleImportShape();
                break;
            case IDC_CREATE_CUBE_BUTTON:
                g_appCore.HandleCreateCube();
                break;
            case IDC_CREATE_CYLINDER_BUTTON:
                g_appCore.HandleCreateCylinder();
                break;
            case IDC_CUT_BUTTON:
                g_appCore.HandleCutOperation();
                break;
            case IDC_THICK_BUTTON:
                g_appCore.HandleThickenOperation();
                break;
            case IDC_CLEAR_BUTTON:
                g_appCore.HandleClearScene();
                break;
            case IDC_EXPORT_BUTTON:
                g_appCore.HandleExportCurrentShape();
                break;
            case IDC_EXTRUDE_BUTTON:
                g_appCore.HandleExtrudeOperation();
                break;
            case IDC_FILLET_BUTTON:
                g_appCore.HandleFilletOperation();
                break;
            case IDC_CHAMFER_BUTTON:
                g_appCore.HandleChamferOperation();
                break;
            case IDC_REVOLVE_BUTTON:
                g_appCore.HandleRevolveOperation();
                break;
            case IDC_UNDO_BUTTON:
                g_appCore.HandleUndo();
                break;
            case IDC_REDO_BUTTON:
                g_appCore.HandleRedo();
                break;
            case IDC_TRIM_BUTTON:
                g_appCore.HandleTrimOperation();
                break;
            case IDC_UNTRIM_BUTTON:
                g_appCore.HandleUntrimOperation();
                break;
            case IDC_SPLIT_BUTTON:
                g_appCore.HandleSplitShapeOperation();
                break;
            case IDC_JOIN_BUTTON:
                g_appCore.HandleJoinOperation();
                break;
            case IDC_MIRROR_BUTTON:
                g_appCore.HandleMirrorOperation();
                break;
            case IDC_OFFSET_BUTTON:
                g_appCore.HandleOffsetOperation();
                break;
            case IDC_THICK_SOLID_BUTTON:
                g_appCore.HandleThickSolidOperation();
                break;
            case IDC_SWEEP_BUTTON:
                g_appCore.HandleSweepOperation();
                break;
            case IDC_TEST_SWEEP_BUTTON:
                g_appCore.HandleTestSweepOperation();
                break;
            case IDC_FILL_BUTTON:
                g_appCore.CreateFillOperation();
                break;
            case IDC_FILL_EDGES_BUTTON:
                g_appCore.CreateFillFromSelectedEdges();
                break;
            case IDC_ADVANCED_FILL_BUTTON:
                g_appCore.CreateAdvancedFillOperation();
                break;
            default:
                return DefWindowProc(m_hWnd, msg, wParam, lParam);
            }
            return 0;

        case WM_SIZE:
            ResizeUIElements();
            return 0;

        case WM_NOTIFY:
        {
            LPNMHDR pnmh = (LPNMHDR)lParam;
            g_appCore.HandleModelTreeNotify(pnmh);
            return 0;
        }

        case WM_LBUTTONDOWN:
            g_appCore.ProcessLButtonDown(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_LBUTTONUP:
            g_appCore.ProcessLButtonUp(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_MOUSEMOVE:
            g_appCore.MouseMoveHandler(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_RBUTTONDOWN:
            SetCapture(m_hWnd);
            isPanningS = true;
            g_appCore.MouseRButtonDownHandler(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_MBUTTONDOWN:
            g_appCore.MouseMButtonDownHandler(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_RBUTTONUP:
            if (isPanningS)
                ReleaseCapture();
            isPanningS = false;
            g_appCore.MouseRButtonUpHandler(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_MBUTTONUP:
            g_appCore.MouseMButtonUpHandler(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), wParam);
            return 0;
        case WM_MOUSEWHEEL:
            g_appCore.MouseWheelHandler(GET_WHEEL_DELTA_WPARAM(wParam), GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
            return 0;

        case WM_KEYDOWN:
        {
            // Check for Ctrl key combinations
            bool ctrlPressed = (GetKeyState(VK_CONTROL) & 0x8000) != 0;

            if (ctrlPressed)
            {
                switch (wParam)
                {
                case 'Z':
                    g_appCore.HandleUndo();
                    break;
                case 'Y':
                    g_appCore.HandleRedo();
                    break;
                }
            }
            else
            {
                switch (wParam)
                {
                case 'F':
                    g_appCore.FitAll();
                    break;
                case 'T':
                    g_appCore.SetViewProjection(V3d_Zpos);
                    break;
                case 'B':
                    g_appCore.SetViewProjection(V3d_Zneg);
                    break;
                case 'L':
                    g_appCore.SetViewProjection(V3d_Xneg);
                    break;
                case 'R':
                    g_appCore.SetViewProjection(V3d_Xpos);
                    break;
                case 'I':
                    g_appCore.SetViewProjection(V3d_XposYnegZpos);
                    break;
                case VK_HOME:
                    g_appCore.ResetView();
                    break;
                case VK_ESCAPE:
                    g_appCore.EndSweepSelectionMode();
                    g_appCore.ClearAllContextSelections(); // Clear all selections on ESC
                    break;
                }
            }
        }
            return 0;

        case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(m_hWnd, &ps);
            RECT clientRect;
            GetClientRect(m_hWnd, &clientRect);
            RECT buttonAreaRect = {0, 0, clientRect.right, BUTTON_AREA_TOTAL_HEIGHT};
            FillRect(hdc, &buttonAreaRect, (HBRUSH)(COLOR_BTNFACE + 1));
            EndPaint(m_hWnd, &ps);
            g_appCore.RedrawView();
            return 0;
        }
        case WM_CTLCOLORSTATIC:
            if ((HWND)lParam == m_hOccWindow)
            {
                return (LRESULT)GetStockObject(BLACK_BRUSH);
            }
            return DefWindowProc(m_hWnd, msg, wParam, lParam);

        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        }
        return DefWindowProc(m_hWnd, msg, wParam, lParam);
    }

    INT_PTR CALLBACK DlgProcRevolveParams(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
    {
        static double* pAngle = nullptr;

        switch (message)
        {
        case WM_INITDIALOG:
            {
                pAngle = (double*)lParam;

                // Initialize the spin control
                HWND hSpin = GetDlgItem(hDlg, IDC_ANGLE_SPIN);
                SendMessage(hSpin, UDM_SETRANGE, 0, MAKELPARAM(360, 1));
                SendMessage(hSpin, UDM_SETPOS, 0, (LPARAM)(int)(*pAngle));

                // Set the initial value in the edit control
                wchar_t buf[32];
                swprintf_s(buf, _countof(buf), L"%.1f", *pAngle);
                SetDlgItemTextW(hDlg, IDC_ANGLE_EDIT, buf);

                return (INT_PTR)TRUE;
            }

        case WM_COMMAND:
            if (LOWORD(wParam) == IDOK)
            {
                // Get the angle value from the edit control
                wchar_t buf[32];
                GetDlgItemTextW(hDlg, IDC_ANGLE_EDIT, buf, _countof(buf));

                // Convert to double
                *pAngle = _wtof(buf);

                // Validate the angle
                if (*pAngle <= 0 || *pAngle > 360)
                {
                    MessageBoxW(hDlg, L"Angle must be between 0 and 360 degrees", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    return (INT_PTR)TRUE;
                }

                EndDialog(hDlg, IDOK);
                return (INT_PTR)TRUE;
            }
            else if (LOWORD(wParam) == IDCANCEL)
            {
                EndDialog(hDlg, IDCANCEL);
                return (INT_PTR)TRUE;
            }
            break;

        case WM_NOTIFY:
            {
                NMHDR* pNMHDR = (NMHDR*)lParam;
                if (pNMHDR->code == UDN_DELTAPOS && pNMHDR->idFrom == IDC_ANGLE_SPIN)
                {
                    NMUPDOWN* pNMUpDown = (NMUPDOWN*)lParam;

                    // Get current value
                    wchar_t buf[32];
                    GetDlgItemTextW(hDlg, IDC_ANGLE_EDIT, buf, _countof(buf));
                    double currentValue = _wtof(buf);

                    // Calculate new value
                    double newValue = currentValue - pNMUpDown->iDelta;

                    // Clamp to valid range
                    newValue = max(1.0, min(360.0, newValue));

                    // Update edit control
                    swprintf_s(buf, _countof(buf), L"%.1f", newValue);
                    SetDlgItemTextW(hDlg, IDC_ANGLE_EDIT, buf);

                    return (INT_PTR)TRUE;
                }
            }
            break;
        }

        return (INT_PTR)FALSE;
    }

}
