#pragma once

#include <string>
#include <vector>
#include <memory>
#include <Aspect_DisplayConnection.hxx>
#include <AIS_InteractiveContext.hxx>
#include <V3d_Viewer.hxx>
#include <V3d_View.hxx>
#include <WNT_Window.hxx>
#include <TopoDS_Shape.hxx>
#include <TopoDS_Face.hxx>
#include <TopoDS_Edge.hxx>
#include <Quantity_Color.hxx>
#include <AIS_Shape.hxx>
#include <Bnd_Box.hxx>
#include <V3d_TypeOfOrientation.hxx>
#include <windows.h>
#include <commctrl.h>

// Ensure notification types are available
#ifndef LPNMHDR
typedef struct tagNMHDR* LPNMHDR;
#endif
#include <OpenGl_GraphicDriver.hxx>
#include <Quantity_NameOfColor.hxx>
#include <TopTools_ListOfShape.hxx>
#include <gp_Ax1.hxx>
#include <optional>

// Custom Windows messages
#define WM_USER_FACE_SELECTED_FOR_MIRROR (WM_USER + 1)

namespace MyCad
{
    // Forward declarations
    class UndoRedoManager;
    class ICommand;
    struct SceneState;
    class ModelTreeManager;

    struct SceneObject
    {
        TopoDS_Shape shape;
        Handle(AIS_InteractiveObject) aisObject;
        std::string name;
        Quantity_Color color;
        int id;
        static int nextId;

        SceneObject(const TopoDS_Shape &s, const Handle(AIS_InteractiveObject) & ais, std::string n, const Quantity_Color &c)
            : shape(s), aisObject(ais), name(std::move(n)), color(c), id(nextId++) {}
    };

    class AppCore
    {
    public:
        AppCore();
        ~AppCore();

        bool InitializeGraphics(HWND hOccWnd);
        void InitializeContextStyles();
        void SetWindowHandles(HWND hMain, HWND hOccChild, int buttonAreaTotalHeight);

        void HandleImportShape();
        void HandleExportCurrentShape();
        void HandleCreateCube();
        void HandleCreateCylinder();
        void HandleCutOperation();
        void HandleThickenOperation();
        void HandleOffsetFaceOperation();
        void HandleClearScene();
        void HandleExtrudeOperation();
        void HandleFilletOperation();
        void HandleChamferOperation();
        void HandleRevolveOperation();
        gp_Ax1 GetRevolveAxisFromUser();
        TopTools_ListOfShape GetSelectedEdges(const Handle(AIS_InteractiveObject)& parentAisIO_filter);

        // Trim and Untrim Operations
        void HandleTrimOperation();
        void HandleUntrimOperation();
        void HandleTrimSurfaceWithCurve();
        void HandleTrimSurfaceWithSurface();
        void HandleTrimCurveWithPoints();
        void HandleTrimShapeWithPlane();
        void HandleSplitShapeOperation();
        void HandleJoinOperation();

        // FreeCAD-Style Offset Operations
        void HandleOffsetOperation();
        void HandleOffsetDialog();
        void HandleThickSolidOperation();
        void HandlePipeOperation();
        void HandleMirrorOperation();
        void HandleSweepOperation();
        void HandleTestSweepOperation();
        void StartSweepSelectionMode();
        void EndSweepSelectionMode();
        void HandleSweepModeClick(int x, int y);

        // Enhanced Interactive Trimming Operations
        void HandleInteractiveCurveTrim();
        void HandleMultiSegmentTrim();
        void HandleTrimAtIntersection();
        void HandleAdvancedTrimDialog();

        // Trim Mode and State Management
        enum TrimMode {
            TRIM_MODE_NORMAL = 0,
            TRIM_MODE_INTERACTIVE_CURVE = 1,
            TRIM_MODE_MULTI_SEGMENT = 2,
            TRIM_MODE_AT_INTERSECTION = 3
        };

        void SetTrimMode(TrimMode mode);
        void HandleTrimModeClick(int x, int y);
        void ShowTrimPreview();
        void ClearTrimPreview();

        // Interactive Split Operations
        void HandleInteractiveSplit();
        void StartSplitMode();
        void EndSplitMode();
        void HandleSplitModeClick(int x, int y);
        void ShowSplitLinePreview();
        void ClearSplitPreview();
        void ExecuteSplit();

        // Mouse drag handling for split line drawing
        void HandleSplitMouseDown(int x, int y);
        void HandleSplitMouseMove(int x, int y);
        void HandleSplitMouseUp(int x, int y);
        void UpdateSplitLinePreview(int startX, int startY, int endX, int endY);
        gp_Pnt ScreenToWorld(int screenX, int screenY);

        // Professional CAD-style rubber band line (like AutoCAD/SolidWorks)
        void CreateRubberBandLine(int startX, int startY, int endX, int endY);
        void UpdateRubberBandLine(int endX, int endY);
        void ClearRubberBandLine();
        gp_Pnt ScreenToWorldPrecise(int screenX, int screenY);
        bool FindRayShapeIntersection(const gp_Lin& ray, const TopoDS_Shape& shape, gp_Pnt& intersectionPoint);
        bool Get3DPointFromPicking(int screenX, int screenY, gp_Pnt& point3D);
        bool ProjectCursorOntoShape(int screenX, int screenY, const TopoDS_Shape& shape, gp_Pnt& projectedPoint);
        bool FindRayShapeIntersectionPrecise(const gp_Lin& ray, const TopoDS_Shape& shape, gp_Pnt& intersectionPoint);

        // Mirror dialog and face selection
        bool ShowMirrorDialog(int& mirrorPlaneType, bool& keepOriginal, TopoDS_Face& selectedFace);
        static INT_PTR CALLBACK MirrorDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);

        // Offset dialog and parameters
        bool ShowOffsetDialog(double& offsetValue, int& offsetDirection, int& offsetMode, int& joinType, bool& keepOriginal);
        static INT_PTR CALLBACK OffsetDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);
        void HandleSelectMirrorFace();

        // Mirror plane types
        enum MirrorPlaneType {
            MIRROR_XY_PLANE = 0,
            MIRROR_XZ_PLANE = 1,
            MIRROR_YZ_PLANE = 2,
            MIRROR_CUSTOM_FACE = 3
        };

        void ProcessLButtonDown(int x_main, int y_main, UINT flags);
        void ProcessLButtonUp(int x_main, int y_main, UINT flags);
        void ClearAllContextSelections();
        Handle(AIS_InteractiveObject) GetAisObjectById(int id);

        // Undo/Redo functionality
        void HandleUndo();
        void HandleRedo();
        bool CanUndo() const;
        bool CanRedo() const;
        std::string GetUndoDescription() const;
        std::string GetRedoDescription() const;

        void RedrawView();
        void FitAll();
        void SetViewProjection(V3d_TypeOfOrientation aOrientation);
        void ResetView();
        void MustBeResized();
        void SetViewBackgroundColor(const Quantity_Color &color);
        void UpdateWindowTitle(const std::wstring &newTitle);

        void MouseMoveHandler(int x_main, int y_main, UINT flags);
        void MouseRButtonDownHandler(int x_main, int y_main, UINT flags);
        void MouseMButtonDownHandler(int x_main, int y_main, UINT flags);
        void MouseRButtonUpHandler(int x_main, int y_main, UINT flags);
        void MouseMButtonUpHandler(int x_main, int y_main, UINT flags);
        void MouseWheelHandler(short zDelta, int x_main, int y_main);

        const Handle(AIS_InteractiveContext) & GetInteractiveContext() const { return m_context; }
        const Handle(V3d_View) & GetView() const { return m_view; }
        Bnd_Box GetSceneBoundingBox() const;
        const TopTools_ListOfShape &GetSelectedFaces() const { return m_selectedFacesList; }

        // Undo/Redo helper methods (public for command access)
        struct ObjectData
        {
            TopoDS_Shape shape;
            std::string name;
            Quantity_Color color;
        };

        int AddObjectToSceneWithId(const TopoDS_Shape &shape, const Quantity_Color &color, const std::string &name, bool fitView = true, int forceId = -1);
        std::optional<ObjectData> GetObjectData(int objectId) const;
        void CaptureSceneState(SceneState& state) const;
        void RestoreSceneState(const SceneState& state);
        void ExecuteCommand(std::unique_ptr<ICommand> command);
        void RemoveObjectFromScene(int objectId);
        void ClearSceneInternal(); // For command system use

        // Model Tree Management
        bool InitializeModelTree(HWND parentWindow, int x, int y, int width, int height);
        void UpdateModelTree();
        void ResizeModelTree(int x, int y, int width, int height);
        void ShowModelTree(bool show);
        ModelTreeManager* GetModelTreeManager() const { return m_modelTreeManager.get(); }

        // Model tree event handlers
        void HandleModelTreeNotify(LPNMHDR pnmh);
        void OnTreeObjectSelected(int objectId);
        void OnTreeObjectVisibilityChanged(int objectId, bool visible);
        void OnTreeObjectDeleted(int objectId);
        void OnTreeObjectRenamed(int objectId, const std::string& newName);

        // Fill operations - Create smooth surfaces from closed boundaries
        void CreateFillOperation();
        void CreateFillFromSelectedEdges();
        void CreateAdvancedFillOperation();
        void HandleFillEdgeSelection(int x, int y);
        void HandleFillSelectionComplete();
        void ConfigureSelectionForEdges();
        void RestoreNormalSelection();
        bool FindEdgeUnderCursor(int x, int y, TopoDS_Edge& foundEdge, Handle(AIS_Shape)& parentShape);
        void ClearEdgeHighlights();
        void EnhanceEdgeVisibility(Handle(AIS_Shape) aisShape);
        void DebugListEdgesInShape(const TopoDS_Shape &shape);

        // Manual edge hover detection functions
        void HandleEdgeHover(int mouseX, int mouseY);
        double CalculateMouseToEdgeDistance(int mouseX, int mouseY, const TopoDS_Edge& edge);
        void CreateHoverHighlight(const TopoDS_Edge& edge);
        void ClearHoverHighlight();

        // Edge selection for operations
        TopTools_ListOfShape GetSelectedEdges(const Handle(AIS_InteractiveObject)& parentObject) const;

    private:
        void AddObjectToScene(const TopoDS_Shape &shape, const Quantity_Color &color = Quantity_NameOfColor::Quantity_NOC_WHITE, const std::string &name = "Object", bool fitView = true);
        void SetupAisObjectDisplay(const Handle(AIS_Shape) & aisShape,
                                   const Quantity_Color &mainColor,
                                   const Quantity_Color &edgeColor = Quantity_NameOfColor::Quantity_NOC_BLACK,
                                   Standard_Real edgeWidth = 1.0);
        bool MapToOccViewCoords(int mainX, int mainY, int &occX, int &occY);
        void UpdateCurrentShapeForExport(const TopoDS_Shape &shape);

        Handle(Aspect_DisplayConnection) m_displayConnection;
        Handle(OpenGl_GraphicDriver) m_graphicDriver;
        Handle(V3d_Viewer) m_viewer;
        Handle(V3d_View) m_view;
        Handle(WNT_Window) m_wntWindow;
        Handle(AIS_InteractiveContext) m_context;

        std::vector<SceneObject> m_sceneObjects;
        TopoDS_Shape m_currentShapeToExport;

        // Legacy two-object selection (kept for backward compatibility)
        Handle(AIS_InteractiveObject) m_selection1_IO;
        Handle(AIS_InteractiveObject) m_selection2_IO;

        // Enhanced multiple selection system
        std::vector<Handle(AIS_InteractiveObject)> m_selectedObjects;
        TopTools_ListOfShape m_selectedFacesList;
        Handle(AIS_InteractiveObject) m_parentShapeOfSelectedFaces;

        // Selection management helpers
        void AddToSelection(const Handle(AIS_InteractiveObject)& object);
        void RemoveFromSelection(const Handle(AIS_InteractiveObject)& object);
        bool IsSelected(const Handle(AIS_InteractiveObject)& object) const;
        void ClearObjectSelection();
        size_t GetSelectedObjectCount() const { return m_selectedObjects.size(); }
        const std::vector<Handle(AIS_InteractiveObject)>& GetSelectedObjects() const { return m_selectedObjects; }

        Standard_Integer m_lastMouseX_occ, m_lastMouseY_occ;
        Standard_Boolean m_isPanning;

        HWND m_hMainWnd;
        HWND m_hOccChildWnd;
        int m_buttonAreaHeight;

        double m_currentPlacementXOffset;
        static const double PLACEMENT_SPACING;

        // Undo/Redo system
        std::unique_ptr<UndoRedoManager> m_undoRedoManager;

        // Model Tree system
        std::unique_ptr<ModelTreeManager> m_modelTreeManager;

        bool m_selectingRevolveAxis = false;
        Handle(AIS_Shape) m_tempAxisDisplay;

        // Mirror dialog state
        bool m_isSelectingMirrorFace = false;
        TopoDS_Face m_selectedMirrorFace;
        HWND m_hMirrorDialog = nullptr;

        // Enhanced Trim state management
        TrimMode m_currentTrimMode = TRIM_MODE_NORMAL;
        bool m_isInTrimMode = false;
        std::vector<gp_Pnt> m_trimPoints;
        std::vector<TopoDS_Edge> m_selectedCurves;
        Handle(AIS_InteractiveObject) m_trimPreviewObject;
        TopTools_ListOfShape m_trimPreviewShapes;
        bool m_showTrimPreview = false;

        // Interactive Split state management
        bool m_isInSplitMode = false;
        TopoDS_Shape m_shapeToSplit;
        gp_Pnt m_splitStartPoint;
        gp_Pnt m_splitEndPoint;
        bool m_isDrawingSplitLine = false;
        bool m_hasSplitStartPoint = false;
        bool m_hasSplitEndPoint = false;
        Handle(AIS_InteractiveObject) m_splitLinePreview;

        // Mouse drag state for split line drawing
        bool m_isDraggingSplitLine = false;
        int m_dragStartX = 0;
        int m_dragStartY = 0;
        int m_dragCurrentX = 0;
        int m_dragCurrentY = 0;

        // Professional CAD-style rubber band line
        bool m_useRubberBandLine = true;
        Handle(AIS_InteractiveObject) m_rubberBandLine;

        // Sweep selection mode variables
        bool m_isInSweepSelectionMode = false;
        TopoDS_Shape m_sweepProfile;
        TopoDS_Shape m_sweepPath;
        bool m_hasSweepProfile = false;
        bool m_hasSweepPath = false;

        // Fill operation state management
        bool m_isInFillSelectionMode = false;
        TopTools_ListOfShape m_selectedEdgesForFill;
        std::vector<Handle(AIS_Shape)> m_edgeHighlights;
        int m_fillDegree = 3;
        int m_fillMaxSegments = 9;
        bool m_fillUseG1Continuity = true;
        bool m_fillUseG2Continuity = false;

        // Manual edge hover detection
        Handle(AIS_Shape) m_hoverHighlight;
        TopoDS_Edge m_hoveredEdge;
        Handle(AIS_Shape) m_hoveredParentShape;
    };

}
