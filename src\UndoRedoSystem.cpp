#include "MyCad/UndoRedoSystem.h"
#include "MyCad/AppCore.h"
#include <algorithm>
#include <functional>

namespace MyCad
{
    // SceneCommand Implementation
    SceneCommand::SceneCommand(AppCore* appCore, const std::string& description)
        : m_appCore(appCore), m_description(description), m_executed(false)
    {
    }

    void SceneCommand::Execute()
    {
        if (!m_executed && m_operation)
        {
            m_operation();
            m_executed = true;
        }
    }

    void SceneCommand::Undo()
    {
        if (m_executed && m_beforeState && m_appCore)
        {
            m_appCore->RestoreSceneState(*m_beforeState);
            m_executed = false;
        }
    }

    void SceneCommand::CaptureBeforeState()
    {
        if (m_appCore)
        {
            m_beforeState = std::make_unique<SceneState>(m_description + " - Before");
            m_appCore->CaptureSceneState(*m_beforeState);
        }
    }

    void SceneCommand::CaptureAfterState()
    {
        if (m_appCore)
        {
            m_afterState = std::make_unique<SceneState>(m_description + " - After");
            m_appCore->CaptureSceneState(*m_afterState);
        }
    }

    void SceneCommand::SetOperation(std::function<void()> operation)
    {
        m_operation = operation;
    }

    // AddObjectCommand Implementation
    AddObjectCommand::AddObjectCommand(AppCore* appCore, const TopoDS_Shape& shape,
                                     const Quantity_Color& color, const std::string& name, bool fitView)
        : m_appCore(appCore), m_shape(shape), m_color(color), m_name(name),
          m_fitView(fitView), m_objectId(-1), m_executed(false)
    {
    }

    void AddObjectCommand::Execute()
    {
        if (!m_executed && m_appCore)
        {
            m_objectId = m_appCore->AddObjectToSceneWithId(m_shape, m_color, m_name, m_fitView);
            m_executed = true;
        }
    }

    void AddObjectCommand::Undo()
    {
        if (m_executed && m_objectId != -1 && m_appCore)
        {
            m_appCore->RemoveObjectFromScene(m_objectId);
            m_executed = false;
        }
    }

    std::string AddObjectCommand::GetDescription() const
    {
        return "Add " + m_name;
    }

    // RemoveObjectCommand Implementation
    RemoveObjectCommand::RemoveObjectCommand(AppCore* appCore, int objectId)
        : m_appCore(appCore), m_objectId(objectId), m_executed(false)
    {
        // Capture object data before removal
        if (m_appCore)
        {
            auto objData = m_appCore->GetObjectData(objectId);
            if (objData.has_value())
            {
                m_shape = objData->shape;
                m_color = objData->color;
                m_name = objData->name;
            }
        }
    }

    void RemoveObjectCommand::Execute()
    {
        if (!m_executed && m_appCore)
        {
            m_appCore->RemoveObjectFromScene(m_objectId);
            m_executed = true;
        }
    }

    void RemoveObjectCommand::Undo()
    {
        if (m_executed && m_appCore && !m_shape.IsNull())
        {
            m_appCore->AddObjectToSceneWithId(m_shape, m_color, m_name, false, m_objectId);
            m_executed = false;
        }
    }

    std::string RemoveObjectCommand::GetDescription() const
    {
        return "Remove " + m_name;
    }

    // ClearSceneCommand Implementation
    ClearSceneCommand::ClearSceneCommand(AppCore* appCore)
        : m_appCore(appCore), m_executed(false)
    {
        // Capture current scene state
        if (m_appCore)
        {
            m_previousState = std::make_unique<SceneState>("Clear Scene - Previous State");
            m_appCore->CaptureSceneState(*m_previousState);
        }
    }

    void ClearSceneCommand::Execute()
    {
        if (!m_executed && m_appCore)
        {
            m_appCore->ClearSceneInternal();
            m_executed = true;
        }
    }

    void ClearSceneCommand::Undo()
    {
        if (m_executed && m_previousState && m_appCore)
        {
            m_appCore->RestoreSceneState(*m_previousState);
            m_executed = false;
        }
    }

    std::string ClearSceneCommand::GetDescription() const
    {
        return "Clear Scene";
    }

    // UndoRedoManager Implementation
    UndoRedoManager::UndoRedoManager(size_t maxHistorySize)
        : m_maxHistorySize(maxHistorySize)
    {
    }

    void UndoRedoManager::ExecuteCommand(std::unique_ptr<ICommand> command)
    {
        if (!command) return;

        // Execute the command
        command->Execute();

        // Add to undo stack
        m_undoStack.push_back(std::move(command));

        // Clear redo stack (new command invalidates redo history)
        m_redoStack.clear();

        // Trim history if needed
        TrimHistory();
    }

    bool UndoRedoManager::CanUndo() const
    {
        return !m_undoStack.empty();
    }

    bool UndoRedoManager::CanRedo() const
    {
        return !m_redoStack.empty();
    }

    void UndoRedoManager::Undo()
    {
        if (!CanUndo()) return;

        // Get the last command
        auto command = std::move(m_undoStack.back());
        m_undoStack.pop_back();

        // Undo the command
        command->Undo();

        // Move to redo stack
        m_redoStack.push_back(std::move(command));
    }

    void UndoRedoManager::Redo()
    {
        if (!CanRedo()) return;

        // Get the last undone command
        auto command = std::move(m_redoStack.back());
        m_redoStack.pop_back();

        // Re-execute the command
        command->Execute();

        // Move back to undo stack
        m_undoStack.push_back(std::move(command));
    }

    void UndoRedoManager::Clear()
    {
        m_undoStack.clear();
        m_redoStack.clear();
    }

    std::string UndoRedoManager::GetUndoDescription() const
    {
        if (CanUndo())
        {
            return "Undo " + m_undoStack.back()->GetDescription();
        }
        return "Undo";
    }

    std::string UndoRedoManager::GetRedoDescription() const
    {
        if (CanRedo())
        {
            return "Redo " + m_redoStack.back()->GetDescription();
        }
        return "Redo";
    }

    void UndoRedoManager::TrimHistory()
    {
        while (m_undoStack.size() > m_maxHistorySize)
        {
            m_undoStack.erase(m_undoStack.begin());
        }
    }

} // namespace MyCad
