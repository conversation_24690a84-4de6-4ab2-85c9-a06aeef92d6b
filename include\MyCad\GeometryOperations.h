#pragma once

#include <TopoDS_Shape.hxx>
#include <TopoDS_Face.hxx>
#include <TopoDS_Solid.hxx>
#include <TopoDS_Edge.hxx>
#include <TopoDS_Wire.hxx>
#include <gp_Pnt.hxx>
#include <gp_Ax2.hxx>
#include <gp_Ax1.hxx>
#include <gp_Dir.hxx>
#include <gp_Pln.hxx>
#include <Geom_Surface.hxx>
#include <string>
#include <windows.h>
#include <TopTools_ListOfShape.hxx>

namespace MyCadGeom
{

    TopoDS_Shape CreateCube(double size = 50.0);
    TopoDS_Shape CreateCylinder(const gp_Ax2 &axis, double radius = 20.0, double height = 80.0);
    TopoDS_Shape CutShapes(const TopoDS_Shape &objectShape, const TopoDS_Shape &toolShape);
    TopoDS_Shape <PERSON>hapes(const TopoDS_Shape &shape1, const TopoDS_Shape &shape2);
    TopoDS_Shape Thicken<PERSON>hape(const TopoDS_Shape &shape, double thickness = -2.0, const TopTools_ListOfShape &facesToRemove = TopTools_ListOfShape());
    // TopoDS_Shape OffsetFace(const TopoDS_Solid& parentSolid, const TopoDS_Face& selectedFace, double thickness); // This was used by the old sequential method, can be removed if not used elsewhere or kept
    gp_Dir GetCorrectedFaceNormal(const TopoDS_Solid &solid, const TopoDS_Face &face); // **** YOU NEED TO IMPLEMENT THIS ****
    TopoDS_Shape TranslateShape(const TopoDS_Shape &shape, double offsetX, double offsetY, double offsetZ);

    TopoDS_Shape ImportStepIgesFile(HWND hwndParent, const std::string &filePath);
    bool ExportStepIgesFile(HWND hwndParent, const TopoDS_Shape &shape, const std::string &filePath);
    TopoDS_Shape ExtrudeFace(const TopoDS_Solid &solid, const TopoDS_Face &faceToExtrude, double distance, const gp_Dir &direction = gp_Dir());
    TopoDS_Shape FilletEdges(const TopoDS_Shape &shape, const TopTools_ListOfShape &edges, double radius);
    TopoDS_Shape ChamferEdges(const TopoDS_Shape &shape, const TopTools_ListOfShape &edges, double distance);
    TopoDS_Shape RevolveShape(const TopoDS_Shape& profileShape, const gp_Ax1& axis, double angle = 360.0);
    gp_Ax1 ExtractAxisFromEdge(const TopoDS_Edge& edge);

    // Trim and Untrim Operations
    TopoDS_Shape TrimSurfaceWithCurve(const TopoDS_Face& surface, const TopoDS_Wire& trimmingWire, bool keepInside = true);
    TopoDS_Shape TrimSurfaceWithSurface(const TopoDS_Face& surfaceToTrim, const TopoDS_Face& trimmingSurface);
    TopoDS_Shape TrimCurveWithPoints(const TopoDS_Edge& curve, const gp_Pnt& startPoint, const gp_Pnt& endPoint);
    TopoDS_Shape TrimShapeWithPlane(const TopoDS_Shape& shape, const gp_Pln& plane, bool keepPositiveSide = true);
    TopoDS_Shape TrimFaceWithWire(const TopoDS_Face& face, const TopoDS_Wire& wire, bool removeInside = true);

    // Enhanced 2D Curve Trimming Operations
    TopTools_ListOfShape TrimCurvesAtIntersection(const TopoDS_Edge& curve1, const TopoDS_Edge& curve2, const gp_Pnt& intersectionPoint, bool keepBothSegments = false);
    TopTools_ListOfShape FindCurveIntersections(const TopoDS_Edge& curve1, const TopoDS_Edge& curve2);
    TopoDS_Shape TrimCurveAtParameter(const TopoDS_Edge& curve, Standard_Real parameter, bool keepStartSegment = true);
    TopTools_ListOfShape TrimCurveMultipleSegments(const TopoDS_Edge& curve, const std::vector<Standard_Real>& parameters);
    TopoDS_Shape TrimCurveWithCurve(const TopoDS_Edge& curveToTrim, const TopoDS_Edge& trimmingCurve, bool keepInside = true);

    // Enhanced 3D Surface/Solid Trimming Operations
    TopoDS_Shape TrimSolidWithWire(const TopoDS_Solid& solid, const TopoDS_Wire& cuttingWire, const gp_Dir& direction);
    TopoDS_Shape TrimFaceWithEdge(const TopoDS_Face& face, const TopoDS_Edge& cuttingEdge);
    TopTools_ListOfShape SplitFaceWithWire(const TopoDS_Face& face, const TopoDS_Wire& splittingWire);

    // Untrim Operations
    TopoDS_Shape UntrimSurface(const TopoDS_Face& trimmedSurface);
    TopoDS_Shape UntrimCurve(const TopoDS_Edge& trimmedCurve);
    TopoDS_Shape UntrimCurveToFullExtent(const TopoDS_Edge& trimmedCurve);
    TopTools_ListOfShape UntrimMultipleCurves(const TopTools_ListOfShape& trimmedCurves);

    // Advanced Trimming Operations
    TopoDS_Shape TrimShapeWithShape(const TopoDS_Shape& shapeToTrim, const TopoDS_Shape& trimmingTool, bool keepInside = true);
    TopTools_ListOfShape SplitShapeWithShape(const TopoDS_Shape& shapeToSplit, const TopoDS_Shape& splittingTool);
    TopoDS_Shape CreateTrimmedSurface(const Handle(Geom_Surface)& surface, const TopoDS_Wire& outerBoundary, const TopTools_ListOfShape& innerBoundaries = TopTools_ListOfShape());

    // Join Operations
    TopoDS_Shape JoinShapes(const TopTools_ListOfShape& shapes);
    TopoDS_Shape JoinTwoShapes(const TopoDS_Shape& shape1, const TopoDS_Shape& shape2);

    // Intersection and Analysis Utilities for Trimming
    std::vector<gp_Pnt> FindCurveCurveIntersections(const TopoDS_Edge& curve1, const TopoDS_Edge& curve2);
    std::vector<Standard_Real> FindCurveParametersAtPoints(const TopoDS_Edge& curve, const std::vector<gp_Pnt>& points);
    bool IsPointOnCurve(const TopoDS_Edge& curve, const gp_Pnt& point, Standard_Real tolerance = Precision::Confusion());
    Standard_Real GetCurveParameterAtPoint(const TopoDS_Edge& curve, const gp_Pnt& point);
    gp_Pnt GetCurvePointAtParameter(const TopoDS_Edge& curve, Standard_Real parameter);

    // Visual Feedback and Preview Utilities
    TopoDS_Shape CreatePreviewTrimResult(const TopoDS_Edge& curve, const gp_Pnt& startPoint, const gp_Pnt& endPoint);
    TopTools_ListOfShape CreatePreviewMultiTrimResult(const TopoDS_Edge& curve, const std::vector<gp_Pnt>& trimPoints);
    TopoDS_Shape HighlightCurveSegment(const TopoDS_Edge& curve, Standard_Real startParam, Standard_Real endParam);

    // Simple Trim Utility Functions
    gp_Pnt GetShapeCenter(const TopoDS_Shape& shape);
    TopoDS_Shape TrimFaceToHalf(const TopoDS_Face& face);
    TopoDS_Shape TrimCurveToHalf(const TopoDS_Edge& edge);

    // FreeCAD-Style Offset Operations
    TopoDS_Shape OffsetShape(const TopoDS_Shape& shape, Standard_Real offsetValue, Standard_Boolean makeThick = Standard_False);
    TopoDS_Shape OffsetFace(const TopoDS_Face& face, Standard_Real offsetValue);
    TopoDS_Shape OffsetWire(const TopoDS_Wire& wire, Standard_Real offsetValue, const gp_Pln& plane = gp_Pln());
    TopoDS_Shape OffsetEdge(const TopoDS_Edge& edge, Standard_Real offsetValue, const gp_Pln& plane = gp_Pln());
    TopoDS_Shape OffsetSolid(const TopoDS_Shape& solid, Standard_Real offsetValue, const TopTools_ListOfShape& facesToRemove = TopTools_ListOfShape());

    // Advanced Offset Operations (like FreeCAD)
    TopoDS_Shape CreateThickSolid(const TopoDS_Shape& solid, const TopTools_ListOfShape& facesToRemove, Standard_Real thickness, Standard_Real tolerance = 1e-3);
    TopoDS_Shape CreatePipe(const TopoDS_Wire& spine, const TopoDS_Shape& profile);
    TopTools_ListOfShape OffsetMultipleFaces(const TopoDS_Shape& shape, const std::vector<std::pair<TopoDS_Face, Standard_Real>>& faceOffsets);

    // Sweep Operations (FreeCAD-Style)
    TopoDS_Shape SweepProfileAlongPath(const TopoDS_Shape& profile, const TopoDS_Shape& path);
    bool ValidateProfileForSweep(const TopoDS_Shape& profile);
    bool ValidatePathForSweep(const TopoDS_Shape& path);
    TopoDS_Shape CreateSweepPreview(const TopoDS_Shape& profile, const TopoDS_Shape& path);

    // Helper functions for creating sweep-compatible shapes
    TopoDS_Wire CreateCircularProfile(double radius = 5.0, const gp_Pnt& center = gp_Pnt(0, 0, 0));
    TopoDS_Wire CreateRectangularProfile(double width = 10.0, double height = 6.0, const gp_Pnt& center = gp_Pnt(0, 0, 0));
    TopoDS_Wire CreateStraightPath(const gp_Pnt& start, const gp_Pnt& end);
    TopoDS_Wire CreateCurvedPath(); // Creates a multi-segment curved path like in your working code

    // Fill Operations - Create smooth surfaces from closed boundaries
    TopoDS_Face CreateFilledSurface(const TopoDS_Wire& boundary, int degree = 3, int maxSegments = 9);
    TopoDS_Face CreateFilledSurface(const TopTools_ListOfShape& edges, int degree = 3, int maxSegments = 9);
    TopoDS_Wire CreateWireFromEdges(const TopTools_ListOfShape& edges);
    bool ValidateBoundaryForFill(const TopoDS_Wire& boundary);
    bool ValidateEdgesForFill(const TopTools_ListOfShape& edges);
    bool IsWireClosed(const TopoDS_Wire& wire);
    TopoDS_Face CreateAdvancedFilledSurface(const TopoDS_Wire& boundary,
                                           int degree = 3,
                                           int maxSegments = 9,
                                           bool useG1Continuity = true,
                                           bool useG2Continuity = false);

    // Fill validation and utility functions
    Standard_Real CalculateWireArea(const TopoDS_Wire& wire);
    gp_Pnt CalculateWireCentroid(const TopoDS_Wire& wire);
    bool CheckWireOrientation(const TopoDS_Wire& wire);
    TopoDS_Wire FixWireOrientation(const TopoDS_Wire& wire);

    // FreeCAD-Compatible Offset Utilities
    enum OffsetMode {
        OFFSET_SKIN = 0,        // "Skin" - Surface offset (like FreeCAD)
        OFFSET_PIPE = 1,        // "Pipe" - Pipe/tube creation (like FreeCAD)
        OFFSET_RECTOVERSO = 2   // "RectoVerso" - Both directions (like FreeCAD)
    };

    enum JoinType {
        JOIN_ARC = 0,           // "Arc" - Rounded joins (like FreeCAD)
        JOIN_TANGENT = 1,       // "Tangent" - Tangent joins (like FreeCAD)
        JOIN_INTERSECTION = 2   // "Intersection" - Sharp joins (like FreeCAD)
    };

    // FreeCAD-Compatible Offset Function (matches FreeCAD's makeElementOffset)
    TopoDS_Shape PerformFreeCADOffset(const TopoDS_Shape& shape,
                                      Standard_Real offsetValue,
                                      OffsetMode mode = OFFSET_SKIN,
                                      JoinType join = JOIN_ARC,
                                      Standard_Boolean intersection = Standard_False,
                                      Standard_Boolean selfIntersection = Standard_False,
                                      Standard_Boolean fill = Standard_False,
                                      Standard_Real tolerance = 1e-3);

    TopoDS_Shape PerformOffset(const TopoDS_Shape& shape, Standard_Real offsetValue, OffsetMode mode, const TopTools_ListOfShape& facesToRemove = TopTools_ListOfShape());

    // Sharp Edge Offset Functions - for preserving truly sharp edges
    TopoDS_Shape PerformSharpEdgeOffset(const TopoDS_Shape& shape, Standard_Real offsetValue, Standard_Real tolerance = 1e-3);
    bool IsBoxLikeShape(const TopoDS_Shape& shape);
    TopoDS_Shape ScaleShapeUniformly(const TopoDS_Shape& shape, Standard_Real offsetValue);
    TopoDS_Shape PerformFaceByFaceOffset(const TopoDS_Shape& shape, Standard_Real offsetValue, Standard_Real tolerance = 1e-3);

    // Interactive Split Operations
    TopTools_ListOfShape SplitSolidWithLine(const TopoDS_Shape& solid, const gp_Pnt& startPoint, const gp_Pnt& endPoint);
    TopTools_ListOfShape SplitSolidWithPlane(const TopoDS_Shape& solid, const gp_Pln& splittingPlane);
    TopoDS_Wire CreateSplitLineWire(const gp_Pnt& startPoint, const gp_Pnt& endPoint);
    gp_Pln CreatePlaneFromLine(const gp_Pnt& startPoint, const gp_Pnt& endPoint, const gp_Dir& normal = gp_Dir(0, 0, 1));
    gp_Pln CreateDirectionalPlaneFromLine(const TopoDS_Shape& solid, const gp_Pnt& startPoint, const gp_Pnt& endPoint);

    // Mirror Operations
    TopoDS_Shape MirrorShape(const TopoDS_Shape& shape, const gp_Pln& mirrorPlane);
    TopoDS_Shape MirrorShapeWithSideBySidePlacement(const TopoDS_Shape& shape, const gp_Pln& mirrorPlane, Standard_Real spacing = 5.0);
    TopoDS_Shape MirrorShapeAcrossXYPlane(const TopoDS_Shape& shape);
    TopoDS_Shape MirrorShapeAcrossXZPlane(const TopoDS_Shape& shape);
    TopoDS_Shape MirrorShapeAcrossYZPlane(const TopoDS_Shape& shape);
    TopoDS_Shape MirrorShapeAcrossAxis(const TopoDS_Shape& shape, const gp_Ax1& axis);
    TopoDS_Shape MirrorShapeAcrossFace(const TopoDS_Shape& shape, const TopoDS_Face& mirrorFace);
    gp_Pln ExtractPlaneFromFace(const TopoDS_Face& face);

    // Enhanced mirror functions for side-by-side placement
    TopoDS_Shape MirrorShapeAcrossXYPlaneWithSpacing(const TopoDS_Shape& shape, Standard_Real spacing = 5.0);
    TopoDS_Shape MirrorShapeAcrossXZPlaneWithSpacing(const TopoDS_Shape& shape, Standard_Real spacing = 5.0);
    TopoDS_Shape MirrorShapeAcrossYZPlaneWithSpacing(const TopoDS_Shape& shape, Standard_Real spacing = 5.0);
    TopoDS_Shape MirrorShapeAcrossFaceWithSpacing(const TopoDS_Shape& shape, const TopoDS_Face& mirrorFace, Standard_Real spacing = 5.0);

    // Utility functions for trimming
    TopoDS_Wire ProjectWireOnSurface(const TopoDS_Wire& wire, const TopoDS_Face& surface);
    bool IsPointInsideWire(const gp_Pnt& point, const TopoDS_Wire& wire, const TopoDS_Face& face);
    TopoDS_Wire CreateRectangularWire(const gp_Pnt& corner1, const gp_Pnt& corner2, const gp_Dir& normal);

}
